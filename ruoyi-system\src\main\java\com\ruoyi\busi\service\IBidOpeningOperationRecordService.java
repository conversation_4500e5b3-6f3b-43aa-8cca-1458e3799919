package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BidOpeningOperationRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;

/**
 * 开标操作记录Service接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IBidOpeningOperationRecordService extends IService<BidOpeningOperationRecord> {
    /**
     * 查询开标操作记录列表
     *
     * @param bidOpeningOperationRecord 开标操作记录
     * @return 开标操作记录集合
     */
    public List<BidOpeningOperationRecord> selectList(BidOpeningOperationRecord bidOpeningOperationRecord);

    public AjaxResult getProjectStatus(BidOpeningOperationRecord bidOpeningOperationRecord, LoginUser loginUser);

    AjaxResult saveRecord(BidOpeningOperationRecord bidOpeningOperationRecord);

    AjaxResult getSignInCount(Long projectId);
}
