package com.ruoyi.busi.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.busi.domain.BusiAttachment;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 附件Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiAttachmentService extends IService<BusiAttachment> {
    /**
     * 查询附件列表
     *
     * @param busiAttachment 附件
     * @return 附件集合
     */
    public List<BusiAttachment> selectList(BusiAttachment busiAttachment);

    /**
     * 查询附件列表
     *
     * @param attachmentIds 附件ids
     * @return 附件集合
     */
    public List<BusiAttachment> getByIds(List<Long> attachmentIds);

    /**
     * 根据业务id查询附件列表
     *
     * @param busiId 业务id
     * @return 附件集合
     */
    public List<BusiAttachment> selectByBusiId(Long busiId, String fileType);
    public List<BusiAttachment> selectByBusiId(Long busiId);
    public boolean deleteByBusiId(Long busiId);
    public boolean deleteByBusiIdAndType(Long busiId, String fileType);

    List<BusiAttachment> getByBusiId(Long projectId);


      Map<Long,List<BusiAttachment>> getByBusiIds(List<Long> ids);
}
