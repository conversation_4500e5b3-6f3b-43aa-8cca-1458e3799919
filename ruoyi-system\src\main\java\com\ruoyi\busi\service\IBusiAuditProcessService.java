package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.BusiAuditProcess;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;

import java.util.List;

/**
 * 业务审核流程Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IBusiAuditProcessService extends IService<BusiAuditProcess> {
    List<BusiAuditProcess> selectList(BusiAuditProcess busiAuditProcess);
    List<BusiAuditProcess> selectList(Long busiId);
    BusiAuditProcess selectLast(Long busiId);

    boolean checkAuditRole(Long busiId);

    boolean saveInfo(Long busiId, AuditBusiTypeEnum busiType, Integer auditResult, String auditRemark, Integer busiState);

    boolean saveInfo(Long busiId, AuditBusiTypeEnum busiType, Integer auditResult, String auditResultName, String auditRemark, Integer busiState);
}