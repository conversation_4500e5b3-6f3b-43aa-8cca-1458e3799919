package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiBidEvaluation;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 评标记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IBusiBidEvaluationService extends IService<BusiBidEvaluation> {
    /**
     * 查询评标记录列表
     *
     * @param busiBidEvaluation 评标记录
     * @return 评标记录集合
     */
    public List<BusiBidEvaluation> selectList(BusiBidEvaluation busiBidEvaluation);

    public AjaxResult saveBatchBusiBidderInfo(BusiBidEvaluation busiBidEvaluation);

    public AjaxResult removeBusiBidderInfo(Long[] bidEvaluationIds);


    public BusiBidEvaluation selectByProject(Long projectId);
}
