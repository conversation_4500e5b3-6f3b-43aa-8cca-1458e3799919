package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiBidOpening;
import com.ruoyi.busi.domain.BusiTenderNotice;

import java.util.List;

/**
 * 开标记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IBusiBidOpeningService extends IService<BusiBidOpening> {
    /**
     * 查询开标记录列表
     *
     * @param busiBidOpening 开标记录
     * @return 开标记录集合
     */
    public List<BusiBidOpening> selectList(BusiBidOpening busiBidOpening);

    public boolean saveWithRecord(BusiBidOpening busiBidOpening);

    public List<BusiTenderNotice> openingProject();

    public BusiBidOpening selectByProject(Long projectId);

    public BusiBidOpening selectWithAttachment(Long busiBidOpeningId);

}