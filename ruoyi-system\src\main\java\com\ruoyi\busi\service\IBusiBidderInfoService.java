package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 参与投标人信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IBusiBidderInfoService extends IService<BusiBidderInfo> {
    /**
     * 查询参与投标人信息列表
     *
     * @param busiBidderInfo 参与投标人信息
     * @return 参与投标人信息集合
     */
    public List<BusiBidderInfo> selectList(BusiBidderInfo busiBidderInfo);

    public AjaxResult updateBidderInfo(BusiBidderInfo busiBidderInfo, LoginUser loginUser) throws Exception;

    AjaxResult bidderAnnouncement(Long projectId);
    AjaxResult getBidderInfoByProjectID(Long projectId);
//, HttpServletResponse response
    void exportBidOpeningRecords(Long projectId) throws IOException;

    void exportBidOpeningRecords1(Long projectId, HttpServletResponse response) throws IOException;

     AjaxResult signIn(LoginUser loginUser, BusiBidderInfo busiBidderInfo);
}
