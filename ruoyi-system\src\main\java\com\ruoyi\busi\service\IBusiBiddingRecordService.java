package com.ruoyi.busi.service;

import java.io.IOException;
import java.util.List;
import com.ruoyi.busi.domain.BusiBiddingRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;

/**
 * 投标记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IBusiBiddingRecordService extends IService<BusiBiddingRecord> {
    /**
     * 查询投标记录列表
     *
     * @param busiBiddingRecord 投标记录
     * @return 投标记录集合
     */
    public List<BusiBiddingRecord> selectList(BusiBiddingRecord busiBiddingRecord);


    AjaxResult saveBiddingRecord(BusiBiddingRecord busiBiddingRecord);


    AjaxResult getCancelInfo( BusiBiddingRecord byId );

    BusiBiddingRecord selectByProjectAndBidder(Long projectId, Long bidderId);

    List<BusiBiddingRecord> selectByProject(Long projectId);

    AjaxResult  responseFileDecryption(BusiBiddingRecord busiBiddingRecord, LoginUser loginUser) throws Exception;

    AjaxResult  responseFileDecryptionList(Long projectId) throws Exception;

    AjaxResult  getProjectFileById(Long projectId) throws Exception;

    AjaxResult  updateAmountByProject(Long projectId) throws Exception;


}
