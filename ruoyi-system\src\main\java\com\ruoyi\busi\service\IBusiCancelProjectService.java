package com.ruoyi.busi.service;

import java.io.IOException;
import java.util.List;
import com.ruoyi.busi.domain.BusiCancelProject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 取消采购项目Service接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IBusiCancelProjectService extends IService<BusiCancelProject> {
    /**
     * 查询取消采购项目列表
     *
     * @param busiCancelProject 取消采购项目
     * @return 取消采购项目集合
     */
    public List<BusiCancelProject> selectList(BusiCancelProject busiCancelProject);

    AjaxResult  saveCancelProject(BusiCancelProject busiCancelProject) throws IOException;
    AjaxResult approveRequest(BusiCancelProject busiCancelProject) throws IOException;

    AjaxResult getCancelProjectById(Long cancelId);

    String createContent(BusiCancelProject cancelNotice, BusiTenderProject tenderProject);
}
