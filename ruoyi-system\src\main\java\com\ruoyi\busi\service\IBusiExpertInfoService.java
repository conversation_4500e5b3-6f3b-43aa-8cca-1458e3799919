package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiExpertInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiExtractExpertApply;

/**
 * 专家信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IBusiExpertInfoService extends IService<BusiExpertInfo> {
    /**
     * 查询专家信息列表
     *
     * @param busiExpertInfo 专家信息
     * @return 专家信息集合
     */
    public List<BusiExpertInfo> selectList(BusiExpertInfo busiExpertInfo);

    /**
     * 导入用户数据
     *
     * @param list 专家专业分类数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importExpertInfo(List<BusiExpertInfo> list, boolean isUpdateSupport, String operName);

    /**
     * 抽取专家方法
     * @param searchExpertInfo 抽取参数
     * @return
     */
    public List<BusiExpertInfo> extractExpert(BusiExpertInfo searchExpertInfo);

}