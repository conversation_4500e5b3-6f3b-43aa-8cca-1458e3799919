package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiExpertLeaderVoteRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 专家组长投票记录Service接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface IBusiExpertLeaderVoteRecordService extends IService<BusiExpertLeaderVoteRecord> {
    /**
     * 查询专家组长投票记录列表
     *
     * @param busiExpertLeaderVoteRecord 专家组长投票记录
     * @return 专家组长投票记录集合
     */
    public List<BusiExpertLeaderVoteRecord> selectList(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord);

   public AjaxResult saveBusiExpertLeaderVoteRecord(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord);

    public AjaxResult getProjectLeaderVoteRecord(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord);
}
