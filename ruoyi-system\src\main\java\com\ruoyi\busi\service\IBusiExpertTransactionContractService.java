package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiExpertTransactionContract;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 成交合同Service接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
public interface IBusiExpertTransactionContractService extends IService<BusiExpertTransactionContract> {
    /**
     * 查询成交合同列表
     *
     * @param busiExpertTransactionContract 成交合同
     * @return 成交合同集合
     */
    public List<BusiExpertTransactionContract> selectList(BusiExpertTransactionContract busiExpertTransactionContract);

   AjaxResult  saveTransactionContract(BusiExpertTransactionContract busiExpertTransactionContract);

}
