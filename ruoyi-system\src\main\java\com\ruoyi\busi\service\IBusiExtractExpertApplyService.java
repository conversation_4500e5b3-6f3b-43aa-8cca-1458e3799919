package com.ruoyi.busi.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.utils.RestExtractParam;
import org.springframework.transaction.annotation.Transactional;

/**
 * 专家抽取申请Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiExtractExpertApplyService extends IService<BusiExtractExpertApply> {
    /**
     * 查询专家抽取申请列表
     *
     * @param busiExtractExpertApply 专家抽取申请
     * @return 专家抽取申请集合
     */
    public List<BusiExtractExpertApply> selectList(BusiExtractExpertApply busiExtractExpertApply);
    /**
     * 新增并包含专家组和回避条件
     *
     * @param busiExtractExpertApply 专家抽取申请
     * @return 专家抽取申请集合
     */
    public void saveHaveExtract(BusiExtractExpertApply busiExtractExpertApply) throws IOException;


    Boolean  suiJiChouQu(BusiExtractExpertApply busiExtractExpertApply) throws IOException;

    AjaxResult resetExtract( RestExtractParam restExtractParam) throws IOException;

    /**
     * 专家抽取
     * @param applyIds
     * @param isCheck
     * @throws Exception
     */
    public void extract(List<Long> applyIds, Boolean isCheck);

    boolean removeApply(Long applyId);
    void cancelExpertExtract(Long projectId) throws IOException;
}
