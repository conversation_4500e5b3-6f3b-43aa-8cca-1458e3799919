package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiExtractExpertEvade;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 专家抽取回避Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiExtractExpertEvadeService extends IService<BusiExtractExpertEvade> {
    /**
     * 查询专家抽取回避列表
     *
     * @param busiExtractExpertEvade 专家抽取回避
     * @return 专家抽取回避集合
     */
    public List<BusiExtractExpertEvade> selectList(BusiExtractExpertEvade busiExtractExpertEvade);

    /**
     * 获取专家组回避
     * @param applyIds 专家申请ids
     * @return
     */
    public List<BusiExtractExpertEvade> getByApplyIds(List<Long> applyIds);
}