package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiExtractExpertGroup;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 专家组Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiExtractExpertGroupService extends IService<BusiExtractExpertGroup> {
    /**
     * 查询专家组列表
     *
     * @param busiExtractExpertGroup 专家组
     * @return 专家组集合
     */
    public List<BusiExtractExpertGroup> selectList(BusiExtractExpertGroup busiExtractExpertGroup);

    /**
     * 获取专家组集合
     * @param applyIds 专家申请ids
     * @return
     */
    public List<BusiExtractExpertGroup> getByApplyIds(List<Long> applyIds);
}