package com.ruoyi.busi.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 专家抽取结果Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IBusiExtractExpertResultService extends IService<BusiExtractExpertResult> {
    /**
     * 查询专家抽取结果列表
     *
     * @param busiExtractExpertResult 专家抽取结果
     * @return 专家抽取结果集合
     */
    public List<BusiExtractExpertResult> selectList(BusiExtractExpertResult busiExtractExpertResult);


    AjaxResult  login(BusiExtractExpertResult busiExtractExpertResult);

    AjaxResult getLoginUserProject(BusiExtractExpertResult busiExtractExpertResult);

    List<BusiExtractExpertResult>  getZhuanJiaByProjectId(BusiExtractExpertResult busiExtractExpertResult) throws IOException;

    List<BusiExtractExpertResult>  getByProject(Long projectId) throws IOException;

   Map getZhuanJiaCount(Long expertId);

}
