package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiOpenMessageRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 在线开标消息记录Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface IBusiOpenMessageRecordService extends IService<BusiOpenMessageRecord> {
    /**
     * 查询在线开标消息记录列表
     *
     * @param busiOpenMessageRecord 在线开标消息记录
     * @return 在线开标消息记录集合
     */
    public List<BusiOpenMessageRecord> selectList(BusiOpenMessageRecord busiOpenMessageRecord);


    public AjaxResult historyMessages(Long projectId);
}
