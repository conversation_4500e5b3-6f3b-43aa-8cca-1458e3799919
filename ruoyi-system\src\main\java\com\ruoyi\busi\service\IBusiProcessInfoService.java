package com.ruoyi.busi.service;

import java.util.List;

import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.busi.domain.BusiProcessInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 全流程信息归档信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface IBusiProcessInfoService extends IService<BusiProcessInfo> {
    /**
     * 查询全流程信息归档信息列表
     *
     * @param busiProcessInfo 全流程信息归档信息
     * @return 全流程信息归档信息集合
     */
    public List<BusiProcessInfo> selectList(BusiProcessInfo busiProcessInfo);

    /**
     * 查询全流程信息归档信息信息
     *
     * @param processId 全流程信息归档信息
     * @return 全流程信息归档信息,包含文件信息
     */
    public List<BaseTreeData> selectProcessAttachment(Long processId);

    boolean initInfo(Long projectId, String projectName);
    public Boolean initAttachmentForProcess(Long processId);
}