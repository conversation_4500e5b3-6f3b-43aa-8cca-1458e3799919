package com.ruoyi.busi.service;

import java.util.List;
import com.ruoyi.busi.domain.BusiTenderDocumentsDownload;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 采购文件下载记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IBusiTenderDocumentsDownloadService extends IService<BusiTenderDocumentsDownload> {
    /**
     * 查询采购文件下载记录列表
     *
     * @param busiTenderDocumentsDownload 采购文件下载记录
     * @return 采购文件下载记录集合
     */
    public List<BusiTenderDocumentsDownload> selectList(BusiTenderDocumentsDownload busiTenderDocumentsDownload);
}