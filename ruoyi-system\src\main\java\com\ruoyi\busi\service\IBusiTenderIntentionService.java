package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiTenderIntention;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 采购意向Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiTenderIntentionService extends IService<BusiTenderIntention> {
    /**
     * 查询采购意向列表
     *
     * @param busiTenderIntention 采购意向
     * @return 采购意向集合
     */
    public List<BusiTenderIntention> selectList(BusiTenderIntention busiTenderIntention);

    AjaxResult saveBusiTenderIntention(BusiTenderIntention busiTenderIntention);

    AjaxResult editBusiTenderIntention(BusiTenderIntention busiTenderIntention);

    /**
     * 根据采购项目查询采购意向列表
     *
     * @param tenderProjectId 采购项目id
     * @return 采购意向集合
     */
    BusiTenderIntention selectByProject(Long tenderProjectId);

    int selectCount(BusiTenderIntention intentionQuery);

    AjaxResult getBusiTenderIntentionById(Long intentionId);

}
