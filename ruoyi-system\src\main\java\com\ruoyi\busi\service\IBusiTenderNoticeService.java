package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 采购公告信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiTenderNoticeService extends IService<BusiTenderNotice> {
    /**
     * 查询采购公告信息列表
     *
     * @param busiTenderNotice 采购公告信息
     * @return 采购公告信息集合
     */
    public List<BusiTenderNotice> selectList(BusiTenderNotice busiTenderNotice);

    AjaxResult  updateAnnouncementInfo(BusiTenderNotice busiTenderNotice);
    AjaxResult changeAnnouncementInfo(BusiTenderNotice busiTenderNotice) throws Exception;

    AjaxResult saveTenderNoticeAttachment( BusiTenderNotice busiTenderNotice) throws Exception;
    AjaxResult getTenderNoticeInfo(Long noticeId);
    AjaxResult getChangeNoticeInfo(Long noticeId, Integer type);
    AjaxResult removeTenderNotice( List<BusiTenderNotice> busiTenderNotices);
    //开标列表查询
    AjaxResult duringBidOpeninglist(BusiTenderNotice busiTenderNotice,LoginUser loginUser);

    AjaxResult duringBidOpeningInfo(BusiTenderNotice busiTenderNotice);


    BusiTenderNotice selectByProject(Long projectId);
    List<BusiTenderNotice> selectForBidOpening();

    NoticeInfoAndIdsVo getTenderNotisByProjectId(List<Long> projectIds);

    AjaxResult getNoticeStatistics(LoginUser loginUser);


    AjaxResult getNoticeStatisticsByType(LoginUser loginUser,Integer type);

    BusiTenderVo getTenderNotice(Long tenderNoticeId, boolean isMain);

    byte[] getTenderNoticeFileZip(Long tenderNoticeId) throws Exception;
    String getMainContent(BusiTenderProject tenderProject, BusiTenderNotice tenderNotice, BusiTenderNotice oldTenderNotice);

    BusiTenderNotice getOneIgnoreDeleted(BusiTenderNotice busiTenderNotice);

    AjaxResult getNoticeTypes(Long projectId);

    BusiTenderNotice getTenderNoticeByProjectId(Long projectId);

    AjaxResult push2Zcxh(Long projectId);
}
