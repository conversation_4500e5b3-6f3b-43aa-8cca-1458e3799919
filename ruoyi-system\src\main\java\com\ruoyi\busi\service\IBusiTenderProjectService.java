package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import freemarker.template.TemplateException;
import org.springframework.web.bind.annotation.PathVariable;

import java.io.IOException;
import java.util.List;

/**
 * 采购项目信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiTenderProjectService extends IService<BusiTenderProject> {
    /**
     * 查询采购项目信息列表
     *
     * @param busiTenderProject 采购项目信息
     * @return 采购项目信息集合
     */
    public List<BusiTenderProject> selectList(BusiTenderProject busiTenderProject);

    public List<BusiTenderProject> getListByExtractionType(BusiTenderProject busiTenderProject);


    /**
     * 查询所有能够新建开标信息的项目
     * @param busiTenderProject
     * @return
     */
    public List<BusiTenderProject> selectBidOpeningProject(BusiTenderProject busiTenderProject);

    public AjaxResult saveHaveAttachment(BusiTenderProject busiTenderProject);

    BusiTenderProject getByIdHasAttachment(Long projectId);

    BusiTenderVo getAllById(Long projectId);

    AjaxResult updateByIdHaveAttachment(BusiTenderProject busiTenderProject);


    ProjectInfoAndIdsVo getProjectsByLoginInfo(boolean isScope);

    ProjectInfoAndIdsVo getProjectsByProjectIds(List<Long> projectIds);

    AjaxResult supplierLookProdect(LoginUser  loginUser);

     AjaxResult supplierViewProdect(LoginUser loginUser);
    AjaxResult supplierisWinProdect(LoginUser loginUser);

    BusiTenderProject getSubById(Long projectId);

    AjaxResult codePage(Long projectId);

    void  freeMarker(Long  projectId) throws IOException, TemplateException;
}
