package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiVenueOccupy;

import java.util.List;

/**
 * 场地占用Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiVenueOccupyService extends IService<BusiVenueOccupy> {
    /**
     * 查询场地占用列表
     *
     * @param busiVenueOccupy 场地占用
     * @return 场地占用集合
     */
    public List<BusiVenueOccupy> selectList(BusiVenueOccupy busiVenueOccupy);

    List<BusiVenueOccupy> getListIgnoreDeleted(BusiVenueOccupy busiVenueOccupy);

    boolean deleteByNoticeId(Long noticeId);
}