package com.ruoyi.busi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.BusiWinningBidderAdviceNote;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 成交通知书Service接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IBusiWinningBidderAdviceNoteService extends IService<BusiWinningBidderAdviceNote> {
    /**
     * 查询成交通知书列表
     *
     * @param busiWinningBidderAdviceNote 成交通知书
     * @return 成交通知书集合
     */
    public List<BusiWinningBidderAdviceNote> selectList(BusiWinningBidderAdviceNote busiWinningBidderAdviceNote);

    void saveAdviceNote(BusiWinningBidderNotice winningBidderNotice);

    BusiWinningBidderAdviceNote selecInfoIncludeAttachments(Long adviceNoteId);

    boolean audit(BusiWinningBidderAdviceNote winningBidderNotice);

}