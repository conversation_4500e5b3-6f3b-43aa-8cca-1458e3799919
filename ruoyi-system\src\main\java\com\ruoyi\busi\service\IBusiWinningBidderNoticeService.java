package com.ruoyi.busi.service;

import java.io.IOException;
import java.util.List;

import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.common.core.domain.AjaxResult;
import freemarker.template.TemplateException;

/**
 * 中标结果公告信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBusiWinningBidderNoticeService extends IService<BusiWinningBidderNotice> {
    /**
     * 查询中标结果公告信息列表
     *
     * @param busiWinningBidderNotice 中标结果公告信息
     * @return 中标结果公告信息集合
     */
    public List<BusiWinningBidderNotice> selectList(BusiWinningBidderNotice busiWinningBidderNotice);

    /**
     * 新增中标结果通告
     * @param busiWinningBidderNotice
     */
    public AjaxResult saveBusiWinningBidderNotice(BusiWinningBidderNotice busiWinningBidderNotice);

    /**
     * 新增流标结果通告
     * @param busiWinningBidderNotice
     * @param abortiveType 失败类型：1投标供应商数量不足3家  3评审有效供应商数量不足3家
     */
    AjaxResult saveAbortiveTenderNotice(BusiWinningBidderNotice busiWinningBidderNotice);
    /**
     * 推送成交通知书
     * @param busiWinningBidderNotice
     */
    public AjaxResult pushNotice(BusiWinningBidderNotice busiWinningBidderNotice);

    /**
     * 生成采购结果公告内容
     * @return
     * @throws IOException
     * @throws TemplateException
     */
    String createNoticeContent(BusiWinningBidderNotice busiWinningBidderNotice, BusiTenderProject tenderProject, BusiBidderInfo winner) throws IOException, TemplateException;

    /**
     * 生成流标结果公告内容
     * @param projectId
     * @return
     */
    String createAbortiveTenderNoticeContent(Long projectId, String remark) throws IOException, TemplateException;

    BusiTenderVo view(Long noticeId);
}