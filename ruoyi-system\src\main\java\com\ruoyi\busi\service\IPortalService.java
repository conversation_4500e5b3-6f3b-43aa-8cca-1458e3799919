package com.ruoyi.busi.service;

import com.ruoyi.busi.domain.vo.PortalDataVo;
import com.ruoyi.busi.domain.vo.PortalProcessVo;

import java.util.List;

/**
 * 门户Service接口
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface IPortalService {
    /**
     * 查询列表
     */
    public List<PortalDataVo> list(PortalDataVo vo);
    /**
     * 查询列表
     */
    public List<PortalDataVo> notOpeninglist(PortalDataVo vo);
    /**
     * 查询公告详情，使用后端组装前端样式方式
     */
    public String info(PortalDataVo vo);
    /**
     * 查询列表
     */
    public List<PortalProcessVo> process(PortalDataVo vo);

}