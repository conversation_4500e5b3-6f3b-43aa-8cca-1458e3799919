package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiAttachmentMapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.service.IBusiAttachmentService;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiAttachmentServiceImpl extends ServiceImpl<BusiAttachmentMapper, BusiAttachment> implements IBusiAttachmentService {
    /**
     * 查询附件列表
     *
     * @param busiAttachment 附件
     * @return 附件
     */
    @Override
    public List<BusiAttachment> selectList(BusiAttachment busiAttachment) {
        QueryWrapper<BusiAttachment> busiAttachmentQueryWrapper = new QueryWrapper<>();
                        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(busiAttachment.getBusiId()),"busi_id",busiAttachment.getBusiId());
                        busiAttachmentQueryWrapper.like(ObjectUtil.isNotEmpty(busiAttachment.getFileName()),"file_name",busiAttachment.getFileName());
                        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(busiAttachment.getFileType()),"file_type",busiAttachment.getFileType());
                        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(busiAttachment.getFileSuffix()),"file_suffix",busiAttachment.getFileSuffix());
                        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(busiAttachment.getFilePath()),"file_path",busiAttachment.getFilePath());
                        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(busiAttachment.getFileMd5()),"file_md5",busiAttachment.getFileMd5());
        return list(busiAttachmentQueryWrapper);
    }

    @Override
    public List<BusiAttachment> getByIds(List<Long> attachmentIds) {
        if (attachmentIds != null && attachmentIds.size() > 0) {
            QueryWrapper<BusiAttachment> busiAttachmentQueryWrapper = new QueryWrapper<>();
            busiAttachmentQueryWrapper.in("attachment_id", attachmentIds);
            return list(busiAttachmentQueryWrapper);
        }
        return new ArrayList<>();
    }

    @Override
    public List<BusiAttachment> selectByBusiId(Long busiId, String fileType) {
        QueryWrapper<BusiAttachment> busiAttachmentQueryWrapper = new QueryWrapper<>();
        busiAttachmentQueryWrapper.eq("busi_id",busiId);
        busiAttachmentQueryWrapper.eq(ObjectUtil.isNotEmpty(fileType),"file_type",fileType);
        busiAttachmentQueryWrapper.eq("del_flag",0);
        return list(busiAttachmentQueryWrapper);
    }

    @Override
    public List<BusiAttachment> selectByBusiId(Long busiId) {
        return selectByBusiId(busiId, null);
    }

    @Override
    public boolean deleteByBusiId(Long busiId) {
        int i = baseMapper.deleteByBusiId(busiId);
        if(i>0){
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByBusiIdAndType(Long busiId, String fileType) {
        int i = baseMapper.deleteByBusiIdAndType(busiId,fileType);
        if(i>0){
            return true;
        }
        return false;
    }

    @Override
    public List<BusiAttachment> getByBusiId(Long projectId) {

        return list(new QueryWrapper<BusiAttachment>().eq("busi_id",projectId));
    }

    @Override
    public Map<Long, List<BusiAttachment>> getByBusiIds(List<Long> ids) {
        Map<Long, List<BusiAttachment>> map =new HashMap<>();
        for (Long id : ids) {
            map.put(id,list(new QueryWrapper<BusiAttachment>().eq("busi_id",id)));
        }
        return map;
    }
}
