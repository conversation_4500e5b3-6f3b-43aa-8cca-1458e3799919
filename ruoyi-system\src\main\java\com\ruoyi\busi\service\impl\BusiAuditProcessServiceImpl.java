package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.BusiAuditProcess;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;
import com.ruoyi.busi.mapper.BusiAuditProcessMapper;
import com.ruoyi.busi.service.IBusiAuditProcessService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiAuditProcessServiceImpl extends ServiceImpl<BusiAuditProcessMapper, BusiAuditProcess> implements IBusiAuditProcessService {
    @Autowired
    private ISysRoleService sysRoleService;
    /**
     * 查询附件列表
     *
     * @param busiAuditProcess 附件
     * @return 附件
     */
    @Override
    public List<BusiAuditProcess> selectList(BusiAuditProcess busiAuditProcess) {
        QueryWrapper<BusiAuditProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(busiAuditProcess.getBusiId()),"busi_id",busiAuditProcess.getBusiId());

        queryWrapper.orderByAsc("create_time");
        return list(queryWrapper);
    }

    @Override
    public List<BusiAuditProcess> selectList(Long busiId) {
        QueryWrapper<BusiAuditProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("busi_id",busiId);

        queryWrapper.orderByAsc("audit_time");
        return list(queryWrapper);
    }

    @Override
    public BusiAuditProcess selectLast(Long busiId) {
        QueryWrapper<BusiAuditProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("busi_id",busiId);
        queryWrapper.orderByDesc("create_time");
        List<BusiAuditProcess> processes = list(queryWrapper);
        if(processes!=null && !processes.isEmpty()) {
            return processes.get(0);
        }
        return null;
    }

    @Override
    public boolean checkAuditRole(Long busiId) {
        BusiAuditProcess process = selectLast(busiId);
        LoginUser user = SecurityUtils.getLoginUser();
        if (process.getNextOperator()!=null && SecurityUtils.getLoginUser() != null && SecurityUtils.getLoginUser().getUser() != null && SecurityUtils.getLoginUser().getUser().getRoles() != null) {
            List<SysRole> roleList = SecurityUtils.getLoginUser().getUser().getRoles();
            for (SysRole role : roleList) {
                if(role.getRoleId().equals(process.getNextOperator())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean saveInfo(Long busiId, AuditBusiTypeEnum busiType, Integer auditResult, String auditResultName, String auditRemark, Integer busiState) {
        BusiAuditProcess busiAuditProcess = new BusiAuditProcess();
        busiAuditProcess.setBusiId(busiId);
        busiAuditProcess.setAuditResult(auditResult);
        busiAuditProcess.setAuditRemark(auditRemark);
        busiAuditProcess.setBusiState(busiState);
        busiAuditProcess.setAuditResultName(auditResultName);
        busiAuditProcess.setAuditTime(new Date());
        busiAuditProcess.setBusiType(busiType.getCode());
        busiAuditProcess.setOperator(SecurityUtils.getUserId());
        busiAuditProcess.setOperatorName(SecurityUtils.getUsername());
        if (busiState > 0 && busiState < 10) {
            if(busiState<=busiType.getProcessNum()){
                SysRole role = sysRoleService.selectByKey(busiType.getProcess()[busiState-1]);
                busiAuditProcess.setNextOperator(role.getRoleId());
            }
        }
        int i = baseMapper.insert(busiAuditProcess);
        return i > 0;
    }

    @Override
    public boolean saveInfo(Long busiId, AuditBusiTypeEnum busiType, Integer auditResult, String auditRemark, Integer busiState) {
        String auditResultName = "";

        if (auditResult == 1) {
            auditResultName = "通过";
        }else{
            auditResultName = "退回";
        }
        return saveInfo(busiId, busiType, auditResult, auditResultName, auditRemark, busiState);
    }
}
