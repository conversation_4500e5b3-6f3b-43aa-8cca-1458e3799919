package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiBidEvaluation;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.mapper.BusiBidEvaluationMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.utils.AttachmentUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评标记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class BusiBidEvaluationServiceImpl extends ServiceImpl<BusiBidEvaluationMapper, BusiBidEvaluation> implements IBusiBidEvaluationService {
    @Autowired
    private IBusiBidEvaluationService busiBidEvaluationService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;
    @Autowired
    private IBusiWinningBidderNoticeService winningBidderNoticeService;
    @Autowired
    private AttachmentUtil attachmentUtil;

    /**
     * 查询评标记录列表
     *
     * @param busiBidEvaluation 评标记录
     * @return 评标记录
     */
    @Override
    public List<BusiBidEvaluation> selectList(BusiBidEvaluation busiBidEvaluation) {
        QueryWrapper<BusiBidEvaluation> busiBidEvaluationQueryWrapper = new QueryWrapper<>();

        if (ObjectUtil.isNotEmpty(busiBidEvaluation.getParams().get("dataScope")) &&
                (!busiBidEvaluation.getParams().containsKey("isScope") || Boolean.parseBoolean(busiBidEvaluation.getParams().get("isScope").toString()))) {
            LoginUser user = SecurityUtils.getLoginUser();
            if (user != null) {
                busiBidEvaluationQueryWrapper.inSql("project_id",
                        "SELECT project_id FROM busi_tender_project WHERE del_flag=0 AND "+busiBidEvaluation.getParams().get("dataScope").toString());
            }
        }
        busiBidEvaluationQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidEvaluation.getBidEvaluationId()),"bid_evaluation_id",busiBidEvaluation.getBidEvaluationId());
        String beginEvaluationStartTime = busiBidEvaluation.getParams().get("beginEvaluationStartTime")!=null?busiBidEvaluation.getParams().get("beginEvaluationStartTime")+"":"";
        String endEvaluationStartTime = busiBidEvaluation.getParams().get("endEvaluationStartTime")+""!=null?busiBidEvaluation.getParams().get("endEvaluationStartTime")+"":"";
        busiBidEvaluationQueryWrapper.between(ObjectUtil.isNotEmpty(beginEvaluationStartTime) && ObjectUtil.isNotEmpty(endEvaluationStartTime), "evaluation_start_time", beginEvaluationStartTime , endEvaluationStartTime);
        String beginBidEvaluationEndTime = busiBidEvaluation.getParams().get("beginBidEvaluationEndTime")!=null?busiBidEvaluation.getParams().get("beginBidEvaluationEndTime")+"":"";
        String endBidEvaluationEndTime = busiBidEvaluation.getParams().get("endBidEvaluationEndTime")+""!=null?busiBidEvaluation.getParams().get("endBidEvaluationEndTime")+"":"";
        busiBidEvaluationQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidEvaluationEndTime) && ObjectUtil.isNotEmpty(endBidEvaluationEndTime), "bid_evaluation_end_time", beginBidEvaluationEndTime , endBidEvaluationEndTime);
        busiBidEvaluationQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidEvaluation.getDelFlag()),"del_flag",busiBidEvaluation.getDelFlag());
        String beginCreateTime = busiBidEvaluation.getParams().get("beginCreateTime")!=null?busiBidEvaluation.getParams().get("beginCreateTime")+"":"";
        String endCreateTime = busiBidEvaluation.getParams().get("endCreateTime")+""!=null?busiBidEvaluation.getParams().get("endCreateTime")+"":"";
        busiBidEvaluationQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime , endCreateTime);
        busiBidEvaluationQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidEvaluation.getCreateBy()),"create_by",busiBidEvaluation.getCreateBy());
        String beginUpdateTime = busiBidEvaluation.getParams().get("beginUpdateTime")!=null?busiBidEvaluation.getParams().get("beginUpdateTime")+"":"";
        String endUpdateTime = busiBidEvaluation.getParams().get("endUpdateTime")+""!=null?busiBidEvaluation.getParams().get("endUpdateTime")+"":"";
        busiBidEvaluationQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime , endUpdateTime);
        busiBidEvaluationQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidEvaluation.getUpdateBy()),"update_by",busiBidEvaluation.getUpdateBy());
        busiBidEvaluationQueryWrapper.orderByDesc("create_time");

        List<BusiBidEvaluation> list = list(busiBidEvaluationQueryWrapper);
        for (BusiBidEvaluation bidEvaluation : list) {
            //获取项目
            BusiTenderProject byId = tenderProjectService.getById(bidEvaluation.getProjectId());
            if (Objects.nonNull(byId)) {
                bidEvaluation.setProject(byId);
            }
        }

         return list;
    }

    @Transactional
    @Override
    public AjaxResult saveBatchBusiBidderInfo(BusiBidEvaluation busiBidEvaluation) {
        // 检查是否存在与projectId关联的评审记录

        boolean hasExistingRecords = Optional.ofNullable(busiBidEvaluationService.list(new QueryWrapper<BusiBidEvaluation>()
                        .eq("project_id", busiBidEvaluation.getProjectId())
                        .eq("del_flag", 0)))
                .filter(list -> !list.isEmpty())
                .isPresent();
        if (hasExistingRecords) {
            throw  new RuntimeException("该项目已存在评审记录，如需修改，请先删除再新增！");
        }

        // 保存评审记录
        boolean saveSuccess = busiBidEvaluationService.save(busiBidEvaluation);
        if (!saveSuccess) {
            throw  new RuntimeException("评审记录保存失败！");
        }
        // 处理附件保存逻辑

        attachmentUtil.addNewAttachments(busiBidEvaluation);
//        List<BusiAttachment> attachments = busiBidEvaluation.getAttachments();
//        if (!attachments.isEmpty()){
//            List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
//                item.setBusiId(busiBidEvaluation.getBidEvaluationId());
//                item.setDelFlag(DelFlagStatus.OK.getCode());
//                item.setCreateBy(busiBidEvaluation.getCreateBy());
//                item.setUpdateBy(busiBidEvaluation.getUpdateBy());
//                item.setUpdateTime(busiBidEvaluation.getUpdateTime());
//                item.setCreateTime(busiBidEvaluation.getCreateTime());
//                return item;
//            }).collect(Collectors.toList());

            // 保存附件
//            boolean attachmentSaveSuccess = iBusiAttachmentService.saveBatch(batchSaveList);
//            if (!attachmentSaveSuccess) {
//                throw  new RuntimeException("附件保存失败！");
//            }
//        }
       /* // 设置投标人信息的创建和更新者
        List<BusiBidderInfo> projectInfoList = busiBidEvaluation.getProjectInfoList();
        AtomicBoolean haveWinner = new AtomicBoolean(false);
        projectInfoList.forEach(busiBidderInfo -> {
            if (busiBidderInfo.getIsAbandonedBid() == 1) {
                if(StringUtils.isBlank(busiBidderInfo.getAbandonedBidReason())) {
                    throw new RuntimeException("废标原因不能为空！");
                }
            }else {
                if(busiBidderInfo.getBidderAmount()==null){
                    throw  new RuntimeException("投标金额不能为空！");
                }
                if(busiBidderInfo.getIsWin() !=null && busiBidderInfo.getIsWin()==1){
                    haveWinner.set(true);
                }
            }
        });
        if (!haveWinner.get() && busiBidEvaluation.getAbortiveTender()==0) {
            throw  new RuntimeException("中标人不能为空！");
        }
        // 保存投标人信息
        boolean batchSaveSuccess = busiBidderInfoService.saveOrUpdateBatch(projectInfoList);
        if (!batchSaveSuccess) {
            throw  new RuntimeException("参与投标人信息保存失败！");
        }

        //如果评审结果为流标，则自动生成流标结果公告，并将采购项目状态置为流标状态
        if(busiBidEvaluation.getAbortiveTender()==1){
            try {
                BusiWinningBidderNotice busiWinningBidderNotice = new BusiWinningBidderNotice();
                busiWinningBidderNotice.setProjectId(busiBidEvaluation.getProjectId());
                busiWinningBidderNotice.setAbortiveType(3);
                winningBidderNoticeService.saveAbortiveTenderNotice(busiWinningBidderNotice);
            } catch (Exception e) {
                throw  new RuntimeException("流标公告保存失败！");
            }
        }else{
            project.setProjectStatus(50);
        }*/
        BusiTenderProject project = tenderProjectService.getById(busiBidEvaluation.getProjectId());

        project.setProjectStatus(50);
        tenderProjectService.updateById(project);
        return AjaxResult.success("评审记录保存成功！");
    }

    @Override
    public AjaxResult removeBusiBidderInfo(Long[] bidEvaluationIds) {
        List<BusiBidEvaluation> busiBidEvaluations = busiBidEvaluationService.listByIds(Arrays.asList(bidEvaluationIds));
        // 使用Stream API提取projectId并生成idList
        List<Long> projectIdList = busiBidEvaluations.stream()
                .map(BusiBidEvaluation::getProjectId) // 假设getProjectId()方法返回projectId
                .collect(Collectors.toList());
        //删除参与投标人信息
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().in("project_id",projectIdList));
        if (!busiBidderInfos.isEmpty()){
            List<Long> bidderInfoList = busiBidderInfos.stream()
                    .map(BusiBidderInfo::getBidderInfoId) // 假设getProjectId()方法返回projectId
                    .collect(Collectors.toList());
            // busiBidderInfoService.updateBatchById(busiBidderInfos);
            busiBidderInfoService.removeByIds(bidderInfoList);
        }

        //删除附件信息
        List<Long> busiBidderInfoIdList=busiBidEvaluations.stream()
                .map(BusiBidEvaluation::getBidEvaluationId) // 假设getProjectId()方法返回projectId
                .collect(Collectors.toList());
        List<BusiAttachment> busiAttachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().in("busi_id",busiBidderInfoIdList).eq("del_flag",0));
        if (!busiAttachments.isEmpty()){
            List<Long> attachmentIds=busiAttachments.stream()
                    .map(BusiAttachment::getAttachmentId) // 假设getProjectId()方法返回projectId
                    .collect(Collectors.toList());
            iBusiAttachmentService.removeByIds(attachmentIds);
        }
        return AjaxResult.success(busiBidEvaluationService.removeByIds(busiBidderInfoIdList));
    }

    @Override
    public BusiBidEvaluation selectByProject(Long projectId) {
        QueryWrapper<BusiBidEvaluation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(true, "project_id", projectId);
        queryWrapper.eq(true, "del_flag", 0);
        return getOne(queryWrapper);
    }

}
