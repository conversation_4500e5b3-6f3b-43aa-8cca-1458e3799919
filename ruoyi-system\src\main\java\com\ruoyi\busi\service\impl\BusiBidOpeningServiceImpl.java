package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.mapper.BusiBidOpeningMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.utils.AttachmentUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开标记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class BusiBidOpeningServiceImpl extends ServiceImpl<BusiBidOpeningMapper, BusiBidOpening> implements IBusiBidOpeningService {
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IBusiTenderNoticeService tenderNoticeService;
    @Autowired
    private IBusiAttachmentService busiAttachmentService;
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private AttachmentUtil attachmentUtil;

    /**
     * 查询开标记录列表
     *
     * @param busiBidOpening 开标记录
     * @return 开标记录
     */
    @Override
    public List<BusiBidOpening> selectList(BusiBidOpening busiBidOpening) {
        QueryWrapper<BusiBidOpening> busiBidOpeningQueryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(busiBidOpening.getParams().get("dataScope")) &&
                (!busiBidOpening.getParams().containsKey("isScope") || Boolean.parseBoolean(busiBidOpening.getParams().get("isScope").toString()))) {
            LoginUser user = SecurityUtils.getLoginUser();
            if (user != null) {
                busiBidOpeningQueryWrapper.inSql("project_id",
                        "SELECT project_id FROM busi_tender_project WHERE del_flag=0 AND "+busiBidOpening.getParams().get("dataScope").toString());
            }
        }
        busiBidOpeningQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidOpening.getCompereId()),"compere_id",busiBidOpening.getCompereId());
        busiBidOpeningQueryWrapper.like(ObjectUtil.isNotEmpty(busiBidOpening.getCompereName()),"compere_name",busiBidOpening.getCompereName());
        busiBidOpeningQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidOpening.getCompereCode()),"compere_code",busiBidOpening.getCompereCode());
        busiBidOpeningQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBidOpening.getBidOpeningMode()),"bid_opening_mode",busiBidOpening.getBidOpeningMode());
        String beginDecodeStartTime = busiBidOpening.getParams().get("beginDecodeStartTime")!=null?busiBidOpening.getParams().get("beginDecodeStartTime")+"":"";
        String endDecodeStartTime = busiBidOpening.getParams().get("endDecodeStartTime")+""!=null?busiBidOpening.getParams().get("endDecodeStartTime")+"":"";
        busiBidOpeningQueryWrapper.between(ObjectUtil.isNotEmpty(beginDecodeStartTime) && ObjectUtil.isNotEmpty(endDecodeStartTime), "decode_start_time", beginDecodeStartTime , endDecodeStartTime);
        String beginBidAnnounceTime = busiBidOpening.getParams().get("beginBidAnnounceTime")!=null?busiBidOpening.getParams().get("beginBidAnnounceTime")+"":"";
        String endBidAnnounceTime = busiBidOpening.getParams().get("endBidAnnounceTime")+""!=null?busiBidOpening.getParams().get("endBidAnnounceTime")+"":"";
        busiBidOpeningQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidAnnounceTime) && ObjectUtil.isNotEmpty(endBidAnnounceTime), "bid_announce_time", beginBidAnnounceTime , endBidAnnounceTime);
        String beginBidOpeningTime = busiBidOpening.getParams().get("beginBidOpeningTime")!=null?busiBidOpening.getParams().get("beginBidOpeningTime")+"":"";
        String endBidOpeningTime = busiBidOpening.getParams().get("endBidOpeningTime")+""!=null?busiBidOpening.getParams().get("endBidOpeningTime")+"":"";
        busiBidOpeningQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidOpeningTime) && ObjectUtil.isNotEmpty(endBidOpeningTime), "bid_opening_time", beginBidOpeningTime , endBidOpeningTime);
        String beginBidOpeningEndTime = busiBidOpening.getParams().get("beginBidOpeningEndTime")!=null?busiBidOpening.getParams().get("beginBidOpeningEndTime")+"":"";
        String endBidOpeningEndTime = busiBidOpening.getParams().get("endBidOpeningEndTime")+""!=null?busiBidOpening.getParams().get("endBidOpeningEndTime")+"":"";
        busiBidOpeningQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidOpeningEndTime) && ObjectUtil.isNotEmpty(endBidOpeningEndTime), "bid_opening_end_time", beginBidOpeningEndTime , endBidOpeningEndTime);
        busiBidOpeningQueryWrapper .orderByDesc("create_time");
        return list(busiBidOpeningQueryWrapper);
    }

    @Transactional
    @Override
    public boolean saveWithRecord(BusiBidOpening busiBidOpening) {
        boolean success = saveOrUpdate(busiBidOpening);
        if (success) {
            //修改项目当前状态
            BusiTenderProject project = busiTenderProjectService.getById(busiBidOpening.getProjectId());
            project.setProjectStatus(40);
            busiTenderProjectService.updateById(project);
            //修改采购公告开标结束时间
            BusiTenderNotice notice = tenderNoticeService.selectByProject(busiBidOpening.getProjectId());
            notice.setBidOpeningEndTime(busiBidOpening.getBidOpeningEndTime());
            tenderNoticeService.updateById(notice);

            // 处理附件保存逻辑
            List<BusiAttachment> attachments = busiBidOpening.getAttachments();
            if (!attachments.isEmpty()){
                List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
                    item.setBusiId(busiBidOpening.getBidOpeningId());
                    item.setDelFlag(DelFlagStatus.OK.getCode());
                    item.setCreateBy(busiBidOpening.getCreateBy());
                    item.setUpdateBy(busiBidOpening.getUpdateBy());
                    item.setUpdateTime(busiBidOpening.getUpdateTime());
                    item.setCreateTime(busiBidOpening.getCreateTime());
                    return item;
                }).collect(Collectors.toList());

                // 保存附件
                boolean attachmentSaveSuccess = busiAttachmentService.saveBatch(batchSaveList);
                if (!attachmentSaveSuccess) {
                    throw  new RuntimeException("附件保存失败！");
                }
            }
//            Map<String, String> attachmentMap = busiBidOpening.getAttachmentMap();
//            for (String key : attachmentMap.keySet()) {
//                String filePaths = attachmentMap.get(key);
//                if (StringUtils.isNoneBlank(filePaths)) {
//                    String[] filePathList = filePaths.split(",");
//                    for (String filePath : filePathList) {
//                        BusiAttachment attachment = new BusiAttachment();
//                        attachment.setFileType(key);
//                        attachment.setFilePath(filePath);
//                        attachment.setBusiId(busiBidOpening.getBidOpeningId());
//                        attachment.setFileName(getFileName(filePath));
//                        attachment.setFileSuffix(getFileSuffix(filePath));
//                        busiAttachmentService.save(attachment);
//                    }
//                }
//            }

            String s = sysConfigService.selectConfigByKey("sys.version");
//            if("1.0".equals(s)){ //将投标信息转换为开标信息并保存
//                BusiBiddingRecord recordQuery = new BusiBiddingRecord();
//                recordQuery.setProjectId(busiBidOpening.getProjectId());
//                recordQuery.setDelFlag(0);
//                recordQuery.getParams().put("isScope", "1");
//                List<BusiBiddingRecord> busiBiddingRecordList = busiBiddingRecordService.selectList(recordQuery);
//                for (BusiBiddingRecord record : busiBiddingRecordList) {
//                    BusiBidderInfo info = new BusiBidderInfo();
//
//                    info.setProjectId(busiBidOpening.getProjectId());
//                    info.setBidderId(record.getBidderId());
//                    info.setBidderCode(record.getBidderCode());
//                    info.setBidderName(record.getBidderName());
//                    info.setDecodeFlag(1);
//
//                    busiBidderInfoService.save(info);
//                }
//            }

            return true;
        }
        return false;
    }

    @Override
    public List<BusiTenderNotice> openingProject() {
        return tenderNoticeService.selectForBidOpening();
    }

    @Override
    public BusiBidOpening selectByProject(Long projectId) {
        QueryWrapper<BusiBidOpening> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(true, "project_id", projectId);
        queryWrapper.eq(true, "del_flag", 0);
        BusiBidOpening one = getOne(queryWrapper);
        one.setAttachments(busiAttachmentService.getByBusiId(one.getBidOpeningId()));
        return one;
    }

    @Override
    public BusiBidOpening selectWithAttachment(Long busiBidOpeningId) {
        BusiBidOpening info = getById(busiBidOpeningId);
        BusiBidderInfo infoQuery = new BusiBidderInfo();
        infoQuery.setProjectId(info.getProjectId());
        infoQuery.setDelFlag(0);
        infoQuery.getParams().put("isScope", "1");
        List<BusiBidderInfo> bidderInfoList = busiBidderInfoService.selectList(infoQuery);
        info.setBusiBidderInfoList(bidderInfoList);
//        JSONObject map = new JSONObject();
//        List<SysDictData> sysDictDataList = sysDictTypeService.selectDictDataByType("bid_opening_attachment");
//        List<BusiAttachment> attachmentList = busiAttachmentService.selectByBusiId(busiBidOpeningId);
//        for (SysDictData data : sysDictDataList) {
//            StringBuilder dataFile = new StringBuilder();
//            for (BusiAttachment attachment : attachmentList) {
//                if (attachment.getFileType().equals(data.getDictValue())) {
//                    dataFile.append(",").append(attachment.getFilePath());
//                }
//            }
//            if (dataFile.length() > 0) {
//                map.put(data.getDictLabel(), dataFile.substring(1));
//            }else{
//                map.put(data.getDictLabel(), "");
//            }
//        }
//        info.setAttachmentMap(map);
        info.setAttachmentMap(attachmentUtil.getAttachmentMap(busiBidOpeningId, ProcessEnum.BID_OPENING.getAttachmentCode()));
        return info;
    }

    private String getFileSuffix(String filePath){
        int i = filePath.lastIndexOf(".");
        if (i != -1) {
            return filePath.substring(i+1);
        }
        return "";
    }


    private String getFileName(String filePath){
        int i = filePath.lastIndexOf("/");
        if (i != -1) {
            return filePath.substring(i+1);
        }
        return "";
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("1", "11");
        System.out.println(map);
        String s = map.get("1");
        s += "111";
        System.out.println(map);
    }
}
