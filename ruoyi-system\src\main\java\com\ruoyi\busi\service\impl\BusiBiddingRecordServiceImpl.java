package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.XmlToEntityVo;
import com.ruoyi.busi.mapper.BusiBiddingRecordMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.service.IDocResponseEntInfoService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.utils.XmlToObjectUtil;
import com.ruoyi.utils.XmlUtil;
import com.ruoyi.utils.ZipUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 投标记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class BusiBiddingRecordServiceImpl extends ServiceImpl<BusiBiddingRecordMapper, BusiBiddingRecord> implements IBusiBiddingRecordService {
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IBusiBiddingRecordService iBusiBiddingRecordService;
    @Autowired
    private IBusiBidderInfoService iBusiBidderInfoService;

    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;

    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IBidOpeningOperationRecordService iBidOpeningOperationRecordService;
    @Resource
    IBusiBidderInfoService busiBidderInfoService;
    @Resource
    IDocResponseEntInfoService docResponseEntInfoService;
;
    /**
     * 查询投标记录列表
     *
     * @param busiBiddingRecord 投标记录
     * @return 投标记录
     */
    @Override
    @DataScope(entAlias = "bidder_id")
    public List<BusiBiddingRecord> selectList(BusiBiddingRecord busiBiddingRecord) {
        QueryWrapper<BusiBiddingRecord> busiBiddingRecordQueryWrapper = new QueryWrapper<>();
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getProjectId()), "project_id", busiBiddingRecord.getProjectId());
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getBidderId()), "bidder_id", busiBiddingRecord.getBidderId());
        busiBiddingRecordQueryWrapper.like(ObjectUtil.isNotEmpty(busiBiddingRecord.getBidderName()), "bidder_name", busiBiddingRecord.getBidderName());
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getBidderCode()), "bidder_code", busiBiddingRecord.getBidderCode());
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getUploadIp()), "upload_ip", busiBiddingRecord.getUploadIp());
        String beginUploadTime = busiBiddingRecord.getParams().get("beginUploadTime") != null ? busiBiddingRecord.getParams().get("beginUploadTime") + "" : "";
        String endUploadTime = busiBiddingRecord.getParams().get("endUploadTime") + "" != null ? busiBiddingRecord.getParams().get("endUploadTime") + "" : "";
        busiBiddingRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginUploadTime) && ObjectUtil.isNotEmpty(endUploadTime), "upload_time", beginUploadTime, endUploadTime);
        String beginCancelTime = busiBiddingRecord.getParams().get("beginCancelTime") != null ? busiBiddingRecord.getParams().get("beginCancelTime") + "" : "";
        String endCancelTime = busiBiddingRecord.getParams().get("endCancelTime") + "" != null ? busiBiddingRecord.getParams().get("endCancelTime") + "" : "";
        busiBiddingRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginCancelTime) && ObjectUtil.isNotEmpty(endCancelTime), "cancel_time", beginCancelTime, endCancelTime);
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getDelFlag()), "del_flag", busiBiddingRecord.getDelFlag());
        String beginCreateTime = busiBiddingRecord.getParams().get("beginCreateTime") != null ? busiBiddingRecord.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiBiddingRecord.getParams().get("endCreateTime") + "" != null ? busiBiddingRecord.getParams().get("endCreateTime") + "" : "";
        busiBiddingRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getCreateBy()), "create_by", busiBiddingRecord.getCreateBy());
        String beginUpdateTime = busiBiddingRecord.getParams().get("beginUpdateTime") != null ? busiBiddingRecord.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiBiddingRecord.getParams().get("endUpdateTime") + "" != null ? busiBiddingRecord.getParams().get("endUpdateTime") + "" : "";
        busiBiddingRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiBiddingRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiBiddingRecord.getUpdateBy()), "update_by", busiBiddingRecord.getUpdateBy());

        busiBiddingRecordQueryWrapper.orderByDesc("create_time");

        busiBiddingRecordQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiBiddingRecord.getParams().get("dataScope"))
                        &&!busiBiddingRecord.getParams().containsKey("isScope"),
                busiBiddingRecord.getParams().get("dataScope") + ""
        );
        List<BusiBiddingRecord> list = list(busiBiddingRecordQueryWrapper);

        for (BusiBiddingRecord biddingRecord : list) {
            biddingRecord.setProject(iBusiTenderProjectService.getById(biddingRecord.getProjectId()));
            biddingRecord.setPriceUnitLabel(dictDataService.selectDictLabel("price_unit", biddingRecord.getPriceUnit()));
            List<BusiAttachment> busiAttachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().eq("busi_id", biddingRecord.getBiddingId()).eq("del_flag", 0));
            if (!busiAttachments.isEmpty()){
                biddingRecord.setAttachments(busiAttachments);
            }
        }

        return list;
    }

    @Transactional
    @Override
    public AjaxResult saveBiddingRecord(BusiBiddingRecord busiBiddingRecord) {
        ArrayList<Long> longs = new ArrayList<>();
        longs.add(busiBiddingRecord.getProjectId());
        List<BusiTenderNotice> projectId = busiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().in("project_id", longs));
        if (!projectId.isEmpty()){
            busiBiddingRecord.setPriceUnit(projectId.get(0).getPriceUnit());
        }
        List<BusiBiddingRecord> list = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("project_id", busiBiddingRecord.getProjectId())
                .eq("del_flag", 0)
                .eq("bidder_id", busiBiddingRecord.getBidderId())
        );
        if (!list.isEmpty()) {
            // list 不为空，执行相应操作
            throw  new RuntimeException("该项目已投标，请先取消原投标记录");
        } else {
            save(busiBiddingRecord);
            // 处理附件保存逻辑
            List<BusiAttachment> attachments = busiBiddingRecord.getAttachments();
            List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
                item.setBusiId(busiBiddingRecord.getBiddingId());
                item.setDelFlag(DelFlagStatus.OK.getCode());
                item.setCreateBy(busiBiddingRecord.getCreateBy());
                item.setUpdateBy(busiBiddingRecord.getUpdateBy());
                item.setUpdateTime(busiBiddingRecord.getUpdateTime());
                item.setCreateTime(busiBiddingRecord.getCreateTime());
                return item;
            }).collect(Collectors.toList());

            // 保存附件
            boolean attachmentSaveSuccess = iBusiAttachmentService.saveBatch(batchSaveList);
            if (!attachmentSaveSuccess) {
                throw  new RuntimeException("附件保存失败！");
            }
        }

        return AjaxResult.success("保存成功！");
    }
    @Transactional
    @Override
    public AjaxResult getCancelInfo(BusiBiddingRecord busiBiddingRecord) {
        boolean updateById = updateById(busiBiddingRecord);
        if (!updateById) {
            throw  new RuntimeException("撤回保存失败！");
        }
        //删除附件
        List<BusiAttachment> busiAttachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().eq("busi_id", busiBiddingRecord.getBiddingId()).eq("del_flag", 0));
        if (!busiAttachments.isEmpty()){
            List<Long> collect = busiAttachments.stream().map(BusiAttachment::getAttachmentId).collect(Collectors.toList());
            iBusiAttachmentService.removeByIds(collect);
        }
        return AjaxResult.success(removeById(busiBiddingRecord.getBiddingId()));
    }

    @Override
    public List<BusiBiddingRecord> selectByProject(Long projectId) {
        return baseMapper.selectByProject(projectId);
    }

    private File getAttachment(BusiAttachment attachment){
        String filePath = RuoYiConfig.getUploadPath();
        File file2 = new File("/");
        String c3 = file2.getAbsolutePath();
        String path = attachment.getFilePath();
        String realPath = "";
        int i = 0;
        i = path.indexOf("upload");
        if(i>0) {
            realPath = filePath + path.substring(i + 6);
            realPath = realPath.replace("\\", "");
        }
        File file = new File(realPath);
        if (file.exists()) {
            return file;
        }
        return null;
    }
    @Transactional
    @Override
    public AjaxResult responseFileDecryption(BusiBiddingRecord busiBiddingRecord, LoginUser loginUser) throws Exception {
        //如果是供应商，点击解密只能获取自己的信息
       // if (loginUser.getUser().getRoles().get(0).getRoleKey().equals("supplier")) {
        //projectId

        BidOpeningOperationRecord recordServiceOne = iBidOpeningOperationRecordService.getOne(
                new QueryWrapper<BidOpeningOperationRecord>()
                .eq("project_id", busiBiddingRecord.getProjectId())
                .eq("operation_type", 3)
        );

        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis < recordServiceOne.getDecryptionTime().getTime()) {
            System.out.println("当前时间在解密时间之前");
            BusiBiddingRecord byIdBusiBiddingRecord = iBusiBiddingRecordService.getById(busiBiddingRecord.getBiddingId());
            //获取上传的响应文件gys1.tbwj
            List<BusiAttachment> byBusiId = iBusiAttachmentService.getByBusiId(busiBiddingRecord.getBiddingId());
            if (!byBusiId.isEmpty()){
                if (byBusiId.size()==1){
                    BusiAttachment busiAttachment=byBusiId.get(0);
                    File attachment = getAttachment(busiAttachment);
                    //String encryptedFilePath = busiAttachment.getFilePath(); // 服务器上加密文件的路径
                    // ZipUtil.decryptUrl(encryptedFilePath, busiBiddingRecord.getSupplierKey());
                    // String encryptedFilePath = "/profile/upload/gys1.tbwj";
                    String fileNameWithoutExtension = attachment.getName().substring(0, attachment.getName().lastIndexOf('.'));

                    String newFileName = fileNameWithoutExtension + ".zip";
                    if ( !Md5Utils.hash(busiBiddingRecord.getSupplierKey()).equals(loginUser.getUser().getEnt().getSecretKey())){
                        return AjaxResult.error("解密密码错误");
                    }
                    String first16Chars = Md5Utils.hash(loginUser.getUser().getEnt().getEntCode()).substring(0, 16); // 提取前16位字符

                    String zip =ZipUtil.decrypt(attachment.getPath(),newFileName,first16Chars );
                    String dirPath= ZipUtil.unzip(zip);
                    String xmlPath=dirPath+"/responseProject.xml";
                    System.out.println(dirPath);
//                JSONObject xmlJson = XmlUtil.getXml2Json(xmlPath);
//                System.out.println(xmlJson);
                    XmlToEntityVo xmlToEntityVo = XmlToObjectUtil.convertXmlToObject(XmlUtil.getXml2String(xmlPath), XmlToEntityVo.class);

                    Optional<XmlToEntityVo.BidInfoBean> bidInfoBeanOptional = xmlToEntityVo.getInfoBeans().stream()
                            .filter(bean -> "投标报价".equals(bean.getName()))
                            .findFirst();
                    Optional<XmlToEntityVo.BidAnnounceBean> headBidAnnounceBean = xmlToEntityVo.getBidAnnounceBeans().stream()
                            .filter(bean -> "项目负责人".equals(bean.getName()))
                            .findFirst();
                    Optional<XmlToEntityVo.BidAnnounceBean> LegalBidAnnounceBean = xmlToEntityVo.getBidAnnounceBeans().stream()
                            .filter(bean -> "法定代表人或授权委托人".equals(bean.getName()))
                            .findFirst();

                    System.out.println(JSONObject.toJSONString(xmlToEntityVo));
                    QueryWrapper<BusiBidderInfo> queryWrapper=new QueryWrapper<BusiBidderInfo>();
                    queryWrapper.eq("project_id",byIdBusiBiddingRecord.getProjectId());
                    queryWrapper.eq("bidder_id",byIdBusiBiddingRecord.getBidderId());
                    queryWrapper.eq("del_flag",0);//是否已删除
                    queryWrapper.eq("decode_flag",0);//是否已解密
                    List<BusiBidderInfo> list = iBusiBidderInfoService.list(queryWrapper);
                    if (list.size() == 1){
                        BusiBidderInfo busiBidderInfo=list.get(0);
                        busiBidderInfo.setDecodeFlag(1);
                        busiBidderInfo.setDecodeTime(new Date());
                        if (bidInfoBeanOptional.isPresent()) {
                            XmlToEntityVo.BidInfoBean bidInfoBean = bidInfoBeanOptional.get();
                            // 这里可以继续使用bidInfoBean对象
                            busiBidderInfo.setBidderAmount(new BigDecimal(bidInfoBean.getValue()));
                        }
                        if (headBidAnnounceBean.isPresent()) {
                            XmlToEntityVo.BidAnnounceBean bidInfoBean = headBidAnnounceBean.get();
                            // 这里可以继续使用bidInfoBean对象
                            busiBidderInfo.setLegalPerson(bidInfoBean.getValue());//法人
                        }
                        if (LegalBidAnnounceBean.isPresent()) {
                            XmlToEntityVo.BidAnnounceBean bidInfoBean = LegalBidAnnounceBean.get();
                            // 这里可以继续使用bidInfoBean对象
                            busiBidderInfo.setHeadPerson(bidInfoBean.getValue());//负责人
                        }
                        iBusiBidderInfoService.updateById(busiBidderInfo);
                    }
                    return AjaxResult.success(xmlToEntityVo);
                }else {
                    return AjaxResult.error("查到多个投标文件！");
                }
            }else {
                return AjaxResult.error("没有查到相应的投标文件！");
            }
        } else {
            System.out.println("当前时间不在解密时间之前");
            return AjaxResult.error("已超出解密时间范围");
        }

    }

    @Override
    public BusiBiddingRecord selectByProjectAndBidder(Long projectId, Long bidderId){
        BusiBiddingRecord query = new BusiBiddingRecord();
        query.setProjectId(projectId);
        query.setBidderId(bidderId);
        List<BusiBiddingRecord> biddingRecords = selectList(query);
        if (biddingRecords == null || biddingRecords.isEmpty()) {
            throw new RuntimeException("投标记录为空");
        }
        if (biddingRecords.size() > 1) {
            throw new RuntimeException("投标记录数量异常");
        }
        return biddingRecords.get(0);
    }

    @Override
    public AjaxResult responseFileDecryptionList(Long projectId) throws Exception {
        //获取上传响应文件记录
        List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("project_id", projectId));
        //投标项目人ids
        List<Long> bidderIds = biddingRecords.stream()
                .map(BusiBiddingRecord::getBidderId)
                .collect(Collectors.toList());
        //投标信息
        List<BusiBidderInfo> bidderInfos = iBusiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("decode_flag", 1)
                .eq("project_id", projectId).in("bidder_id", bidderIds));
        //已经解密的投标信息人id
        List<Long> bidderids = bidderInfos.stream()
                .map(BusiBidderInfo::getBidderId)
                .collect(Collectors.toList());
        List<BusiBiddingRecord> decodeBiddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("project_id", projectId).in("bidder_id",bidderids));
       //已经解密的投标文件id
        List<Long> biddingIds = decodeBiddingRecords.stream()
                .map(BusiBiddingRecord::getBiddingId)
                .collect(Collectors.toList());
        List<JSONObject> resultJsonObject=new ArrayList<>();
        //获取全部加密附件


        for (Long biddingId : biddingIds) {
            List<BusiAttachment> byBusiId = iBusiAttachmentService.getByBusiId(biddingId);
            if (byBusiId.size()==1){
                BusiAttachment busiAttachment=byBusiId.get(0);
                File attachment = getAttachment(busiAttachment);
                String fileNameWithoutExtension = attachment.getName().substring(0, attachment.getName().lastIndexOf('.'));

                String newFileName = fileNameWithoutExtension + ".zip";
                String zip =ZipUtil.decrypt(attachment.getPath(),newFileName, busiAttachment.getFileMd5());
                //String encryptedFilePath = busiAttachment.getFilePath(); // 服务器上加密文件的路径
                // ZipUtil.decryptUrl(encryptedFilePath, busiBiddingRecord.getSupplierKey());
                // String encryptedFilePath = "/profile/upload/gys1.tbwj";
               // String zip =ZipUtil.decryptUrl(busiAttachment.getFilePath(), busiAttachment.getFileMd5());
                String dirPath= ZipUtil.unzip(zip);
                String xmlPath=dirPath+"/responseProject.xml";
                System.out.println(dirPath);
//                JSONObject xmlJson = XmlUtil.getXml2Json(xmlPath);
//                System.out.println(xmlJson);
                XmlToEntityVo xmlToEntityVo = XmlToObjectUtil.convertXmlToObject(XmlUtil.getXml2String(xmlPath), XmlToEntityVo.class);
                System.out.println(JSONObject.toJSONString(xmlToEntityVo));
//                QueryWrapper<BusiBidderInfo> queryWrapper=new QueryWrapper<BusiBidderInfo>();
//                queryWrapper.eq("project_id",byIdBusiBiddingRecord.getProjectId());
//                queryWrapper.eq("bidder_id",byIdBusiBiddingRecord.getBiddingId());
//                queryWrapper.eq("del_flag",0);//是否已删除
//                queryWrapper.eq("decode_flag",0);//是否已解密
//                List<BusiBidderInfo> list = iBusiBidderInfoService.list(queryWrapper);
//                if (list.size() == 1){
//                    BusiBidderInfo busiBidderInfo=list.get(0);
//                    busiBidderInfo.setDecodeFlag(1);
//                    iBusiBidderInfoService.updateById(busiBidderInfo);
//                }
                resultJsonObject.add(xmlToEntityVo.toVO());
            }else {
                return AjaxResult.error("查到多个投标文件！");
            }
        }
        System.out.println(resultJsonObject.size());

        return AjaxResult.success(resultJsonObject);
    }

    @Override
    public AjaxResult getProjectFileById(Long projectId) throws Exception {
        //获取响应文件
        List<BusiBiddingRecord> biddingRecords =
                iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("project_id", projectId));
        Map<Long,String> pdfPathMap=new HashMap<>();
//        for (BusiBiddingRecord biddingRecord : biddingRecords) {
//            //获取附件
//            List<Long> tempLong=new ArrayList<>();
//            tempLong.add(biddingRecord.getBiddingId());
//            Map<Long, List<BusiAttachment>> byBusiIds = iBusiAttachmentService.getByBusiIds(tempLong);
//                for (Long l : byBusiIds.keySet()) {
//                    if (Objects.equals(l, biddingRecord.getBiddingId())) {
//                        BusiAttachment attachment = byBusiIds.get(l).get(0);
//                        String fileNameWithoutExtension = attachment.getFileName().substring(0, attachment.getFileName().lastIndexOf('.'));
//                       // String newFileName = fileNameWithoutExtension + ".zip";
//                        BaseEntInfo entId = baseEntInfoService.getOne(new QueryWrapper<BaseEntInfo>().eq("ent_id", biddingRecord.getBidderId()));
//  /*                      if ( !Md5Utils.hash(biddingRecord.getSupplierKey()).equals(entId.getSecretKey())){
//                            return AjaxResult.error("解密密码错误");
//                        }*/
//                        String first16Chars = entId.getSecretKey().substring(0, 16); // 提取前16位字符
//                        String zip =ZipUtil.decryptUrlAndUnzip(attachment.getFilePath(),first16Chars );
//                        pdfPathMap.put(biddingRecord.getBidderId(),zip);
//                    }
//                }
//            }
        List<DocResponseEntInfo> docResponseEntInfoList = docResponseEntInfoService.selectByProject(projectId, false);
        for (DocResponseEntInfo info : docResponseEntInfoList) {
            pdfPathMap.put(info.getEntId(),info.getPdfPath());
        }

   //         List<Long> biddingIds = biddingRecords.stream().map(BusiBiddingRecord::getBiddingId).collect(Collectors.toList());
        //附件信息
  //      Map<Long, List<BusiAttachment>> byBusiIds = iBusiAttachmentService.getByBusiIds(biddingIds);
     /*   //解密响应文件
        for (Long l : byBusiIds.keySet()) {
            BusiAttachment attachment = byBusiIds.get(l).get(0);
            String fileNameWithoutExtension = attachment.getFileName().substring(0, attachment.getFileName().lastIndexOf('.'));
            for (BusiBiddingRecord biddingRecord : biddingRecords) {
                if (Objects.equals(l, biddingRecord.getBiddingId())){
                    String newFileName = fileNameWithoutExtension + ".zip";
                    BaseEntInfo entId = baseEntInfoService.getOne(new QueryWrapper<BaseEntInfo>().eq("ent_id", l));
                    if ( !Md5Utils.hash(biddingRecord.getSupplierKey()).equals(entId.getSecretKey())){
                        return AjaxResult.error("解密密码错误");
                    }
                    String first16Chars = entId.getSecretKey().substring(0, 16); // 提取前16位字符
                    String zip =ZipUtil.decrypt(attachment.getFilePath(),newFileName,first16Chars );
                    String dirPath= ZipUtil.unzip(zip);
                    String pdfPath=dirPath+"/responseBidTxt.pdf";
                    pdfPathMap.put(l,pdfPath);
                }
            }
        }

*/

        Map<String,Object> map=new HashMap();
        map.put("file",pdfPathMap);
        //获取采购文件
        BusiTenderNotice tenderNoticeByProjectId = busiTenderNoticeService.getTenderNoticeByProjectId(projectId);
        for(BusiAttachment attachment : tenderNoticeByProjectId.getAttachments()){
            if(attachment.getFileType().equals("5")){
                map.put("tenderNoticeFilePath",attachment.getFilePath());
                break;
            }
        }
        return   AjaxResult.success(map);

    }

    @Override
    public AjaxResult updateAmountByProject(Long projectId) throws Exception {
        BigDecimal bd = new BigDecimal(0.00);
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
        for (BusiBidderInfo info : busiBidderInfos) {
            try {
                DocResponseEntInfo entInfo = docResponseEntInfoService.selectByProjectAndBidder(info.getProjectId(), info.getBidderId());
                if (entInfo != null && entInfo.getDetailMap() != null && entInfo.getDetailMap().get("kbylb") != null && !entInfo.getDetailMap().get("kbylb").isEmpty()) {
                    DocResponseEntDetail kbylb = entInfo.getDetailMap().get("kbylb").get(0);
                    if (StringUtils.isNoneBlank(kbylb.getDetailContent())) {
                        bd = JSONObject.parse(kbylb.getDetailContent()).getBigDecimal("bidPrice");
                        BusiBiddingRecord b = iBusiBiddingRecordService.selectByProjectAndBidder(info.getProjectId(), info.getBidderId());
                        b.setBidAmount(bd);
                        iBusiBiddingRecordService.updateById(b);
                    }

                    //将未解密成功的供应商置为无效状态
                    if(info.getDecodeFlag()==0){
                        info.setIsAbandonedBid(1);
                        busiBidderInfoService.updateById(info);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
