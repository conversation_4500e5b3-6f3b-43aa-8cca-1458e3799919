package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.mapper.BusiCancelProjectMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.enums.ZcxhEnum;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.BaseUtil;
import com.ruoyi.utils.PdfUtil;
import com.ruoyi.utils.ZcxhUtil;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 取消采购项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class BusiCancelProjectServiceImpl extends ServiceImpl<BusiCancelProjectMapper, BusiCancelProject> implements IBusiCancelProjectService {
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiAttachmentService busiAttachmentService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    private IBusiExtractExpertApplyService busiExtractExpertApplyService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IBusiBidOpeningService busiBidOpeningService;
    @Autowired
    private IBusiBidEvaluationService bidEvaluationService;
    @Autowired
    private IBusiWinningBidderNoticeService busiWinningBidderNoticeService;
    @Autowired
    private IBusiWinningBidderAdviceNoteService busiWinningBidderAdviceNoteService;
    @Autowired
    private IBusiExpertTransactionContractService busiExpertTransactionContractService;
    @Resource
    private PdfUtil pdfUtil;
    @Autowired
    private AttachmentUtil attachmentUtil;
    @Autowired
    private ZcxhUtil zcxhUtil;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;

    @Autowired
    private IBusiAuditProcessService auditProcessService;
    @Value("${extractcode.username}")
    String username;
    @Value("${extractcode.thirdPartySecret}")
    String thirdPartySecret;
    @Value("${extractcode.password}")
    String password;
    @Value("${extractcode.url}")
    String tokenUrl;

    @Value("${extractcode.deleteExtract}")
    String deleteExtract;

    /**
     * 查询取消采购项目列表
     *
     * @param busiCancelProject 取消采购项目
     * @return 取消采购项目
     */
    @Override
    public List<BusiCancelProject> selectList(BusiCancelProject busiCancelProject) {
        QueryWrapper<BusiCancelProject> busiCancelProjectQueryWrapper = new QueryWrapper<>();
        BaseUtil.checkUser(busiCancelProjectQueryWrapper, busiCancelProject);
        busiCancelProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiCancelProject.getProjectId()), "project_id", busiCancelProject.getProjectId());
        busiCancelProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiCancelProject.getCancelReason()), "cancel_reason", busiCancelProject.getCancelReason());
        String beginCancelDate = busiCancelProject.getParams().get("beginCancelDate") != null ? busiCancelProject.getParams().get("beginCancelDate") + "" : "";
        String endCancelDate = busiCancelProject.getParams().get("endCancelDate") + "" != null ? busiCancelProject.getParams().get("endCancelDate") + "" : "";
        busiCancelProjectQueryWrapper.between(ObjectUtil.isNotEmpty(beginCancelDate) && ObjectUtil.isNotEmpty(endCancelDate), "cancel_date", beginCancelDate, endCancelDate);
        busiCancelProjectQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiCancelProject.getParams().get("dataScope")),
                busiCancelProject.getParams().get("dataScope") + ""
        );
        List<BusiCancelProject> list = list(busiCancelProjectQueryWrapper);
        for (BusiCancelProject cancelProject : list) {
            //获取项目
            BusiTenderProject byId = iBusiTenderProjectService.getById(cancelProject.getProjectId());
            if (Objects.nonNull(byId)) {
                cancelProject.setProject(byId);
            }
            cancelProject.setLastAuditProcess(auditProcessService.selectLast(cancelProject.getCancelId()));
        }
        return list;
    }

    @Override
    public AjaxResult saveCancelProject(BusiCancelProject busiCancelProject) throws IOException {
        busiCancelProject.setCancelDate(new Date());
        busiCancelProject.setBusiState(1);
        boolean save = saveOrUpdate(busiCancelProject);
        auditProcessService.saveInfo(busiCancelProject.getCancelId(), AuditBusiTypeEnum.CANCEL_PROJECT, 1, "提交", "提交", 1);

        // 处理附件保存逻辑
        busiAttachmentService.deleteByBusiId(busiCancelProject.getCancelId());
        List<BusiAttachment> attachments = busiCancelProject.getAttachments();
        if (!attachments.isEmpty()){
            List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
                item.setBusiId(busiCancelProject.getCancelId());
                item.setDelFlag(DelFlagStatus.OK.getCode());
                item.setCreateBy(busiCancelProject.getCreateBy());
                item.setUpdateBy(busiCancelProject.getUpdateBy());
                item.setUpdateTime(busiCancelProject.getUpdateTime());
                item.setCreateTime(busiCancelProject.getCreateTime());
                return item;
            }).collect(Collectors.toList());

            // 保存附件
            boolean attachmentSaveSuccess = busiAttachmentService.saveBatch(batchSaveList);
            if (!attachmentSaveSuccess) {
                throw  new RuntimeException("附件保存失败！");
            }
        }
        busiCancelProject.setCancelContent(createContent(busiCancelProject));
        updateById(busiCancelProject);
        return AjaxResult.success("新增取消公告申请成功");
    }

    @Override
    public AjaxResult approveRequest(BusiCancelProject busiCancelProject) throws IOException {
        //修改关闭项目状态
        UpdateWrapper<BusiCancelProject> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("cancel_id", busiCancelProject.getCancelId());
        //判断审批状态
        if (busiCancelProject.getAuditResult()) {
            auditProcessService.saveInfo(busiCancelProject.getCancelId(), AuditBusiTypeEnum.CANCEL_PROJECT, 1, "通过", busiCancelProject.getAuditRemark(), 10);
            updateWrapper.set("busi_state", 10);

            //修改项目状态
            BusiTenderProject byId = iBusiTenderProjectService.getSubById(busiCancelProject.getProjectId());
            if (null != byId) {
                byId.setProjectStatus(-1);
                iBusiTenderProjectService.updateById(byId);
            }
            //修改公告信息
            ArrayList<Long> projectIds = new ArrayList<>();
            projectIds.add(busiCancelProject.getProjectId());
            NoticeInfoAndIdsVo tenderNotisByProjectId = iBusiTenderNoticeService.getTenderNotisByProjectId(projectIds);

            if (null != tenderNotisByProjectId && tenderNotisByProjectId.getNoticeIds() != null && !tenderNotisByProjectId.getNoticeIds().isEmpty()) {
                iBusiTenderNoticeService.removeByIds(tenderNotisByProjectId.getNoticeIds());
            } else {
                // 如果条件不满足，执行以下代码
                System.out.println("获取的 tenderNotisByProjectId 对象为 null 或者 noticeIds 集合为空");
            }
            BusiExtractExpertApply extractExpertApply =
                    busiExtractExpertApplyService.getOne(new QueryWrapper<BusiExtractExpertApply>().eq("project_id", busiCancelProject.getProjectId()));
            if (extractExpertApply != null && extractExpertApply.getApplyId() != null) {
                List<BusiExtractExpertResult> busiExtractExpertResults =
                        busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>().eq("apply_id", extractExpertApply.getApplyId()));
                if (busiExtractExpertResults!=null && !busiExtractExpertResults.isEmpty()) {
                    Set<Long> resultIds = busiExtractExpertResults.stream()
                            .map(BusiExtractExpertResult::getResultId)
                            .collect(Collectors.toSet());
                    busiExtractExpertResultService.removeByIds(resultIds);
                    OkHttpClient client = new OkHttpClient().newBuilder()
                            .build();
                    MediaType mediaType = MediaType.parse("application/json");
                    RequestBody body = RequestBody.create(mediaType, "[" + busiCancelProject.getProjectId() + "]");
                    Request request = new Request.Builder()
                            .url(deleteExtract)
                            .method("POST", body)
                            .addHeader("token", getToken())
                            .addHeader("Content-Type", "application/json")
                            .addHeader("Cookie", "JSESSIONID=C67EE2FCCCFA74425CEDCBD01FE50067")
                            .build();
                    Response response = client.newCall(request).execute();
                    JSONObject jsonObject = JSON.parseObject(response.body().string());
                    if (!jsonObject.get("msg").equals("操作成功")) {
                        throw new RuntimeException("第三方取消专家占用失败");
                    }
                }
            }


            send2Zcxh(busiCancelProject, byId);
        } else {
            auditProcessService.saveInfo(busiCancelProject.getCancelId(), AuditBusiTypeEnum.CANCEL_PROJECT, 0, "退回", busiCancelProject.getAuditRemark(), 0);
            updateWrapper.set("busi_state", 0);
        }
        this.update(updateWrapper);
        // auditProcessService.saveInfo(busiCancelProject.getCancelId(), AuditBusiTypeEnum.CANCELPROJECTAUDIT.getCode(), 1, "不通过", busiCancelProject.getAuditRemark(), 2);

        //删除开评标情况
        QueryWrapper<BusiBidOpening> bidOpeningQuery = new QueryWrapper<>();
        bidOpeningQuery.eq("project_id", busiCancelProject.getProjectId());
        busiBidOpeningService.remove(bidOpeningQuery);
        QueryWrapper<BusiBidEvaluation> bidEvaluationQuery = new QueryWrapper<>();
        bidEvaluationQuery.eq("project_id", busiCancelProject.getProjectId());
        bidEvaluationService.remove(bidEvaluationQuery);

        //删除结果公告
        QueryWrapper<BusiWinningBidderNotice> winningBidderNoticeQueryWrapper = new QueryWrapper<>();
        winningBidderNoticeQueryWrapper.eq("project_id", busiCancelProject.getProjectId());
        busiWinningBidderNoticeService.remove(winningBidderNoticeQueryWrapper);

        //删除中标通知书
        QueryWrapper<BusiWinningBidderAdviceNote> winningBidderAdviceNoteQueryWrapper = new QueryWrapper<>();
        winningBidderAdviceNoteQueryWrapper.eq("project_id", busiCancelProject.getProjectId());
        busiWinningBidderAdviceNoteService.remove(winningBidderAdviceNoteQueryWrapper);

        //删除合同
        QueryWrapper<BusiExpertTransactionContract> expertTransactionContractQueryWrapper = new QueryWrapper<>();
        expertTransactionContractQueryWrapper.eq("project_id", busiCancelProject.getProjectId());
        busiExpertTransactionContractService.remove(expertTransactionContractQueryWrapper);


        return AjaxResult.success();
    }


    //推送至政采协会
    private void send2Zcxh(BusiCancelProject busiCancelProject, BusiTenderProject project){
        JSONObject sendData = new JSONObject();
        sendData.put("noticeContent", busiCancelProject.getCancelContent());
        sendData.put("noticeTitle", busiCancelProject.getProjectName()+"—终止公告");
        sendData.put("purchasingUnit", project.getTendererName());
        sendData.put("noticeSource", "限额以下");
        sendData.put("noticeType", "终止（废标）公告");
        sendData.put("publishingTime", busiCancelProject.getCancelDate().getTime());
        sendData.put("thirdId", busiCancelProject.getCancelId());
        sendData.put("tenderType", project.getProjectTypeName());
        if(null!=project.getProjectArea()){
            BaseTreeData area = baseTreeDataService.getById(project.getProjectArea());
            project.setProjectAreaName(area.getName());
            project.setProjectAreaCode(area.getCode());
            sendData.put("projectArea",area.getName());
            sendData.put("projectAreaCode",area.getCode());
        }
//        sendData.put("projectArea",project.getProjectAreaName());
//        sendData.put("projectAreaCode",project.getProjectAreaCode());
        sendData.put("projectType", project.getProjectTypeName());
        sendData.put("projectIndustry", project.getProjectIndustryName());
        sendData.put("noticeUrl", "https://xeyx.hbszfcgxh.com:10443");
        try {
            zcxhUtil.send2Zcxh(sendData, ZcxhEnum.NOTICE.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public AjaxResult getCancelProjectById(Long cancelId) {
        BusiCancelProject cancelProject = getById(cancelId);
        //获取项目
        BusiTenderProject byId = iBusiTenderProjectService.getById(cancelProject.getProjectId());
        if (Objects.nonNull(byId)) {
            cancelProject.setProject(byId);
        }
        cancelProject.setAudit(auditProcessService.checkAuditRole(cancelId));

        cancelProject.setAuditProcessList(auditProcessService.selectList(cancelId));

        cancelProject.setAttachments(busiAttachmentService.selectByBusiId(cancelId));

        return AjaxResult.success(cancelProject);
    }

    public String createContent(BusiCancelProject cancelNotice){
        BusiTenderProject byId = iBusiTenderProjectService.getById(cancelNotice.getProjectId());
        return createContent(cancelNotice, byId);
    }
    @Override
    public String createContent(BusiCancelProject cancelNotice, BusiTenderProject tenderProject){
        BusiTenderNotice tenderNotice = iBusiTenderNoticeService.selectByProject(tenderProject.getProjectId());
        Map<String, Object> map = new HashMap<>();
        map.put("cancelNotice", cancelNotice);
        map.put("tenderProject", tenderProject);
        map.put("tenderNotice", tenderNotice);
        map.put("fileItems", attachmentUtil.getAttachmentList(cancelNotice.getCancelId(), ProcessEnum.CANCEL_NOTICE.getAttachmentCode()));
        String ftlPath = "portal_templates/取消公告.ftl";
        try {
            return pdfUtil.toHtml(map, ftlPath);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getToken() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\"t\":" + System.currentTimeMillis() + ",\r\n\"username\":\"" + username + "\",\r\n\"password\":\"" + password + "\",\r\n\"thirdPartySecret\":\"" + thirdPartySecret + "\"\r\n}");
        Request request = new Request.Builder()
                .url(tokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.body() != null) {
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            return jsonObject.getString("token");
        }
        return "";
    }
}
