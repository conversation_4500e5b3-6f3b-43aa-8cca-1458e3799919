package com.ruoyi.busi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExpertInfoMapper;
import com.ruoyi.busi.domain.BusiExpertInfo;
import com.ruoyi.busi.service.IBusiExpertInfoService;

/**
 * 专家信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class BusiExpertInfoServiceImpl extends ServiceImpl<BusiExpertInfoMapper, BusiExpertInfo> implements IBusiExpertInfoService {
    /**
     * 查询专家信息列表
     *
     * @param busiExpertInfo 专家信息
     * @return 专家信息
     */
    @Override
    public List<BusiExpertInfo> selectList(BusiExpertInfo busiExpertInfo) {
        QueryWrapper<BusiExpertInfo> busiExpertInfoQueryWrapper = getBusiExpertInfoQueryWrapper(busiExpertInfo);
        return list(busiExpertInfoQueryWrapper);
    }

    private QueryWrapper<BusiExpertInfo> getBusiExpertInfoQueryWrapper(BusiExpertInfo busiExpertInfo) {
        QueryWrapper<BusiExpertInfo> busiExpertInfoQueryWrapper = new QueryWrapper<>();
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertAccountCode()),"expert_account_code",busiExpertInfo.getExpertAccountCode());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertSex()),"expert_sex", busiExpertInfo.getExpertSex());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertPoliticalOutlook()),"expert_political_outlook", busiExpertInfo.getExpertPoliticalOutlook());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertGraduationSchool()),"expert_graduation_school", busiExpertInfo.getExpertGraduationSchool());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertDuty()),"expert_duty", busiExpertInfo.getExpertDuty());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertPhone()),"expert_phone", busiExpertInfo.getExpertPhone());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertMailAddress()),"expert_mail_address", busiExpertInfo.getExpertMailAddress());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertWorkYears()),"expert_work_years", busiExpertInfo.getExpertWorkYears());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertCertificateType()),"expert_certificate_type", busiExpertInfo.getExpertCertificateType());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertBirthday()),"expert_birthday", busiExpertInfo.getExpertBirthday());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertDegree()),"expert_degree", busiExpertInfo.getExpertDegree());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertGraduactionTime()),"expert_graduaction_time", busiExpertInfo.getExpertGraduactionTime());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertTitles()),"expert_titles", busiExpertInfo.getExpertTitles());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertEmail()),"expert_email", busiExpertInfo.getExpertEmail());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertNation()),"expert_nation", busiExpertInfo.getExpertNation());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertPhoto()),"expert_photo", busiExpertInfo.getExpertPhoto());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertMajorStudy()),"expert_major_study", busiExpertInfo.getExpertMajorStudy());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertPlace()),"expert_place", busiExpertInfo.getExpertPlace());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertTitlesGrade()),"expert_titles_grade", busiExpertInfo.getExpertTitlesGrade());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertPostalCode()),"expert_postal_code", busiExpertInfo.getExpertPostalCode());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertOpinion()),"expert_opinion", busiExpertInfo.getExpertOpinion());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertStatus()),"expert_status", busiExpertInfo.getExpertStatus());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertOpeningBank()),"expert_opening_bank", busiExpertInfo.getExpertOpeningBank());
        busiExpertInfoQueryWrapper.like(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertAccountName()),"expert_account_name", busiExpertInfo.getExpertAccountName());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertEvaluationItems()),"expert_evaluation_items", busiExpertInfo.getExpertEvaluationItems());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertCertificateCode()),"expert_certificate_code", busiExpertInfo.getExpertCertificateCode());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertName()),"expert_name", busiExpertInfo.getExpertName());
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertOrganization()),"expert_organization", busiExpertInfo.getExpertOrganization());
        busiExpertInfoQueryWrapper.likeLeft(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertIndustryClassify()),"expert_industry_classify", busiExpertInfo.getExpertIndustryClassify());

        //抽取条件
        busiExpertInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertType()),"expert_type",busiExpertInfo.getExpertType());
        busiExpertInfoQueryWrapper.likeRight(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertMainEvaluatArea()),"expert_main_evaluat_area", busiExpertInfo.getExpertMainEvaluatArea());
        busiExpertInfoQueryWrapper.likeRight(ObjectUtil.isNotEmpty(busiExpertInfo.getExpertCareer()),"expert_career", busiExpertInfo.getExpertCareer());
        Map<String, Object> params = busiExpertInfo.getParams();
        busiExpertInfoQueryWrapper.last(params.containsKey("extractExpertNumber")," limit "+params.get("extractExpertNumber"));
        //排除条件
        busiExpertInfoQueryWrapper.notIn(params.containsKey("evadeExpertNames"),"expert_name", params.get("evadeExpertNames"));
        busiExpertInfoQueryWrapper.notIn(params.containsKey("evadeExpertOrganizations"),"expert_organization", params.get("evadeExpertOrganizations"));
        return busiExpertInfoQueryWrapper;
    }

    /**
     * 导入用户数据
     *
     * @param list 专家专业分类数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importExpertInfo(List<BusiExpertInfo> list, boolean isUpdateSupport, String operName) {

        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入专家数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        Date date = new Date();
        for (BusiExpertInfo expertInfo : list)
        {
            try{
                BusiExpertInfo expertInfo1 = getById(expertInfo.getExpertId());
                if(expertInfo1 == null){
                    expertInfo.setCreateBy(operName);
                    expertInfo.setUpdateBy(operName);
                    expertInfo.setCreateTime(date);
                    expertInfo.setUpdateTime(date);
                    save(expertInfo);
                }else if(isUpdateSupport){
                    BeanUtil.copyProperties(expertInfo,expertInfo1);
                    expertInfo1.setUpdateBy(operName);
                    expertInfo1.setUpdateTime(date);
                    updateById(expertInfo1);
                }
                successNum++;
            }catch (Exception e){
                failureNum++;
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<BusiExpertInfo> extractExpert(BusiExpertInfo searchExpertInfo) {
        QueryWrapper<BusiExpertInfo> busiExpertInfoQueryWrapper = getBusiExpertInfoQueryWrapper(searchExpertInfo);
        return list(busiExpertInfoQueryWrapper);
    }


}
