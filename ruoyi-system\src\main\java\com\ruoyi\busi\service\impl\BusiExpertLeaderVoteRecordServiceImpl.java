package com.ruoyi.busi.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiExtractExpertApplyService;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExpertLeaderVoteRecordMapper;
import com.ruoyi.busi.domain.BusiExpertLeaderVoteRecord;
import com.ruoyi.busi.service.IBusiExpertLeaderVoteRecordService;

/**
 * 专家组长投票记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class BusiExpertLeaderVoteRecordServiceImpl extends ServiceImpl<BusiExpertLeaderVoteRecordMapper, BusiExpertLeaderVoteRecord> implements IBusiExpertLeaderVoteRecordService {

    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;

    /**
     * 查询专家组长投票记录列表
     *
     * @param busiExpertLeaderVoteRecord 专家组长投票记录
     * @return 专家组长投票记录
     */
    @Override
    public List<BusiExpertLeaderVoteRecord> selectList(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        QueryWrapper<BusiExpertLeaderVoteRecord> busiExpertLeaderVoteRecordQueryWrapper = new QueryWrapper<>();
                        busiExpertLeaderVoteRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertLeaderVoteRecord.getVoterExpertId()),"voter_expert_id",busiExpertLeaderVoteRecord.getVoterExpertId());
                        busiExpertLeaderVoteRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertLeaderVoteRecord.getCandidateExpertId()),"candidate_expert_id",busiExpertLeaderVoteRecord.getCandidateExpertId());
                        busiExpertLeaderVoteRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertLeaderVoteRecord.getVoteTime()),"vote_time",busiExpertLeaderVoteRecord.getVoteTime());
                        busiExpertLeaderVoteRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExpertLeaderVoteRecord.getGroupId()),"group_id",busiExpertLeaderVoteRecord.getGroupId());
            busiExpertLeaderVoteRecordQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiExpertLeaderVoteRecord.getParams().get("dataScope")),
        busiExpertLeaderVoteRecord.getParams().get("dataScope")+""
        );
        return list(busiExpertLeaderVoteRecordQueryWrapper);
    }

    @Override
    public AjaxResult saveBusiExpertLeaderVoteRecord(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        BusiExpertLeaderVoteRecord one = this.getOne(new QueryWrapper<BusiExpertLeaderVoteRecord>()
                .eq("apply_id", busiExpertLeaderVoteRecord.getApplyId())
                .eq("voter_expert_id", busiExpertLeaderVoteRecord.getVoterExpertId())
        );
        if (null == one  ) {
            this.save(busiExpertLeaderVoteRecord);
        } else {
            busiExpertLeaderVoteRecord.setVoteRecordId(one.getVoteRecordId());
            this.saveOrUpdate(busiExpertLeaderVoteRecord);
        }
        return AjaxResult.success(busiExpertLeaderVoteRecord);
    }

    public  AjaxResult getProjectLeaderVoteRecord(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord){
        List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>()
                        .eq("apply_id", busiExpertLeaderVoteRecord.getApplyId())
                //.eq("project_id",busiExpertLeaderVoteRecord.getApplyId())
        );
        // 提取resultId集合
        Set<Long> resultIds = busiExtractExpertResults.stream()
                .map(BusiExtractExpertResult::getResultId)
                .collect(Collectors.toSet());

        // 构建查询条件，确保voter_expert_id在resultIds集合中
        List<BusiExpertLeaderVoteRecord> voteRecords = this.list(new QueryWrapper<BusiExpertLeaderVoteRecord>()
                .eq("apply_id", busiExpertLeaderVoteRecord.getApplyId())
                .in("voter_expert_id", resultIds)
        );
        List<BusiExpertLeaderVoteRecord> expertLeaderVoteRecords = this.list(new QueryWrapper<BusiExpertLeaderVoteRecord>()
                .eq("apply_id", busiExpertLeaderVoteRecord.getApplyId()));
        if (expertLeaderVoteRecords.size()==busiExtractExpertResults.size()){
            // 使用Map来统计每个候选专家的票数
            Map<Long, Integer> voteCounts = new HashMap<>();
            // 遍历投票记录，统计票数
            for (BusiExpertLeaderVoteRecord record : expertLeaderVoteRecords) {
                Long candidateExpertId = record.getCandidateExpertId();
                voteCounts.put(candidateExpertId, voteCounts.getOrDefault(candidateExpertId, 0) + 1);
            }
            // 找出票数最多的候选专家ID
            Long leaderExpertId = voteCounts.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(null);
            // 如果没有找到票数最多的专家，则返回null
            if (leaderExpertId == null) {
                return null;
            }
            BusiExtractExpertResult extractExpertResultServiceById = busiExtractExpertResultService.getById(leaderExpertId);
            extractExpertResultServiceById.setExpertLeader(1);
            busiExtractExpertResultService.saveOrUpdate(extractExpertResultServiceById);
            return AjaxResult.success(extractExpertResultServiceById);
        }else {
            return    AjaxResult.success("请等待其他专家选择专家组长");
        }
    }

}
