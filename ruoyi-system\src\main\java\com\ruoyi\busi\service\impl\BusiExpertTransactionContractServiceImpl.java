package com.ruoyi.busi.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.utils.BaseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExpertTransactionContractMapper;

/**
 * 成交合同Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Service
public class BusiExpertTransactionContractServiceImpl extends ServiceImpl<BusiExpertTransactionContractMapper, BusiExpertTransactionContract> implements IBusiExpertTransactionContractService {
    @Autowired
    IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiBidderInfoService iBusiBidderInfoService;
    @Autowired
    private IBusiProcessInfoService busiProcessInfoService;
    /**
     * 查询成交合同列表
     *
     * @param busiExpertTransactionContract 成交合同
     * @return 成交合同
     */
    @Override
    public List<BusiExpertTransactionContract> selectList(BusiExpertTransactionContract busiExpertTransactionContract) {
        QueryWrapper<BusiExpertTransactionContract> busiExpertTransactionContractQueryWrapper = new QueryWrapper<>();
        BaseUtil.checkUser(busiExpertTransactionContractQueryWrapper, busiExpertTransactionContract);
        String beginContractSigningDate = busiExpertTransactionContract.getParams().get("beginContractSigningDate")!=null?busiExpertTransactionContract.getParams().get("beginContractSigningDate")+"":"";
        String endContractSigningDate = busiExpertTransactionContract.getParams().get("endContractSigningDate")+""!=null?busiExpertTransactionContract.getParams().get("endContractSigningDate")+"":"";
        busiExpertTransactionContractQueryWrapper.between(ObjectUtil.isNotEmpty(beginContractSigningDate) && ObjectUtil.isNotEmpty(endContractSigningDate), "contract_signing_date", beginContractSigningDate , endContractSigningDate);

        List<BusiExpertTransactionContract> list = list(busiExpertTransactionContractQueryWrapper);
        if (!list.isEmpty()){
            List<Long> collected = list.stream().map(item -> {
                return item.getContractId();
            }).collect(Collectors.toList());

            //获取附件
            List<BusiAttachment> attachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().in("busi_id", collected));
            if (!attachments.isEmpty()){
                Map<Long, List<BusiAttachment>> attachmentMap = attachments.stream()
                        .collect(Collectors.groupingBy(BusiAttachment::getAttachmentId));
                list.forEach(item -> {
                    item.setAttachments(attachmentMap.get(item.getProjectId()));
                });
            }
            for (BusiExpertTransactionContract expertTransactionContract : list) {
                //获取项目
                BusiTenderProject byId = iBusiTenderProjectService.getById(expertTransactionContract.getProjectId());
                if (Objects.nonNull(byId)) {
                    expertTransactionContract.setProject(byId);
                }
                //获取中标信息
                BusiBidderInfo one = iBusiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>().eq("is_win", 1).eq("project_id", expertTransactionContract.getProjectId()));
                if (Objects.nonNull(one)) {
                    System.out.println("one不为空");
                    expertTransactionContract.setBusiBidderInfo(one);
                }
            }

        }
        return list;
    }

    @Override
    public AjaxResult saveTransactionContract(BusiExpertTransactionContract busiExpertTransactionContract) {
        boolean save = save(busiExpertTransactionContract);
        if (save){
            BusiTenderProject byId = iBusiTenderProjectService.getById(busiExpertTransactionContract.getProjectId());
            if (null!=byId){
                byId.setProjectStatus(80);
                iBusiTenderProjectService.updateById(byId);
            }
            // 处理附件保存逻辑
            List<BusiAttachment> attachments = busiExpertTransactionContract.getAttachments();
            if (!attachments.isEmpty()){
                List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
                    item.setBusiId(busiExpertTransactionContract.getContractId());
                    item.setDelFlag(DelFlagStatus.OK.getCode());
                    item.setCreateBy(busiExpertTransactionContract.getCreateBy());
                    item.setUpdateBy(busiExpertTransactionContract.getUpdateBy());
                    item.setUpdateTime(busiExpertTransactionContract.getUpdateTime());
                    item.setCreateTime(busiExpertTransactionContract.getCreateTime());
                    return item;
                }).collect(Collectors.toList());
                // 保存附件
                boolean attachmentSaveSuccess = iBusiAttachmentService.saveBatch(batchSaveList);
                if (!attachmentSaveSuccess) {
                    throw  new RuntimeException("附件保存失败！");
                }

            }

        }else {
            throw  new RuntimeException("保存失败！");
        }
        return AjaxResult.success(busiExpertTransactionContract);
    }

    @Override
    public BusiExpertTransactionContract getById(Serializable id) {
        BusiExpertTransactionContract byId = super.getById(id);
        //获取附件
        List<BusiAttachment> attachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().in("busi_id", byId.getContractId()));
        if (!attachments.isEmpty()){
            byId.setAttachments(attachments);
        }
        //获取项目
        BusiTenderProject project = iBusiTenderProjectService.getById(byId.getProjectId());
        if (Objects.nonNull(project)) {
            byId.setProject(project);
        }
        //获取中标信息
        BusiBidderInfo one = iBusiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>().eq("is_win", 1).eq("project_id", byId.getProjectId()));
        if (Objects.nonNull(one)) {
            System.out.println("one不为空");
            byId.setBusiBidderInfo(one);
        }
        return byId;
    }





}
