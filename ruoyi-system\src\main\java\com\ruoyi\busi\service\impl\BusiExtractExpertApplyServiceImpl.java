package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.Yzpw;
import com.ruoyi.busi.mapper.BusiExtractExpertApplyMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.utils.UniqueIDGenerator;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.ParamVo;
import com.ruoyi.utils.RandomizingVo;
import com.ruoyi.utils.RestExtractParam;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 专家抽取申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiExtractExpertApplyServiceImpl extends ServiceImpl<BusiExtractExpertApplyMapper, BusiExtractExpertApply> implements IBusiExtractExpertApplyService {
    @Autowired
    private IBusiExtractExpertResultService iBusiExtractExpertResultService;
    @Autowired
    private IBusiExtractExpertEvadeService iBusiExtractExpertEvadeService;
    @Autowired
    private IBusiExtractExpertGroupService iBusiExtractExpertGroupService;
    @Autowired
    private IBusiExpertInfoService iBusiExpertInfoService;
    @Autowired
    private IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiVenueOccupyService iBusiVenueOccupyService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;

    @Value("${extractcode.username}")
    String username;
    @Value("${extractcode.thirdPartySecret}")
    String thirdPartySecret;
    @Value("${extractcode.password}")
    String password;
    @Value("${extractcode.url}")
    String tokenUrl;
    @Value("${extractcode.chouQu}")
    String chouqu;
    @Value("${extractcode.resetExtract}")
    String resetExtract;
    @Value("${extractcode.deleteExtract}")
    String deleteExtract;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询专家抽取申请列表
     *
     * @param busiExtractExpertApply 专家抽取申请
     * @return 专家抽取申请
     */
    @Override
    public List<BusiExtractExpertApply> selectList(BusiExtractExpertApply busiExtractExpertApply) {
        List<BusiExtractExpertApply> list = new ArrayList<>();
        BusiTenderProject projectQuery = new BusiTenderProject();
//        projectQuery.getParams().put("isScope", 1);
        projectQuery.setDelFlag(0);
        List<BusiTenderProject> projects = iBusiTenderProjectService.selectList(projectQuery);

        if (!projects.isEmpty()) {
            List<Long> projectIds = projects.stream().map(BusiTenderProject::getProjectId).collect(Collectors.toList());
            Map<Long, BusiTenderProject> projectMap = projects.stream().collect(Collectors.toMap(BusiTenderProject::getProjectId, project -> project));

            QueryWrapper<BusiExtractExpertApply> busiExtractExpertApplyQueryWrapper = new QueryWrapper<>();
            busiExtractExpertApplyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertApply.getProjectId()), "project_id", busiExtractExpertApply.getProjectId());
            busiExtractExpertApplyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertApply.getExpertNumber()), "expert_number", busiExtractExpertApply.getExpertNumber());
            busiExtractExpertApplyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertApply.getDelFlag()), "del_flag", busiExtractExpertApply.getDelFlag());
            String beginCreateTime = busiExtractExpertApply.getParams().get("beginCreateTime") != null ? busiExtractExpertApply.getParams().get("beginCreateTime") + "" : "";
            String endCreateTime = busiExtractExpertApply.getParams().get("endCreateTime") + "" != null ? busiExtractExpertApply.getParams().get("endCreateTime") + "" : "";
            busiExtractExpertApplyQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
            busiExtractExpertApplyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertApply.getCreateBy()), "create_by", busiExtractExpertApply.getCreateBy());
            String beginUpdateTime = busiExtractExpertApply.getParams().get("beginUpdateTime") != null ? busiExtractExpertApply.getParams().get("beginUpdateTime") + "" : "";
            String endUpdateTime = busiExtractExpertApply.getParams().get("endUpdateTime") + "" != null ? busiExtractExpertApply.getParams().get("endUpdateTime") + "" : "";
            busiExtractExpertApplyQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
            busiExtractExpertApplyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertApply.getUpdateBy()), "update_by", busiExtractExpertApply.getUpdateBy());
            busiExtractExpertApplyQueryWrapper.orderByDesc("create_time");
            busiExtractExpertApplyQueryWrapper.in("project_id", projectIds);
            list = list(busiExtractExpertApplyQueryWrapper);
            if (!list.isEmpty()) {
                for (BusiExtractExpertApply extractExpertApply : list) {
                    extractExpertApply.setProject(projectMap.get(extractExpertApply.getProjectId()));
                }
            }
        }
        return list;
    }

    @Transactional
    @Override
    public void saveHaveExtract(BusiExtractExpertApply busiExtractExpertApply) throws IOException {
        //修改项目
        //修改项目状态为20
        BusiTenderProject project = iBusiTenderProjectService.getById(busiExtractExpertApply.getProjectId());
        if (project != null && busiExtractExpertApply.getApplyId() == null&&busiExtractExpertApply.getExtractionType()==1) {
            project.setProjectStatus(30);
            iBusiTenderProjectService.updateById(project);
        }
        saveOrUpdate(busiExtractExpertApply);
        if (busiExtractExpertApply.getApplyMethod() == 0) {
            List<BusiExtractExpertEvade> evades = busiExtractExpertApply.getEvades();
            List<BusiExtractExpertGroup> groups = busiExtractExpertApply.getGroups();
            Long applyId = busiExtractExpertApply.getApplyId();
            if (ObjectUtil.isNotEmpty(evades)) {
                evades.forEach(item -> {
                    item.setApplyId(applyId);
                });
                //删除
                iBusiExtractExpertEvadeService.remove(new QueryWrapper<BusiExtractExpertEvade>().eq("apply_id", applyId));
                //新增
                iBusiExtractExpertEvadeService.saveOrUpdateBatch(evades);
            }
            if (ObjectUtil.isNotEmpty(groups)) {
                groups.forEach(item -> {
                    item.setApplyId(applyId);
                });
                //删除
                iBusiExtractExpertGroupService.remove(new QueryWrapper<BusiExtractExpertGroup>().eq("apply_id", applyId));
                //修改
                iBusiExtractExpertGroupService.saveOrUpdateBatch(groups);
            }
//            //检车专家是否已抽取
//            List<Long> applyIds = new ArrayList<>();
//            applyIds.add(applyId);
//            extract(applyIds, true);

            suiJiChouQu(busiExtractExpertApply);
        }
        else if (busiExtractExpertApply.getApplyMethod() == 1) {

            List<Long> projectIds = new ArrayList();
            projectIds.add(busiExtractExpertApply.getProjectId());
            BusiTenderNotice busiTenderNotice = iBusiTenderNoticeService.getOne(new QueryWrapper<BusiTenderNotice>()
                    .eq("del_flag", 0).eq("notice_stats", 1).eq("project_id", busiExtractExpertApply.getProjectId()));
            BusiVenueOccupy one = iBusiVenueOccupyService.getOne(new QueryWrapper<BusiVenueOccupy>().eq("venue_type", 2).eq("del_flag", 0).eq("notice_id", busiTenderNotice.getNoticeId()));
            //获取评审的开始结束时间
          //  Map<String, String> timeMap = getStartTimeEndTime(sdf.format(one.getOccupyStartTime()), 2);
            ParamVo paramVo = new ParamVo();
            //新增传给协会参数
            BusiTenderProject tenderProject = iBusiTenderProjectService.getById(busiExtractExpertApply.getProjectId());
            if(org.apache.commons.lang3.StringUtils.isNoneBlank(tenderProject.getTenderMode())) {
                //     byId.setTenderModeName(sysDictDataService.selectDictLabel("busi_tender_mode", tenderProject.getTenderMode()));
            }
            // byId.setProjectTypeName(sysDictDataService.selectDictLabel("busi_project_type", tenderProject.getProjectType()));
            // 新增传递项目信息参数
            paramVo.setProjectName(tenderProject.getProjectName());
            paramVo.setProjectCategory(sysDictDataService.selectDictLabel("busi_project_type", tenderProject.getProjectType()));//采购类别
            paramVo.setProcurementMethod(sysDictDataService.selectDictLabel("busi_tender_mode", tenderProject.getTenderMode()));//采购方式
            paramVo.setPurchaserName(tenderProject.getDisplayAgencyOrPurchaserName());//采购人名称
            paramVo.setBudgetAmount(tenderProject.getBudgetAmount());//预算金额
            paramVo.setPurchasingUnitContact(tenderProject.getDisplayAgencyOrPurchaserContactPerson());//联系人
            paramVo.setPurchasingUnitPhone(tenderProject.getDisplayAgencyOrPurchaserPhone());//联系电话
            if(null!=tenderProject.getProjectArea()) {
                BaseTreeData area = baseTreeDataService.getById(tenderProject.getProjectArea());
                //project.setProjectAreaName(area.getName());
                paramVo.setProjectAffiliatedDistrict(area.getName());//隶属县区
            }
            paramVo.setDescription(tenderProject.getProjectContent());//说明
            paramVo.setPackageQuantity(1);//包数量
            paramVo.setProjectStatus(2);//项目状态2
            paramVo.setProjectType(busiExtractExpertApply.getExtractionType() == null ? "未知类型" :
                    busiExtractExpertApply.getExtractionType() == 0 ? "论证" :
                            busiExtractExpertApply.getExtractionType() == 1 ? "评审" : "未知类型");//项目类型


            paramVo.setPsdd(one.getVenueName());
            SimpleDateFormat sdfsfm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            paramVo.setPsjtsj(sdfsfm.format(one.getOccupyStartTime()));
            paramVo.setProjectCode(busiExtractExpertApply.getProjectId());
           // if (busiExtractExpertApply.getExtractionType()==0){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Map<String, String> timeMap = getStartTimeEndTime(sdf.format(new Date()), busiExtractExpertApply.getOccupationTime());
                paramVo.setStartTime(timeMap.get("startTime"));
                paramVo.setEndTime(timeMap.get("endTime"));
          //  }else {
           //     paramVo.setStartTime(sdfsfm.format(one.getOccupyStartTime()));
           //     paramVo.setEndTime(sdfsfm.format(one.getOccupyEndTime()));
           // }
            List<BusiExtractExpertEvade> evades = busiExtractExpertApply.getEvades();
            if (ObjectUtil.isNotEmpty(evades)) {
                evades.forEach(item -> {
                    item.setApplyId(busiExtractExpertApply.getApplyId());
                });
                //删除
                iBusiExtractExpertEvadeService.remove(new QueryWrapper<BusiExtractExpertEvade>().eq("apply_id", busiExtractExpertApply.getApplyId()));
                //新增
                iBusiExtractExpertEvadeService.saveOrUpdateBatch(evades);
            }
            //规避的名称集合
            List<String> evadeNames = evades.stream()
                    .map(BusiExtractExpertEvade::getEvadeName)
                    .collect(Collectors.toList());
            String expertIds = busiExtractExpertApply.getResults().stream()
                    .map(BusiExtractExpertResult::getExpertId)
                    .map(String::valueOf) // 将Long转换为String
                    .collect(Collectors.joining(","));
            paramVo.setExclusionConditions(evadeNames);
            List<ParamVo.DemandsBean> demands = new ArrayList<>();
            ParamVo.DemandsBean demandsBean = new ParamVo.DemandsBean();
            demandsBean.setProId(busiExtractExpertApply.getProjectId());
            demandsBean.setQwsl(Integer.parseInt(busiExtractExpertApply.getExpertNumber()));
            demandsBean.setExpIds(expertIds);
            demandsBean.setCqfs("自主抽取");
            //随机id传组id
            //demandsBean.setId();
            demands.add(demandsBean);

            paramVo.setDemands(demands);
            try {
                OkHttpClient client = new OkHttpClient().newBuilder()
                        .build();
                RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(paramVo).toString());
                Request request = new Request.Builder()
                        .url(chouqu)
                        .method("POST", body)
                        .addHeader("token", getToken())
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Cookie", "JSESSIONID=071327329DCF80B72EA2FFFDB9396A46")
                        .build();
                try {
                    Response response = client.newCall(request).execute();
                    JSONObject jsonObject = JSON.parseObject(response.body().string());
                    if (!jsonObject.get("msg").equals("操作成功")) {
                        throw new RuntimeException(jsonObject.get("msg").toString());
                    } else {
                        List<RandomizingVo> randomizingVos = JSON.parseArray(jsonObject.get("data").toString(), RandomizingVo.class);
                        for (RandomizingVo randomizingVo : randomizingVos) {
                            for (BusiExtractExpertResult result : busiExtractExpertApply.getResults()) {
                                if (randomizingVo.getExpId() == result.getExpertId()) {
                                    result.setPhone(randomizingVo.getPhone());
                                    result.setCompany(randomizingVo.getCompany());
                                    result.setThirtyId(Long.parseLong(randomizingVo.getId() + ""));
                                    result.setApplyId(busiExtractExpertApply.getApplyId());
                                    result.setIsOwner(0);
                                    result.setSignStatus(0);
                                }
                                iBusiExtractExpertResultService.saveOrUpdate(result);
                            }
                        }

                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                busiExtractExpertApply.setApplyStatus(2);
                this.baseMapper.updateById(busiExtractExpertApply);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //检车专家是否已抽取
            //根据项目id 查询公告 得到 开评标时间
//            List<BusiExtractExpertResult> results1 = busiExtractExpertApply.getResults();
//            for (BusiExtractExpertResult busiExtractExpertResult : results1) {
//                busiExtractExpertResult.setApplyId(busiExtractExpertApply.getApplyId());
//            }
//            iBusiExtractExpertResultService.saveOrUpdateBatch(results1);

           /* SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            List<BusiExtractExpertResult> results = unifyDayResult(simpleDateFormat, busiExtractExpertApply);
            //判断本次提交专家是否在列表内
            Map<Long, BusiExtractExpertResult> expertResultMap = results1.stream()
                    .collect(Collectors.toMap(
                            BusiExtractExpertResult::getExpertId,
                            result -> result,
                            (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
            List<BusiExtractExpertResult> collect = results.stream().filter(item -> expertResultMap.containsKey(item.getExpertId())).collect(Collectors.toList());
            if(null!=collect && collect.size()>0){
                List<String> msg = new ArrayList<>();
                //存在重复的
                collect.forEach(item->{
                        msg.add(StringUtils.format("抽取申请【{}】，专家【{}】已安排，请重新选择专家。 ", busiExtractExpertApply.getApplyId()
                                , item.getExpertName()));
                });
            }else{

            }*/
            //新增
        } else {
            throw new ServiceException("抽取方式异常");
        }
        List<Yzpw> yzpw = busiExtractExpertApply.getYzpw();
        List<BusiExtractExpertResult> extractExpertResults=new ArrayList<>();
        //保存业主评委
        for (Yzpw yzpw1 : yzpw) {
            BusiExtractExpertResult busiExtractExpertResult=new BusiExtractExpertResult();
            busiExtractExpertResult.setProjectId(busiExtractExpertApply.getProjectId());
            busiExtractExpertResult.setApplyId(busiExtractExpertApply.getApplyId());
            UniqueIDGenerator idWorker = new UniqueIDGenerator(0, 0);
            busiExtractExpertResult.setExpertId(idWorker.nextId());
            busiExtractExpertResult.setExpertName(yzpw1.getYzpwName());
            busiExtractExpertResult.setExpertCode(yzpw1.getYzpwId());
            busiExtractExpertResult.setPhone(yzpw1.getYzpwPhone());
            busiExtractExpertResult.setIsOwner(1);
            extractExpertResults.add(busiExtractExpertResult);
        }
        iBusiExtractExpertResultService.saveBatch(extractExpertResults);
        //project.setProjectStatus(30);
        iBusiTenderProjectService.updateById(project);

    }

    @Transactional
    @Override
    public Boolean suiJiChouQu(BusiExtractExpertApply busiExtractExpertApply) throws IOException {
        List<Long> longs = new ArrayList<>();
        longs.add(busiExtractExpertApply.getApplyId());
        List<BusiExtractExpertEvade> evades = iBusiExtractExpertEvadeService.getByApplyIds(longs);
        List<BusiExtractExpertGroup> groups = iBusiExtractExpertGroupService.getByApplyIds(longs);
        ParamVo paramVo = new ParamVo();
        //paramVo.setProjectCode(busiExtractExpertApply.getProjectId());
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(busiExtractExpertApply.getProjectId());


        if(org.apache.commons.lang3.StringUtils.isNoneBlank(tenderProject.getTenderMode())) {
       //     byId.setTenderModeName(sysDictDataService.selectDictLabel("busi_tender_mode", tenderProject.getTenderMode()));
        }
       // byId.setProjectTypeName(sysDictDataService.selectDictLabel("busi_project_type", tenderProject.getProjectType()));
        // 新增传递项目信息参数
        paramVo.setProjectName(tenderProject.getProjectName());
        paramVo.setProjectCategory(sysDictDataService.selectDictLabel("busi_project_type", tenderProject.getProjectType()));//采购类别
        paramVo.setProcurementMethod(sysDictDataService.selectDictLabel("busi_tender_mode", tenderProject.getTenderMode()));//采购方式

       // BusiExtractExpertApply expertApply = this.getOne(new QueryWrapper<BusiExtractExpertApply>().eq("", tenderProject.getProjectId()));
        paramVo.setProjectType( busiExtractExpertApply.getExtractionType() == null ? "未知类型" :
                busiExtractExpertApply.getExtractionType() == 0 ? "论证" :
                        busiExtractExpertApply.getExtractionType() == 1 ? "评审" : "未知类型");//项目类型


        paramVo.setPurchaserName(tenderProject.getDisplayAgencyOrPurchaserName());//采购人名称
        paramVo.setBudgetAmount(tenderProject.getBudgetAmount());//预算金额
        paramVo.setPurchasingUnitContact(tenderProject.getDisplayAgencyOrPurchaserContactPerson());//联系人
        paramVo.setPurchasingUnitPhone(tenderProject.getDisplayAgencyOrPurchaserPhone());//联系电话
        if(null!=tenderProject.getProjectArea()) {
            BaseTreeData area = baseTreeDataService.getById(tenderProject.getProjectArea());
            //project.setProjectAreaName(area.getName());
            paramVo.setProjectAffiliatedDistrict(area.getName());//隶属县区
        }
        paramVo.setDescription(tenderProject.getProjectContent());//说明
        paramVo.setPackageQuantity(1);//包数量
        paramVo.setProjectStatus(2);//项目状态2

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");

        BusiTenderNotice busiTenderNotice = iBusiTenderNoticeService.getOne(new QueryWrapper<BusiTenderNotice>()
                .eq("del_flag", 0).eq("notice_stats", 1).eq("project_id", busiExtractExpertApply.getProjectId()));
        BusiVenueOccupy one = iBusiVenueOccupyService.getOne(new QueryWrapper<BusiVenueOccupy>()
                .eq("venue_type", 1)
                .eq("del_flag", 0)
                .eq("notice_id", busiTenderNotice.getNoticeId()));
        paramVo.setProjectCode(busiExtractExpertApply.getProjectId());
      //  if (busiExtractExpertApply.getExtractionType()==0){
            Map<String, String> timeMap = getStartTimeEndTime(sdf2.format(new Date()), busiExtractExpertApply.getOccupationTime());
            paramVo.setStartTime(timeMap.get("startTime"));
            paramVo.setEndTime(timeMap.get("endTime"));
      //  }else {
      //      paramVo.setStartTime(sdf.format(one.getOccupyStartTime()));
      //      paramVo.setEndTime(sdf.format(one.getOccupyEndTime()));
      //  }
//
//        paramVo.setStartTime(sdf.format(one.getOccupyStartTime()));
//        paramVo.setEndTime(sdf.format(one.getOccupyEndTime()));
        BusiVenueOccupy evalVenue = iBusiVenueOccupyService.getOne(new QueryWrapper<BusiVenueOccupy>()
                .eq("venue_type", 2)
                .eq("del_flag", 0)
                .eq("notice_id", busiTenderNotice.getNoticeId()));
        if (busiTenderNotice.getVenueType() == 1) {
        //    paramVo.setPsjtsj(sdf2.format(evalVenue.getOccupyStartTime())+" 09:00:00");
            paramVo.setPsdd("鹤壁市淇滨区钜桥镇湘江东路与子罕大街交叉口东南角京东大厦13层 "+evalVenue.getVenueName());
        }else if(busiTenderNotice.getVenueType() == 2){
        //    paramVo.setPsjtsj(sdf.format(evalVenue.getOccupyStartTime()));
            paramVo.setPsdd(evalVenue.getRemark()+" "+evalVenue.getVenueName());
        }
        //paramVo.setPsdd(evalVenue.getVenueName());
        SimpleDateFormat sdfsfm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        paramVo.setPsjtsj(sdfsfm.format(evalVenue.getOccupyStartTime()));


        //规避的名称集合
        List<String> evadeNames = evades.stream()
                .map(BusiExtractExpertEvade::getEvadeName)
                .collect(Collectors.toList());

        List<ParamVo.DemandsBean> demands = new ArrayList<>();
        for (BusiExtractExpertGroup group : groups) {
            ParamVo.DemandsBean demandsBean = new ParamVo.DemandsBean();
            demandsBean.setProId(busiExtractExpertApply.getProjectId());
            demandsBean.setQwsl(group.getExpertNumber());
            demandsBean.setCqfs("随机抽取");
            demandsBean.setId(group.getGroupId().toString());
            demandsBean.setPspm(group.getGroupAddress());
            demands.add(demandsBean);
        }
        paramVo.setExclusionConditions(evadeNames);
        paramVo.setDemands(demands);
        System.out.println(JSON.toJSONString(paramVo));
        System.out.println("111");

        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(paramVo).toString());
        Request request = new Request.Builder()
                .url(chouqu)
                .method("POST", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=071327329DCF80B72EA2FFFDB9396A46")
                .build();
        try {
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            System.out.println("随机抽取response:" + jsonObject.toJSONString());
            if (!jsonObject.get("msg").equals("操作成功")) {
                throw new RuntimeException(jsonObject.get("msg").toString());
            } else {
                SysUser sysUser = userService.selectUserById(1l);
                busiExtractExpertApply.setApplyStatus(2);
                busiExtractExpertApply.setUpdateBy(sysUser.getUpdateBy());
                busiExtractExpertApply.setUpdateTime(new Date());

                this.baseMapper.updateById(busiExtractExpertApply);
                //this.baseMapper.chouQuUpdateApply(busiExtractExpertApply.getApplyId());
                List<RandomizingVo> randomizingVos = JSON.parseArray(jsonObject.get("data").toString(), RandomizingVo.class);
                //List<BusiExtractExpertResult> busiExtractExpertResults=new ArrayList<>();
                for (RandomizingVo randomizingVo : randomizingVos) {
                    BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
                    busiExtractExpertResult.setApplyId(busiExtractExpertApply.getApplyId());
                    busiExtractExpertResult.setExpertId(Long.parseLong(randomizingVo.getExpId() + ""));//专家id
                    busiExtractExpertResult.setExpertName(randomizingVo.getExpName());//专家姓名
                    busiExtractExpertResult.setExpertCode(randomizingVo.getIdCard());//专家身份证号
                    busiExtractExpertResult.setGroupId(randomizingVo.getDemandId());
                    busiExtractExpertResult.setThirtyId(Long.parseLong(randomizingVo.getId() + ""));
                    busiExtractExpertResult.setCompany(randomizingVo.getCompany());
                    busiExtractExpertResult.setPhone(randomizingVo.getPhone());
                    // busiExtractExpertResult.setThirtyId();
                    busiExtractExpertResult.setDelFlag(0);
                    busiExtractExpertResult.setIsOwner(0);
                    busiExtractExpertResult.setSignStatus(0);
                    //busiExtractExpertResult.setExpertAppraise();//专家评价
                    //busiExtractExpertResults.add(busiExtractExpertResult);
                    iBusiExtractExpertResultService.save(busiExtractExpertResult);
                }
                //iBusiExtractExpertResultService.saveBatch(busiExtractExpertResults);

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return true;
    }


    public String getToken() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\"t\":" + System.currentTimeMillis() + ",\r\n\"username\":\"" + username + "\",\r\n\"password\":\"" + password + "\",\r\n\"thirdPartySecret\":\"" + thirdPartySecret + "\"\r\n}");
        Request request = new Request.Builder()
                .url(tokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.body() != null) {
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            return jsonObject.getString("token");
        } else {
            throw new RuntimeException("请求专家抽取token 失败");
        }
    }

    @Transactional
    public Boolean getChouQu(ParamVo paramVo) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(paramVo).toString());
        Request request = new Request.Builder()
                .url(chouqu)
                .method("POST", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=071327329DCF80B72EA2FFFDB9396A46")
                .build();
        try {
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            if (!jsonObject.get("msg").equals("操作成功")) {
                throw new RuntimeException(jsonObject.get("msg").toString());
            } else {
                List<RandomizingVo> randomizingVos = JSON.parseArray(jsonObject.get("data").toString(), RandomizingVo.class);

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return true;
    }

    @Override
    public AjaxResult resetExtract(RestExtractParam restExtractParam) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(restExtractParam));
        Request request = new Request.Builder()
                .url(resetExtract)
                .method("POST", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=6D5B4D8B8C34D25653844AFC6497F9B2")
                .build();
        Response response = client.newCall(request).execute();
        JSONObject jsonObject = JSON.parseObject(response.body().string());
        List<RandomizingVo> randomizingVos = JSON.parseArray(jsonObject.get("data").toString(), RandomizingVo.class);
        //List<BusiExtractExpertResult> busiExtractExpertResults=new ArrayList<>();
        boolean thirtyId = iBusiExtractExpertResultService.remove(new QueryWrapper<BusiExtractExpertResult>().eq("thirty_id", restExtractParam.getProjectExpertId()));
        if (!thirtyId) {
            throw new RuntimeException("重新抽取删除历史抽取记录失败");
        }
        for (RandomizingVo randomizingVo : randomizingVos) {
            BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
            busiExtractExpertResult.setApplyId(restExtractParam.getApplyId());
            busiExtractExpertResult.setExpertId(Long.parseLong(randomizingVo.getExpId() + ""));//专家id
            busiExtractExpertResult.setExpertName(randomizingVo.getExpName());//专家姓名
            busiExtractExpertResult.setExpertCode(randomizingVo.getIdCard());//专家身份证号
            busiExtractExpertResult.setGroupId(randomizingVo.getDemandId());
            busiExtractExpertResult.setThirtyId(Long.parseLong(randomizingVo.getId() + ""));
            busiExtractExpertResult.setCompany(randomizingVo.getCompany());
            busiExtractExpertResult.setPhone(randomizingVo.getPhone());
            busiExtractExpertResult.setDelFlag(0);
            //busiExtractExpertResult.setExpertAppraise();//专家评价
            //busiExtractExpertResults.add(busiExtractExpertResult);
            iBusiExtractExpertResultService.save(busiExtractExpertResult);
        }
        System.out.println("抽取 随机抽取response:" + jsonObject.toJSONString());
        return AjaxResult.success("重新抽取成功");
    }

    public static Map<String, String> getStartTimeEndTime(String dateString, int period) {
        // 解析日期
        LocalDate date = LocalDate.parse(dateString);

        // 定义时间段
        LocalTime startTime = null;
        LocalTime endTime = null;

        switch (period) {
            case 0: // 上午
                startTime = LocalTime.of(8, 0);
                endTime = LocalTime.of(12, 0);
                break;
            case 1: // 下午
                startTime = LocalTime.of(13, 0);
                endTime = LocalTime.of(18, 0);
                break;
            case 2: // 全天
                startTime = LocalTime.of(8, 0);
                endTime = LocalTime.of(18, 0);
                break;
            default:
                throw new IllegalArgumentException("无效的时间段标志");
        }

        // 格式化时间输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 构建开始和结束日期时间字符串
        String startDateTime = date.atTime(startTime).format(formatter);
        String endDateTime = date.atTime(endTime).format(formatter);

        // 创建Map并添加开始和结束时间
        Map<String, String> timeMap = new HashMap<>();
        timeMap.put("startTime", startDateTime);
        timeMap.put("endTime", endDateTime);

        // 返回Map
        return timeMap;
    }

    @Transactional
    @Override
    public void extract(List<Long> applyIds, Boolean isCheck) {
        Date date = new Date();
        List<String> msg = new ArrayList<>();
        List<BusiExtractExpertResult> saveResults = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        //获取申请信息
        List<BusiExtractExpertApply> applyList = new ArrayList<>();
        if (isCheck) {
            applyList = list(new QueryWrapper<BusiExtractExpertApply>().in("apply_id", applyIds));
        } else {
            applyList = list(new QueryWrapper<BusiExtractExpertApply>().in("apply_id", applyIds).eq("apply_status", "1"));
        }
        //获取专家组
        List<BusiExtractExpertGroup> groupsList = iBusiExtractExpertGroupService.getByApplyIds(applyIds);
        Map<Long, List<BusiExtractExpertGroup>> groupsMap = groupsList.stream()
                .collect(Collectors.groupingBy(BusiExtractExpertGroup::getApplyId));
        //获取回避条件  如果抽取结果表此时间已存在则也排除
        List<BusiExtractExpertEvade> evadesList = iBusiExtractExpertEvadeService.getByApplyIds(applyIds);
        Map<Long, List<BusiExtractExpertEvade>> evadesMap = evadesList.stream()
                .collect(Collectors.groupingBy(BusiExtractExpertEvade::getApplyId));
        //循环检查
        applyList.forEach(item -> {
            Set<String> evadeExperts = new HashSet<>();
            Set<String> evadeOrganization = new HashSet<>();
            //1.获取专家组
            List<BusiExtractExpertGroup> busiExtractExpertGroups = groupsMap.get(item.getApplyId());
            //2.获取回避条件
            List<BusiExtractExpertEvade> busiExtractExpertEvades = evadesMap.get(item.getApplyId());
            //3.获取同一天开评标的专家结果
            List<BusiExtractExpertResult> results = unifyDayResult(simpleDateFormat, item);
            //4.根据项目id查专家结果
            results.forEach(resultExpertName -> {
                evadeExperts.add(resultExpertName.getExpertName());
            });
            //5.过滤
            //0和1来自字典表  0单位1个人
            busiExtractExpertEvades.forEach(evades -> {
                if (evades.getEvadeType() == 1) {
                    evadeExperts.add(evades.getEvadeName());
                } else if (evades.getEvadeType() == 0) {
                    evadeOrganization.add(evades.getEvadeName());
                }
            });

            //6.查询实体类
            BusiExpertInfo searchExpertInfo = new BusiExpertInfo();
            searchExpertInfo.getParams().put("evadeExpertNames", evadeExperts);
            searchExpertInfo.getParams().put("evadeExpertOrganizations", evadeOrganization);
            //7.循环进行专家抽取
            //获取分组
            busiExtractExpertGroups.forEach(group -> {
                //抽取数量
                searchExpertInfo.getParams().put("extractExpertNumber", group.getExpertNumber());
                //抽取专家类型
                searchExpertInfo.setExpertType(group.getExpertType());
                //抽取专家从事专业
                searchExpertInfo.setExpertCareer(group.getExpertClassificationCode());
                //抽取评审区域
                searchExpertInfo.setExpertMainEvaluatArea(group.getGroupAddress());
                List<BusiExpertInfo> expertExpertInfos = iBusiExpertInfoService.extractExpert(searchExpertInfo);
                if (ObjectUtil.isEmpty(expertExpertInfos) || expertExpertInfos.size() < group.getExpertNumber()) {
                    msg.add(StringUtils.format("抽取申请【{}】，专家组【{}】，专家数量不足：【需求{}，实际{}】，请重新维护专家抽取信息。 ", item.getApplyId(), group.getGroupId(), group.getExpertNumber(), expertExpertInfos.size()));
                }
                expertExpertInfos.forEach(expert -> {
                    BusiExtractExpertResult result = new BusiExtractExpertResult();
                    result.setApplyId(item.getApplyId());
                    result.setExpertId(expert.getExpertId());
                    result.setExpertCode(expert.getExpertCertificateCode());
                    result.setDelFlag(DelFlagStatus.OK.getCode());
                    result.setCreateTime(date);
                    result.setCreateBy("系统抽取");
                    result.setUpdateBy("系统抽取");
                    result.setUpdateTime(date);
                    saveResults.add(result);
                });
            });
            item.setApplyStatus(2);
        });
        if (msg.size() != 0) {
            throw new ServiceException("检查本批次申请专家错误，错误信息：" + JSON.toJSONString(msg));
        }
        if (!isCheck) {
            updateBatchById(applyList);
            iBusiExtractExpertResultService.saveBatch(saveResults);
        }
    }


    private List<BusiExtractExpertResult> unifyDayResult(SimpleDateFormat simpleDateFormat, BusiExtractExpertApply apply) {
        List<BusiExtractExpertResult> results = new ArrayList<>();
        BusiTenderNotice notice = iBusiTenderNoticeService.getOne(new QueryWrapper<BusiTenderNotice>().eq("project_id", apply.getProjectId()).eq("notice_stats", 1));
        BusiTenderNotice noticeQuery = new BusiTenderNotice();
//        noticeQuery.getParams().put("beginBidOpeningTime", simpleDateFormat.format(notice.getBidOpeningTime()));
//        noticeQuery.getParams().put("endBidOpeningTime", simpleDateFormat.format(notice.getBidOpeningEndTime() == null ? notice.getBidOpeningTime() : notice.getBidOpeningEndTime()));
        noticeQuery.getParams().put("beginBidEvaluationTime", simpleDateFormat.format(notice.getBidEvaluationTime()));
        noticeQuery.getParams().put("endBidEvaluationTime", simpleDateFormat.format(notice.getBidEvaluationEndTime() == null ? notice.getBidEvaluationTime() : notice.getBidEvaluationEndTime()) + "23:59:59");
        List<BusiTenderNotice> noticeList = iBusiTenderNoticeService.selectList(noticeQuery);
        if (noticeList != null && !noticeList.isEmpty()) {
            List<Long> projectIds = noticeList.stream().map(noticeItem -> noticeItem.getProjectId()).collect(Collectors.toList());
            List<BusiExtractExpertApply> applyList = list(new QueryWrapper<BusiExtractExpertApply>().in("project_id", projectIds));
            List<Long> applyIds = applyList.stream().map(item -> item.getApplyId()).collect(Collectors.toList());
            results = iBusiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>().in("apply_id", applyIds));
        }
        return results;
    }

    @Transactional
    @Override
    public boolean removeApply(Long applyId){
        boolean b = false;
        BusiExtractExpertApply extractExpertApply = getById(applyId);
        if (extractExpertApply != null) {
            b = removeById(applyId);
            if(b){
                BusiTenderProject project = iBusiTenderProjectService.getById(extractExpertApply.getProjectId());
                project.setProjectStatus(20);
                iBusiTenderProjectService.updateById(project);
                try {
                    cancelExpertExtract(extractExpertApply.getProjectId());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "[" + extractExpertApply.getProjectId() + "]");
            Request request = null;
            try {
                request = new Request.Builder()
                        .url(deleteExtract)
                        .method("POST", body)
                        .addHeader("token", getToken())
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Cookie", "JSESSIONID=C67EE2FCCCFA74425CEDCBD01FE50067")
                        .build();
                Response response  = client.newCall(request).execute();
                JSONObject jsonObject = JSON.parseObject(response.body().string());
                if (!jsonObject.get("msg").equals("操作成功")) {
                    throw new RuntimeException("第三方取消专家占用失败");
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return b;
    }

    @Override
    public void cancelExpertExtract(Long projectId) throws IOException {
        BusiExtractExpertApply extractExpertApply =
                getOne(new QueryWrapper<BusiExtractExpertApply>().eq("project_id", projectId));
        if (extractExpertApply != null && extractExpertApply.getApplyId() != null) {
            List<BusiExtractExpertResult> busiExtractExpertResults =
                    iBusiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>().eq("apply_id", extractExpertApply.getApplyId()));
            if (busiExtractExpertResults!=null && !busiExtractExpertResults.isEmpty()) {
                Set<Long> resultIds = busiExtractExpertResults.stream()
                        .map(BusiExtractExpertResult::getResultId)
                        .collect(Collectors.toSet());
                iBusiExtractExpertResultService.removeByIds(resultIds);
                OkHttpClient client = new OkHttpClient().newBuilder()
                        .build();
                MediaType mediaType = MediaType.parse("application/json");
                RequestBody body = RequestBody.create(mediaType, "[" + projectId + "]");
                Request request = new Request.Builder()
                        .url(deleteExtract)
                        .method("POST", body)
                        .addHeader("token", getToken())
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Cookie", "JSESSIONID=C67EE2FCCCFA74425CEDCBD01FE50067")
                        .build();
                Response response = client.newCall(request).execute();
                JSONObject jsonObject = JSON.parseObject(response.body().string());
                if (!jsonObject.get("msg").equals("操作成功")) {
                    throw new RuntimeException("第三方取消专家占用失败");
                }
            }
        }
    }
}
