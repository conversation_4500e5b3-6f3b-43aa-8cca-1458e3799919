package com.ruoyi.enums;

import java.util.Arrays;

public enum ZcxhEnum {
    NOTICE("同步项目公告", "/sms/sync/syncAnnouncement"),
    SIGNIN("签到流标", "开标签到供应商数量不足3家"),
    EVALUATION("评审流标", "有效参与供应商数量不足3家"),
    DECODE("解密流标", "有效解密供应商数量不足3家"),;

    private String name;
    private String url;

    ZcxhEnum(String name, String url) {
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }

}
