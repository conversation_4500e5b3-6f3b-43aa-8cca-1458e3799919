package com.ruoyi.eval.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.vo.GetEvalAgainQuoteVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalAgainQuote;
import com.ruoyi.eval.service.IEvalAgainQuoteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 二次报价Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "二次报价管理")
@RestController
@RequestMapping("/again/quote")
public class EvalAgainQuoteController extends BaseController {
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;

    /**
     * 查询二次报价列表
     */
    @PreAuthorize("@ss.hasPermi('again:quote:list')")
    @ApiOperation(value = "查询二次报价列表")
    @GetMapping("/list")
    public TableDataInfo list(EvalAgainQuote evalAgainQuote) {
        startPage();
        List<EvalAgainQuote> list = evalAgainQuoteService.selectList(evalAgainQuote);
        return getDataTable(list);
    }

    /**
     * 查询二次报价列表
     */
    @PreAuthorize("@ss.hasPermi('again:quote:list')")
    @ApiOperation(value = "查询二次报价列表")
    @GetMapping("/getSecondPriceInfoVos/{projectEvaluationId}")
    public AjaxResult getSecondPriceInfoVos(@PathVariable(value = "projectEvaluationId") Long projectEvaluationId) {
        //二次报价的所用供应商集合
        List<EvalAgainQuote> list = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                .eq("project_evaluation_id", projectEvaluationId));
        Set<String> supplierNames = list.stream().map(EvalAgainQuote::getCreateBy).collect(Collectors.toSet());
        //todo 排序
        // 使用Stream API进行排序，并收集到TreeSet中
        TreeSet<String> sortedSupplierNames = supplierNames.stream()
                .sorted()
                .collect(Collectors.toCollection(TreeSet::new));
        //每次报价
        // 使用Stream API进行分组
        // 使用TreeMap来排序键，并对每个List按createBy属性进行排序
        Map<Integer, List<EvalAgainQuote>> sortedMap = list.stream()
                .collect(Collectors.groupingBy(
                        EvalAgainQuote::getQuoteNumber,
                        TreeMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                quotes -> quotes.stream()
                                        .sorted(Comparator.comparing(EvalAgainQuote::getCreateBy))
                                        .collect(Collectors.toList())
                        )
                ));
        List<Map> result = new ArrayList<>();
        sortedMap.forEach((key, value) -> {
            Map<String, EvalAgainQuote> map = value.stream()
                    .collect(Collectors.toMap(
                            EvalAgainQuote::getCreateBy,
                            replacement -> replacement
                    ));
            Map<String, String> obj = new HashMap<>();
            sortedSupplierNames.forEach(item -> {
                EvalAgainQuote evalAgainQuote = map.get(item);
                if(evalAgainQuote==null){
                    obj.put(item, "0" );
                }else{
                    obj.put(item, evalAgainQuote.getQuoteAmount().toString() );
                }
                 obj.put("priceNum","第" + key + "次报价");
            });
            result.add(obj);
        });
        return AjaxResult.success().put("columns", sortedSupplierNames).put("data",result);
    }

    /**
     * 导出二次报价列表
     */
    @PreAuthorize("@ss.hasPermi('again:quote:export')")
    @Log(title = "二次报价", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出二次报价列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvalAgainQuote evalAgainQuote) {
        List<EvalAgainQuote> list = evalAgainQuoteService.selectList(evalAgainQuote);
        ExcelUtil<EvalAgainQuote> util = new ExcelUtil<EvalAgainQuote>(EvalAgainQuote.class);
        util.exportExcel(response, list, "二次报价数据");
    }

    /**
     * 获取二次报价详细信息
     */
    @PreAuthorize("@ss.hasPermi('again:quote:query')")
    @ApiOperation(value = "获取二次报价详细信息")
    @ApiImplicitParam(name = "againQuoteId", value = "再次报价id", required = true, dataType = "Long")
    @GetMapping(value = "/{againQuoteId}")
    public AjaxResult getInfo(@PathVariable("againQuoteId") Long againQuoteId) {
        return success(evalAgainQuoteService.getById(againQuoteId));
    }

    /**
     * 新增二次报价
     */
    @PreAuthorize("@ss.hasPermi('again:quote:add')")
    @Log(title = "二次报价", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增二次报价")
    @PostMapping
    public AjaxResult add(@RequestBody EvalAgainQuote evalAgainQuote) {
        evalAgainQuote.setEntId(getEntId());
        if (StringUtils.isEmpty(evalAgainQuote.getProjectEvaluationId())){
            return AjaxResult.error("系统异常，请刷新页面");
        }
        return toAjax(evalAgainQuoteService.saveEvalAgainQuote(evalAgainQuote));
    }

    /**
     * 修改二次报价
     */
    @PreAuthorize("@ss.hasPermi('again:quote:edit')")
    @Log(title = "二次报价", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改二次报价")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalAgainQuote evalAgainQuote) {
        return toAjax(evalAgainQuoteService.updateById(evalAgainQuote));
    }

    /**
     * 删除二次报价
     */
    @PreAuthorize("@ss.hasPermi('again:quote:remove')")
    @Log(title = "二次报价", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除二次报价")
    @DeleteMapping("/{againQuoteIds}")
    public AjaxResult remove(@PathVariable Long[] againQuoteIds) {
        return toAjax(evalAgainQuoteService.removeByIds(Arrays.asList(againQuoteIds)));
    }

    //评审汇总
    @PostMapping(value = "/getEvalAgainQuote")
//    public AjaxResult getEvalAgainQuote(@RequestParam("resultId") Long resultId,@RequestParam("itemId") Long itemId,  @RequestParam("projectId") Long projectId) {
    public AjaxResult getEvalAgainQuote(@RequestBody GetEvalAgainQuoteVo getEvalAgainQuoteVo) {
        return AjaxResult.success(evalAgainQuoteService.getEvalAgainQuote(getEvalAgainQuoteVo));
    }

    //二次报价信息
    @PostMapping("/getSecondQuotation")
    public AjaxResult getSecondQuotation(@RequestBody EvalAgainQuote evalAgainQuote) {
        return evalAgainQuoteService.getSecondQuotation(evalAgainQuote);
    }

}
