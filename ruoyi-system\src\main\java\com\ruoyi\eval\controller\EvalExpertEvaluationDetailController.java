package com.ruoyi.eval.controller;

import java.io.IOException;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.eval.vo.CheckZhuanJiaPS;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import com.ruoyi.eval.service.IEvalExpertEvaluationDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 专家打分详情Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "专家打分详情管理")
@RestController
@RequestMapping("/evaluation/detail")
public class EvalExpertEvaluationDetailController extends BaseController {
    @Autowired
    private IEvalExpertEvaluationDetailService evalExpertEvaluationDetailService;

/**
 * 查询专家打分详情列表
 */
@PreAuthorize("@ss.hasPermi('evaluation:detail:list')")
@ApiOperation(value = "查询专家打分详情列表")
@GetMapping("/list")
    public TableDataInfo list(EvalExpertEvaluationDetail evalExpertEvaluationDetail) {
        startPage();
        List<EvalExpertEvaluationDetail> list = evalExpertEvaluationDetailService.selectList(evalExpertEvaluationDetail);
        return getDataTable(list);
    }

    /**
     * 导出专家打分详情列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:export')")
    @Log(title = "专家打分详情", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家打分详情列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvalExpertEvaluationDetail evalExpertEvaluationDetail) {
        List<EvalExpertEvaluationDetail> list = evalExpertEvaluationDetailService.selectList(evalExpertEvaluationDetail);
        ExcelUtil<EvalExpertEvaluationDetail> util = new ExcelUtil<EvalExpertEvaluationDetail>(EvalExpertEvaluationDetail. class);
        util.exportExcel(response, list, "专家打分详情数据");
    }

    /**
     * 获取专家打分详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:query')")
    @ApiOperation(value = "获取专家打分详情详细信息")
    @ApiImplicitParam(name = "expertEvaluationId", value = "专家评分id", required = true, dataType = "Long")
    @GetMapping(value = "getInfo/{expertEvaluationId}")
    public AjaxResult getInfo(@PathVariable("expertEvaluationId")Long expertEvaluationId) {
        return success(evalExpertEvaluationDetailService.getById(expertEvaluationId));
    }

    /**
     * 新增专家打分详情
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:add')")
    @Log(title = "专家打分详情", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家打分详情")
    @PostMapping
    public AjaxResult add(@RequestBody List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails) {


        return toAjax(evalExpertEvaluationDetailService.saveEvalExpertEvaluationDetails(evalExpertEvaluationDetails));
    }

    /**
     * 修改专家打分详情
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:edit')")
    @Log(title = "专家打分详情", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家打分详情")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalExpertEvaluationDetail evalExpertEvaluationDetail) {
        return toAjax(evalExpertEvaluationDetailService.updateById(evalExpertEvaluationDetail));
    }
    //获取供应商评审信息
    @PostMapping("/getDetailByPsxx")
    public AjaxResult getDetailByPsxx(@RequestBody CheckZhuanJiaPS checkZhuanJiaPS) {
        return evalExpertEvaluationDetailService.getDetailByPsxx(checkZhuanJiaPS);
    }

    /**
     * 删除专家打分详情
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:remove')")
    @Log(title = "专家打分详情", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家打分详情")
    @DeleteMapping("/{expertEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] expertEvaluationIds) {
        return toAjax(evalExpertEvaluationDetailService.removeByIds(Arrays.asList(expertEvaluationIds)));
    }



    @PostMapping(value = "/getEalExpertEvaluationDetail")
    public AjaxResult getEalExpertEvaluationDetail(@RequestParam("itemId")Long itemId,@RequestParam("projectId")Long projectId,@RequestParam("resultId")Long resultId) {
        return AjaxResult.success(evalExpertEvaluationDetailService.getByEvalExpertEvaluationDetail(projectId,itemId,resultId));
    }


    //专家组
    @PostMapping(value = "/getEalExpertEvaluationDetailToGroupLeader")
    public AjaxResult getEalExpertEvaluationDetailToGroupLeader(@RequestParam("itemId")Long itemId,@RequestParam("projectId")Long projectId) throws IOException {
        return AjaxResult.success(evalExpertEvaluationDetailService.getEalExpertEvaluationDetailToGroupLeader(projectId,itemId));
    }

    //最终评审汇总
    @PostMapping(value = "/getReviewSummary")
    public AjaxResult getReviewSummary(@RequestParam("projectId")Long projectId,@RequestParam("resultId")Long resultId) {
        return AjaxResult.success(evalExpertEvaluationDetailService.getReviewSummary(projectId,resultId));
    }
    //确认是否填写完成，提交进入下一步
    @PostMapping(value = "/getCheckExpertEvaluationDetail")
    public AjaxResult getCheckExpertEvaluationDetail(@RequestBody  CheckZhuanJiaPS checkZhuanJiaPS) throws IOException {
        return evalExpertEvaluationDetailService.getCheckExpertEvaluationDetail(checkZhuanJiaPS);
    }

    //汇总确认页面
    //Review summary confirmation
    @PostMapping(value = "/reviewSummaryConfirmation")
    public AjaxResult reviewSummaryConfirmation(@RequestBody  CheckZhuanJiaPS checkZhuanJiaPS) throws IOException {
        return evalExpertEvaluationDetailService.reviewSummaryConfirmation(checkZhuanJiaPS);
    }

}
