package com.ruoyi.eval.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalExpertEvaluationInfo;
import com.ruoyi.eval.service.IEvalExpertEvaluationInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 专家评审信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "专家评审信息管理")
@RestController
@RequestMapping("/evalExpertEvaluationInfo")
public class EvalExpertEvaluationInfoController extends BaseController {
    @Autowired
    private IEvalExpertEvaluationInfoService evalExpertEvaluationInfoService;

/**
 * 查询专家评审信息列表
 */
//@PreAuthorize("@ss.hasPermi('evaluation:detail:list')")
@ApiOperation(value = "查询专家评审信息列表")
@GetMapping("/list")
    public TableDataInfo list(EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        startPage();
        List<EvalExpertEvaluationInfo> list = evalExpertEvaluationInfoService.selectList(evalExpertEvaluationInfo);
        return getDataTable(list);
    }

    /**
     * 获取专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:query')")
    @ApiOperation(value = "获取专家评审信息信息")
    @ApiImplicitParam(name = "evalExpertEvaluationInfoId", value = "专家评分id", required = true, dataType = "Long")
    @PostMapping(value = "/getInfo")
    public AjaxResult getInfo(@RequestBody EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        return evalExpertEvaluationInfoService.getInfo(evalExpertEvaluationInfo);
    }

    /**
     * 新增专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:add')")
    @Log(title = "专家评审信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家评审信息")
    @PostMapping
    public AjaxResult add(@RequestBody EvalExpertEvaluationInfo evalExpertEvaluationDetail) {
        return toAjax(evalExpertEvaluationInfoService.save(evalExpertEvaluationDetail));
    }

    /**
     * 修改专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:edit')")
    @Log(title = "专家评审信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家评审信息")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalExpertEvaluationInfo evalExpertEvaluationDetail) {
        return toAjax(evalExpertEvaluationInfoService.updateById(evalExpertEvaluationDetail));
    }

    /**
     * 删除专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:remove')")
    @Log(title = "专家评审信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家评审信息")
    @DeleteMapping("/{expertEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] expertEvaluationIds) {
        return toAjax(evalExpertEvaluationInfoService.removeByIds(Arrays.asList(expertEvaluationIds)));
    }

    /**
     * 修改专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:edit')")
    @Log(title = "专家评审信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家评审信息")
    @PostMapping("/updateNode")
    public AjaxResult updateNode(@RequestBody EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        return evalExpertEvaluationInfoService.updateNode(evalExpertEvaluationInfo);
    }

    /**
     * 获取专家评审信息
     */
//    @PreAuthorize("@ss.hasPermi('evaluation:detail:query')")
    @ApiOperation(value = "获取专家评审信息信息")
    @ApiImplicitParam(name = "evalExpertEvaluationInfoId", value = "专家评分id", required = true, dataType = "Long")
    @PostMapping(value = "/getInfo2")
    public AjaxResult getInfo2(@RequestBody EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        try {
            return evalExpertEvaluationInfoService.getInfo2(evalExpertEvaluationInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }
    }

}
