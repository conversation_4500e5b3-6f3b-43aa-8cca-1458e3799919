package com.ruoyi.eval.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalExpertScoreInfo;
import com.ruoyi.eval.service.IEvalExpertScoreInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 专家评审信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "专家评审信息管理")
@RestController
@RequestMapping("/evalExpertScoreInfo")
public class EvalExpertScoreInfoController extends BaseController {
    @Autowired
    private IEvalExpertScoreInfoService evalExpertScoreInfoService;

/**
 * 查询专家评审信息列表
 */
@PreAuthorize("@ss.hasPermi('evaluation:detail:list')")
@ApiOperation(value = "查询专家评审信息列表")
@GetMapping("/list")
    public TableDataInfo list(EvalExpertScoreInfo evalExpertScoreInfo) {
        startPage();
        List<EvalExpertScoreInfo> list = evalExpertScoreInfoService.selectList(evalExpertScoreInfo);
        return getDataTable(list);
    }

    /**
     * 获取专家评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:query')")
    @ApiOperation(value = "获取专家评审信息信息")
    @ApiImplicitParam(name = "evalExpertEvaluationInfoId", value = "专家评分id", required = true, dataType = "Long")
    @GetMapping(value = "/{expertEvaluationId}")
    public AjaxResult getInfo(@PathVariable("evalExpertEvaluationInfoId")Long evalExpertEvaluationInfoId) {
        return success(evalExpertScoreInfoService.getById(evalExpertEvaluationInfoId));
    }

    /**
     * 新增专家评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:add')")
    @Log(title = "专家评审信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家评审信息")
    @PostMapping
    public AjaxResult add(@RequestBody List<EvalExpertScoreInfo> evalExpertScoreInfos) {
        return toAjax(evalExpertScoreInfoService.saveBatch(evalExpertScoreInfos));
    }

    /**
     * 修改专家评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:edit')")
    @Log(title = "专家评审信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家评审信息")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalExpertScoreInfo evalExpertScoreInfo) {
        evalExpertScoreInfo.getParams().put("opUser",evalExpertScoreInfo.getEvalExpertScoreInfoId());

        return toAjax(evalExpertScoreInfoService.updateById(evalExpertScoreInfo));
    }

    /**
     * 删除专家评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:detail:remove')")
    @Log(title = "专家评审信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家评审信息")
    @DeleteMapping("/{expertEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] expertEvaluationIds) {
        return toAjax(evalExpertScoreInfoService.removeByIds(Arrays.asList(expertEvaluationIds)));
    }


    @ApiOperation(value = "添加专家评审进度信息")
    @PostMapping("/addEvalExpertScoreInfo")
    public AjaxResult addEvalExpertScoreInfo( @RequestBody  EvalExpertScoreInfo evalExpertScoreInfo) {
        return evalExpertScoreInfoService.addEvalExpertScoreInfo(evalExpertScoreInfo);
    }

    @ApiOperation(value = "查询专家评审节点信息")
    @GetMapping("/getEvalExpertScoreInfo")
    public AjaxResult getEvalExpertScoreInfo(EvalExpertScoreInfo evalExpertScoreInfo) {
        return evalExpertScoreInfoService.getEvalExpertScoreInfo(evalExpertScoreInfo);
    }

    @ApiOperation(value = "专家组长重新评审接口")
    @GetMapping("/reEvaluation")
    public AjaxResult reEvaluation(EvalExpertScoreInfo evalExpertScoreInfo) {
        return evalExpertScoreInfoService.reEvaluation(evalExpertScoreInfo);
    }


    @PostMapping("/editEvalExpertScoreInfo")
    public AjaxResult editEvalExpertScoreInfo(@RequestBody EvalExpertScoreInfo evalExpertScoreInfo) {
        evalExpertScoreInfo.getParams().put("opUser","ADMIN");

        return toAjax(evalExpertScoreInfoService.saveOrUpdate(evalExpertScoreInfo));
    }
}
