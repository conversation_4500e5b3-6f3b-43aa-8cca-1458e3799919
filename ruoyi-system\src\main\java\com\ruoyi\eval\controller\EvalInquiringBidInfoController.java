package com.ruoyi.eval.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.eval.vo.EvalInquiringVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalInquiringBidInfo;
import com.ruoyi.eval.service.IEvalInquiringBidInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 询标信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "询标信息管理")
@RestController
@RequestMapping("/bid/info")
public class EvalInquiringBidInfoController extends BaseController {
    @Autowired
    private IEvalInquiringBidInfoService evalInquiringBidInfoService;

/**
 * 查询询标信息列表
 */
@PreAuthorize("@ss.hasPermi('bid:info:list')")
@ApiOperation(value = "查询询标信息列表")
@GetMapping("/list")
    public TableDataInfo list(EvalInquiringBidInfo evalInquiringBidInfo) {
        startPage();
        List<EvalInquiringBidInfo> list = evalInquiringBidInfoService.selectList(evalInquiringBidInfo);
        return getDataTable(list);
    }

    /**
     * 导出询标信息列表
     */
    @PreAuthorize("@ss.hasPermi('bid:info:export')")
    @Log(title = "询标信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出询标信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvalInquiringBidInfo evalInquiringBidInfo) {
        List<EvalInquiringBidInfo> list = evalInquiringBidInfoService.selectList(evalInquiringBidInfo);
        ExcelUtil<EvalInquiringBidInfo> util = new ExcelUtil<EvalInquiringBidInfo>(EvalInquiringBidInfo. class);
        util.exportExcel(response, list, "询标信息数据");
    }

    /**
     * 获取询标信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bid:info:query')")
    @ApiOperation(value = "获取询标信息详细信息")
    @ApiImplicitParam(name = "inquiringBidId", value = "询标id", required = true, dataType = "Long")
    @GetMapping(value = "/{inquiringBidId}")
    public AjaxResult getInfo(@PathVariable("inquiringBidId")Long inquiringBidId) {
        return success(evalInquiringBidInfoService.getById(inquiringBidId));
    }

    /**
     * 新增询标信息
     */
    @PreAuthorize("@ss.hasPermi('bid:info:add')")
    @Log(title = "询标信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增询标信息")
    @PostMapping
    public AjaxResult add(@RequestBody EvalInquiringBidInfo evalInquiringBidInfo) {
        return toAjax(evalInquiringBidInfoService.save(evalInquiringBidInfo));
    }

    /**
     * 修改询标信息
     */
    @PreAuthorize("@ss.hasPermi('bid:info:edit')")
    @Log(title = "询标信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改询标信息")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalInquiringBidInfo evalInquiringBidInfo) {
        return toAjax(evalInquiringBidInfoService.updateById(evalInquiringBidInfo));
    }

    /**
     * 删除询标信息
     */
    @PreAuthorize("@ss.hasPermi('bid:info:remove')")
    @Log(title = "询标信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除询标信息")
    @DeleteMapping("/{inquiringBidIds}")
    public AjaxResult remove(@PathVariable Long[] inquiringBidIds) {
        return toAjax(evalInquiringBidInfoService.removeByIds(Arrays.asList(inquiringBidIds)));
    }
    @ApiOperation(value = "获取询标历史信息")//20250225修改成获取最新一条未回复消息
    @RequestMapping("/getHistoryMessage")
    public AjaxResult getHistoryMessage(@RequestBody EvalInquiringVo evalInquiringVo) {
        return evalInquiringBidInfoService.getHistoryMessage(evalInquiringVo);
    }
}
