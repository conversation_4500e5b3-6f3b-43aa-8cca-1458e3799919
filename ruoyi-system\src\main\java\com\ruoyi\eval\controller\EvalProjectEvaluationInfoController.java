package com.ruoyi.eval.controller;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 项目评审信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "项目评审信息管理")
@RestController
@RequestMapping("/evaluation/info")
public class EvalProjectEvaluationInfoController extends BaseController {
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;

    /**
     * 查询项目评审信息列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:list')")
    @ApiOperation(value = "查询项目评审信息列表")
    @GetMapping("/list")
    public TableDataInfo list(EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        startPage();
        List<EvalProjectEvaluationInfo> list = evalProjectEvaluationInfoService.selectList(evalProjectEvaluationInfo);
        return getDataTable(list);
    }

    /**
     * 查询项目评审信息列表
     */
    @ApiOperation(value = "查询项目评审信息列表")
    @GetMapping("/selectByProject")
    public AjaxResult selectByProject(Long projectId) {
        try {
            EvalProjectEvaluationInfo info = evalProjectEvaluationInfoService.selectByProject(projectId);
            return AjaxResult.success(info);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }
    /**
     * 导出项目评审信息列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:export')")
    @Log(title = "项目评审信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出项目评审信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        List<EvalProjectEvaluationInfo> list = evalProjectEvaluationInfoService.selectList(evalProjectEvaluationInfo);
        ExcelUtil<EvalProjectEvaluationInfo> util = new ExcelUtil<EvalProjectEvaluationInfo>(EvalProjectEvaluationInfo.class);
        util.exportExcel(response, list, "项目评审信息数据");
    }

    /**
     * 获取项目评审信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:query')")
    @ApiOperation(value = "获取项目评审信息详细信息")
    @ApiImplicitParam(name = "projectEvaluationId", value = "项目评审信息id", required = true, dataType = "Long")
    @GetMapping(value = "/{projectEvaluationId}")
    public AjaxResult getInfo(@PathVariable("projectEvaluationId") Long projectEvaluationId) {
        return success(evalProjectEvaluationInfoService.getById(projectEvaluationId));
    }

    /**
     * 新增项目评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:add')")
    @Log(title = "项目评审信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增项目评审信息")
    @PostMapping
    public AjaxResult add(@RequestBody EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        return evalProjectEvaluationInfoService.saveEvalProjectEvaluationInfo(evalProjectEvaluationInfo);
    }

    /**
     * 修改项目评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:edit')")
    @Log(title = "项目评审信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改项目评审信息")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        try {
            evalProjectEvaluationInfoService.updateInfos(evalProjectEvaluationInfo);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    /**
     * 删除项目评审信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:info:remove')")
    @Log(title = "项目评审信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除项目评审信息")
    @DeleteMapping("/{projectEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] projectEvaluationIds) {
        return toAjax(evalProjectEvaluationInfoService.removeByIds(Arrays.asList(projectEvaluationIds)));
    }

    /**
     * 导出评审结果
     * @param projectId
     * @param resultId
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/exportEvalProjectEvaluationInfo")
    public void exportEvalProjectEvaluationInfo(@RequestParam("projectId") Long projectId,@RequestParam("resultId") Long resultId, HttpServletResponse response) throws IOException {
        evalProjectEvaluationInfoService.exportEvalProjectEvaluationInfo(projectId, resultId,response);
    }


    @GetMapping(value = "/saveReviewReport")
    public void saveReviewReport(@RequestParam("projectId") Long projectId,@RequestParam("resultId") Long resultId, HttpServletResponse response){
        evalProjectEvaluationInfoService.saveReviewReport( projectId,  resultId,  response);
    }
}
