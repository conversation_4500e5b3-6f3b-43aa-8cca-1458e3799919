package com.ruoyi.eval.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.service.IEvalProjectEvaluationProcessService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 项目评审进度Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "项目评审进度管理")
@RestController
@RequestMapping("/evaluation/process")
public class EvalProjectEvaluationProcessController extends BaseController {
    @Autowired
    private IEvalProjectEvaluationProcessService evalProjectEvaluationProcessService;

/**
 * 查询项目评审进度列表
 */
@PreAuthorize("@ss.hasPermi('evaluation:process:list')")
@ApiOperation(value = "查询项目评审进度列表")
@GetMapping("/list")
    public TableDataInfo list(EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        startPage();
        List<EvalProjectEvaluationProcess> list = evalProjectEvaluationProcessService.selectList(evalProjectEvaluationProcess);
        return getDataTable(list);
    }

    /**
     * 导出项目评审进度列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:process:export')")
    @Log(title = "项目评审进度", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出项目评审进度列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        List<EvalProjectEvaluationProcess> list = evalProjectEvaluationProcessService.selectList(evalProjectEvaluationProcess);
        ExcelUtil<EvalProjectEvaluationProcess> util = new ExcelUtil<EvalProjectEvaluationProcess>(EvalProjectEvaluationProcess. class);
        util.exportExcel(response, list, "项目评审进度数据");
    }

    /**
     * 获取项目评审进度详细信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:process:query')")
    @ApiOperation(value = "获取项目评审进度详细信息")
    @ApiImplicitParam(name = "evaluationProcessId", value = "项目评审进度id", required = true, dataType = "Long")
    @GetMapping(value = "/{evaluationProcessId}")
    public AjaxResult getInfo(@PathVariable("evaluationProcessId")Long evaluationProcessId) {
        return success(evalProjectEvaluationProcessService.getById(evaluationProcessId));
    }

    /**
     * 新增项目评审进度
     */
    @PreAuthorize("@ss.hasPermi('evaluation:process:add')")
    @Log(title = "项目评审进度", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增项目评审进度")
    @PostMapping
    public AjaxResult saveProjectEvaluationProcess(@RequestBody EvalProjectEvaluationProcess evalProjectEvaluationProcess) {

        return evalProjectEvaluationProcessService.saveProjectEvaluationProcess(evalProjectEvaluationProcess);
    }

    /**
     * 修改项目评审进度
     */
    @PreAuthorize("@ss.hasPermi('evaluation:process:edit')")
    @Log(title = "项目评审进度", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改项目评审进度")
    @PutMapping
    public AjaxResult edit(@RequestBody EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        return evalProjectEvaluationProcessService.updateEvalProjectEvaluationProcess(evalProjectEvaluationProcess);
    }

    /**
     * 删除项目评审进度
     */
    @PreAuthorize("@ss.hasPermi('evaluation:process:remove')")
    @Log(title = "项目评审进度", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除项目评审进度")
    @DeleteMapping("/{evaluationProcessIds}")
    public AjaxResult remove(@PathVariable Long[] evaluationProcessIds) {
        return toAjax(evalProjectEvaluationProcessService.removeByIds(Arrays.asList(evaluationProcessIds)));
    }

    //重新评审
    @GetMapping("/reEvaluate/{evaluationProcessId}")
    public AjaxResult reEvaluate(@PathVariable("evaluationProcessId")Long evaluationProcessId) {
        return evalProjectEvaluationProcessService.reEvaluate(evaluationProcessId);
    }
}
