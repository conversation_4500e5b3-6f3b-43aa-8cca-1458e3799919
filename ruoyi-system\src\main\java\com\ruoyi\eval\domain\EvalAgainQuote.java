package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 二次报价对象 eval_again_quote
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("二次报价对象")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalAgainQuoteMapper.EvalAgainQuoteResult")
public class EvalAgainQuote extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 再次报价id
     */
    @ApiModelProperty("再次报价id")
    @Excel(name = "再次报价id")
    @TableId(type = IdType.ASSIGN_ID)

    private Long againQuoteId;
    /**
     * 项目评审信息id
     */
    @ApiModelProperty("项目评审信息id")
    @Excel(name = "项目评审信息id")
    private String projectEvaluationId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 报价次数
     */
    @ApiModelProperty("报价次数")
    @Excel(name = "报价次数")
    private Integer quoteNumber;
    /**
     * 报价金额
     */
    @ApiModelProperty("报价金额")
    @Excel(name = "报价金额")
    private BigDecimal quoteAmount;
    /**
     * 报价文件
     */
    @ApiModelProperty("报价文件")
    @Excel(name = "报价文件")
    private String quoteFile;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private Long projectId;
}
