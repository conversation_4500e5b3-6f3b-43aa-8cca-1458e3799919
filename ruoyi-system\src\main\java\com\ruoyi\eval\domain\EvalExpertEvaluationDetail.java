package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 专家打分详情对象 eval_expert_evaluation_detail
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("专家打分详情对象")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalExpertEvaluationDetailMapper.EvalExpertEvaluationDetailResult")
public class EvalExpertEvaluationDetail extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 专家评分id
     */
    @ApiModelProperty("专家评分id")
            @Excel(name = "专家评分id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long expertEvaluationId;
    /**
     * 用户评分办法项id
     */
    @ApiModelProperty("用户评分办法项id")
            @Excel(name = "用户评分办法项id")
    private Long scoringMethodUitemId;
    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
            @Excel(name = "专家id")
    private Long expertResultId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
            @Excel(name = "企业id")
    private Long entId;
    /**
     * 评审结果，打分制：分数 通过制：是否通过
     */
    @ApiModelProperty("评审结果，打分制：分数 通过制：是否通过")
            @Excel(name = "评审结果，打分制：分数 通过制：是否通过")
    private String evaluationResult;
    /**
     * 评审原因，通过制未通过时填写
     */
    @ApiModelProperty("评审原因，通过制未通过时填写")
            @Excel(name = "评审原因，通过制未通过时填写")
    private String evaluationRemark;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

        }
