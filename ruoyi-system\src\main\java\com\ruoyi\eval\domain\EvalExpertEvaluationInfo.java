package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 专家评审信息 eval_expert_evaluation_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("专家评审信息")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalExpertEvaluationInfoMapper.EvalExpertEvaluationInfoResult")
public class EvalExpertEvaluationInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 专家评分id
     */
    @ApiModelProperty("专家评审id")
    @Excel(name = "专家评审id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long evalExpertEvaluationInfoId;
    /**
     * 专家id
     */
    @ApiModelProperty("项目评审id")
    @Excel(name = "项目评审id")
    private Long projectEvaluationId;
    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
    @Excel(name = "专家id")
    private Long expertResultId;
    /**
     * 评审状态（0未提交 1已提交
     */
    @ApiModelProperty("评审节点（1专家信息确认 2阅读纪律 3专家回避 4推选专家组长 5确认专家组长 6开始评标  99评标结束")
    @Excel(name = "评审节点（1专家信息确认 2阅读纪律 3专家回避 4推选专家组长 5确认专家组长 6开始评标  99评标结束")
    private Integer evalNode;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer delFlag;

    @TableField(exist = false)
    private Long projectId;
    @TableField(exist = false)
    private String expertCode;
}
