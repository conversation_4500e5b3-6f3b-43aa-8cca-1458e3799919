package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 专家评审信息 eval_expert_score_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("专家评分信息")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalExpertScoreInfoMapper.EvalExpertScoreInfoResult")
public class EvalExpertScoreInfo extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 专家评分id
     */
    @ApiModelProperty("专家评审id")
    @Excel(name = "专家评审id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long evalExpertScoreInfoId;
    /**
     * 专家id
     */
    @ApiModelProperty("项目评审id")
    @Excel(name = "项目评审id")
    private Long projectEvaluationId;
    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
    @Excel(name = "专家id")
    private Long expertResultId;
    /**
     * 评分细则id
     */
    @ApiModelProperty("评分细则id")
    @Excel(name = "评分细则id")
    private Long scoringMethodItemId;
    /**
     * 评审状态（0未提交 1已提交
     */
    @ApiModelProperty("评审状态（0未提交 1已提交")
    @Excel(name = "评审状态（0未提交 1已提交")
    private Integer evalState;
    /**
     * 评审原因，通过制未通过时填写
     */
    @ApiModelProperty("评审内容")
    @Excel(name = "评审内容")
    private String evalContent;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer delFlag;

        }
