package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 询标信息对象 eval_inquiring_bid_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("询标信息对象")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalInquiringBidInfoMapper.EvalInquiringBidInfoResult")
public class EvalInquiringBidInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 询标id
     */
    @ApiModelProperty("询标id")
    @Excel(name = "询标id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long inquiringBidId;
    /**
     * 项目评审信息id
     */
    @ApiModelProperty("项目评审信息id")
    @Excel(name = "项目评审信息id")
    private Long projectEvaluationId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
    @Excel(name = "专家id")
    private Long expertResultId;
    /**
     * 询问内容
     */
    @ApiModelProperty("询问内容")
    @Excel(name = "询问内容")
    private String inquiringContent;
    /**
     * 询问时间
     */
    @ApiModelProperty("询问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "询问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date inquiringTime;
    /**
     * 回复内容
     */
    @ApiModelProperty("回复内容")
    @Excel(name = "回复内容")
    private String replyContent;
    /**
     * 回复时间
     */
    @ApiModelProperty("回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;
    /**
     * 回复文件
     */
    @ApiModelProperty("回复文件")
    @Excel(name = "回复文件")
    private String replyFile;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
