package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目评审信息对象 eval_project_evaluation_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("项目评审信息对象")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalProjectEvaluationInfoMapper.EvalProjectEvaluationInfoResult")
public class EvalProjectEvaluationInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目评审信息id
     */
    @ApiModelProperty("项目评审信息id")
    @Excel(name = "项目评审信息id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long projectEvaluationId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 评审时间
     */
    @ApiModelProperty("评审时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评审时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date evaluationTime;
    /**
     * 评审结束时间
     */
    @ApiModelProperty("评审结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评审结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date evaluationEndTime;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private String expertCode;
    @TableField(exist = false)
    private List<BusiBidderInfo> bidderInfos;
}
