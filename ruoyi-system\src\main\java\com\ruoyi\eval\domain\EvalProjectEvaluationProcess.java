package com.ruoyi.eval.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目评审进度对象 eval_project_evaluation_process
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("项目评审进度对象")
@TableName(resultMap = "com.ruoyi.eval.mapper.EvalProjectEvaluationProcessMapper.EvalProjectEvaluationProcessResult")
public class EvalProjectEvaluationProcess extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目评审进度id
     */
    @ApiModelProperty("项目评审进度id")
    @Excel(name = "项目评审进度id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long evaluationProcessId;
    /**
     * 项目评审信息id
     */
    @ApiModelProperty("项目评审信息id")
    @Excel(name = "项目评审信息id")
    private String projectEvaluationId;
    /**
     * 评分细则id
     */
    @ApiModelProperty("评分细则id")
    @Excel(name = "评分细则id")
    private Long scoringMethodItemId;
    /**
     * 评审状态，0未开始 1评审中 10评审结束
     */
    @ApiModelProperty("评审状态，0未开始 1评审中 10评审结束")
    @Excel(name = "评审状态，0未开始 1评审中 10评审结束")
    private Integer evaluationState;
    /**
     * 评审结果，json格式，记录所有供应商的最终评审信息
     */
    @ApiModelProperty("评审结果，json格式，记录所有供应商的最终评审信息")
    @Excel(name = "评审结果，json格式，记录所有供应商的最终评审信息")
    private String evaluationResult;
    /**
     * 评审结果说明，记录对评审结果的说明情况
     */
    @ApiModelProperty("评审结果说明，记录对评审结果的说明情况")
    @Excel(name = "评审结果说明，记录对评审结果的说明情况")
    private String evaluationResultRemark;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /*二次报价开始时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @ApiModelProperty("二次报价次数")
    private Integer num=1;

    @ApiModelProperty("二次报价时间")
    private Integer minutes;
}
