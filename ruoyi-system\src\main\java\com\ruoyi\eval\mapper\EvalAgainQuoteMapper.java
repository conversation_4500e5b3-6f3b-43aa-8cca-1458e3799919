package com.ruoyi.eval.mapper;

import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.eval.domain.EvalAgainQuote;
import org.apache.ibatis.annotations.Param;

/**
 * 二次报价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Mapper
public interface EvalAgainQuoteMapper extends BaseMapper<EvalAgainQuote> {
    EvalAgainQuote getAmountByEntAndProjectEvaluationInfo(@Param("projectEvaluationId") Long projectEvaluationId, @Param("entId") Long entId);
    
}