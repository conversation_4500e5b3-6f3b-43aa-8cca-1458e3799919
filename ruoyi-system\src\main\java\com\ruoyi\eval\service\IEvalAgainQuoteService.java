package com.ruoyi.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalAgainQuote;
import com.ruoyi.eval.vo.GetEvalAgainQuoteVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 二次报价Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalAgainQuoteService extends IService<EvalAgainQuote> {
    /**
     * 查询二次报价列表
     *
     * @param evalAgainQuote 二次报价
     * @return 二次报价集合
     */
    public List<EvalAgainQuote> selectList(EvalAgainQuote evalAgainQuote);

    Map<String,Object> getEvalAgainQuote(GetEvalAgainQuoteVo getEvalAgainQuoteVo);
    Map<String,Object> getEvalAgainQuoteXunJia(GetEvalAgainQuoteVo getEvalAgainQuoteVo);

    Boolean saveEvalAgainQuote(EvalAgainQuote evalAgainQuote);

    public AjaxResult getSecondQuotation(EvalAgainQuote evalAgainQuote);

    BigDecimal getEntAmount(EvalAgainQuote evalAgainQuote);

}
