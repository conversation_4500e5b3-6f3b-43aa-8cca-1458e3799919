package com.ruoyi.eval.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.eval.vo.CheckZhuanJiaPS;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 专家打分详情Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalExpertEvaluationDetailService extends IService<EvalExpertEvaluationDetail> {
    /**
     * 查询专家打分详情列表
     *
     * @param evalExpertEvaluationDetail 专家打分详情
     * @return 专家打分详情集合
     */
    public List<EvalExpertEvaluationDetail> selectList(EvalExpertEvaluationDetail evalExpertEvaluationDetail);
    //资格性评审--通过or不通过
    public Map<String,Object> getByEvalExpertEvaluationDetail(Long projectId, Long itemId,Long resultId);

    //专业性评审  -- 评分
  //  public AjaxResult getByEvalExpertEvaluationDetail1(Long projectId,Long itemId);

    public Map<String, Object> getReviewSummary(Long projectId,Long resultId);

    public  Map<String,Object> getEalExpertEvaluationDetailToGroupLeader(Long projectId,Long itemId) throws IOException;

    Boolean saveEvalExpertEvaluationDetails(List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails);
    public AjaxResult getCheckExpertEvaluationDetail(CheckZhuanJiaPS checkZhuanJiaPS) throws IOException;

    public AjaxResult reviewSummaryConfirmation(CheckZhuanJiaPS checkZhuanJiaPS);


    public AjaxResult getDetailByPsxx(CheckZhuanJiaPS checkZhuanJiaPS);
}
