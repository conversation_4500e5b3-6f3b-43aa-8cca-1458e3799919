package com.ruoyi.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertEvaluationInfo;

import java.io.IOException;
import java.util.List;

/**
 * 专家打分详情Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalExpertEvaluationInfoService extends IService<EvalExpertEvaluationInfo> {
    /**
     * 查询专家打分详情列表
     *
     * @param evalExpertEvaluationInfo 专家打分详情
     * @return 专家打分详情集合
     */
    List<EvalExpertEvaluationInfo> selectList(EvalExpertEvaluationInfo evalExpertEvaluationInfo);

    AjaxResult updateNode(EvalExpertEvaluationInfo evalExpertEvaluationInfo);
    AjaxResult getInfo(EvalExpertEvaluationInfo evalExpertEvaluationInfo);
    AjaxResult getInfo2(EvalExpertEvaluationInfo evalExpertEvaluationInfo);

}
