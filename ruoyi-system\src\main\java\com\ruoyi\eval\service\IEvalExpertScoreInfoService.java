package com.ruoyi.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertScoreInfo;

import java.util.List;

/**
 * 专家打分信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalExpertScoreInfoService extends IService<EvalExpertScoreInfo> {
    /**
     * 查询专家打分信息列表
     *
     * @param evalExpertScoreInfo 专家打分详情
     * @return 专家打分信息集合
     */
    public List<EvalExpertScoreInfo> selectList(EvalExpertScoreInfo evalExpertScoreInfo);

    public AjaxResult updateState(Long evalExpertScoreInfoId, Integer state);

    public AjaxResult addEvalExpertScoreInfo(EvalExpertScoreInfo evalExpertScoreInfo);
    public AjaxResult getEvalExpertScoreInfo(EvalExpertScoreInfo evalExpertScoreInfo);
    public AjaxResult reEvaluation(EvalExpertScoreInfo evalExpertScoreInfo);


}
