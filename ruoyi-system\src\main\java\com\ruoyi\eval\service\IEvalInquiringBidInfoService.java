package com.ruoyi.eval.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalInquiringBidInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.eval.vo.EvalInquiringVo;

/**
 * 询标信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalInquiringBidInfoService extends IService<EvalInquiringBidInfo> {
    /**
     * 查询询标信息列表
     *
     * @param evalInquiringBidInfo 询标信息
     * @return 询标信息集合
     */
    public List<EvalInquiringBidInfo> selectList(EvalInquiringBidInfo evalInquiringBidInfo);

   public AjaxResult  getHistoryMessage(EvalInquiringVo evalInquiringVo);
}
