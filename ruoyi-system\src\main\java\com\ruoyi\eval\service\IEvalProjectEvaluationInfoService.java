package com.ruoyi.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 项目评审信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalProjectEvaluationInfoService extends IService<EvalProjectEvaluationInfo> {
    /**
     * 查询项目评审信息列表
     *
     * @param evalProjectEvaluationInfo 项目评审信息
     * @return 项目评审信息集合
     */
    public List<EvalProjectEvaluationInfo> selectList(EvalProjectEvaluationInfo evalProjectEvaluationInfo);

    public void exportEvalProjectEvaluationInfo(Long projectId, Long resultId, HttpServletResponse response) throws IOException;

  public AjaxResult saveEvalProjectEvaluationInfo(EvalProjectEvaluationInfo evalProjectEvaluationInfo);


   public void saveReviewReport(Long projectId, Long resultId, HttpServletResponse httpServletResponse);

    void updateInfos(EvalProjectEvaluationInfo evalProjectEvaluationInfo);

    EvalProjectEvaluationInfo selectByProject(Long projectId);

}
