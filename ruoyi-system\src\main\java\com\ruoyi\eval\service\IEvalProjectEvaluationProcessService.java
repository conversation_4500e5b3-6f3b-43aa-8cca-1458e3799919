package com.ruoyi.eval.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 项目评审进度Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IEvalProjectEvaluationProcessService extends IService<EvalProjectEvaluationProcess> {
    /**
     * 查询项目评审进度列表
     *
     * @param evalProjectEvaluationProcess 项目评审进度
     * @return 项目评审进度集合
     */
    public List<EvalProjectEvaluationProcess> selectList(EvalProjectEvaluationProcess evalProjectEvaluationProcess);

   // public AjaxResult getProjectEvaluationStatus(@PathVariable Long projectId);
   public AjaxResult reEvaluate(Long evaluationProcessId);
    public AjaxResult saveProjectEvaluationProcess( EvalProjectEvaluationProcess evalProjectEvaluationProcess);

    public AjaxResult updateEvalProjectEvaluationProcess(EvalProjectEvaluationProcess evalProjectEvaluationProcess);

}
