package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.eval.domain.EvalAgainQuote;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.mapper.EvalAgainQuoteMapper;
import com.ruoyi.eval.service.IEvalAgainQuoteService;
import com.ruoyi.eval.service.IEvalExpertEvaluationDetailService;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationProcessService;
import com.ruoyi.eval.vo.GetEvalAgainQuoteVo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 二次报价Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Log4j2
@Service
public class EvalAgainQuoteServiceImpl extends ServiceImpl<EvalAgainQuoteMapper, EvalAgainQuote> implements IEvalAgainQuoteService {
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IBusiBiddingRecordService iBusiBiddingRecordService;
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private IScoringMethodItemService iScoringMethodItemService;
    @Autowired
    private IEvalExpertEvaluationDetailService iEvalExpertEvaluationDetailService;
    @Autowired
    private IBusiExtractExpertResultService iBusiExtractExpertResultService;
    @Autowired
    private IScoringMethodUinfoService iScoringMethodUinfoService;
    @Autowired
    private IEvalProjectEvaluationProcessService evalProjectEvaluationProcessService;

    /**
     * 查询二次报价列表
     *
     * @param evalAgainQuote 二次报价
     * @return 二次报价
     */
    @Override
    public List<EvalAgainQuote> selectList(EvalAgainQuote evalAgainQuote) {
        if (evalAgainQuote.getParams().containsKey("projectId")) {
            EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>()
                    .eq("project_id", evalAgainQuote.getParams().get("projectId")));
            if (null != evaluationInfo) {
                evalAgainQuote.setProjectEvaluationId(evaluationInfo.getProjectEvaluationId().toString());
            } else {
                throw new ServiceException("项目评审信息不存在");
            }
        }
        BaseEntInfo ent = SecurityUtils.getLoginUser().getUser().getEnt();
        if (ent.getEntType() == 3) {
            //只能查自己
            evalAgainQuote.setEntId(ent.getEntId());
            //判断是否废标
            BusiBidderInfo info = busiBidderInfoService.getOne(
                    new QueryWrapper<BusiBidderInfo>().eq("project_id", evalAgainQuote.getParams().get("projectId"))
                            .eq("bidder_id", ent.getEntId()).eq("is_abandoned_bid", 0));
            if (info == null) {
                return new ArrayList<>();
            }
        }
        QueryWrapper<EvalAgainQuote> evalAgainQuoteQueryWrapper = getEvalAgainQuoteQueryWrapper(evalAgainQuote);
        return list(evalAgainQuoteQueryWrapper);
    }

    private QueryWrapper<EvalAgainQuote> getEvalAgainQuoteQueryWrapper(EvalAgainQuote evalAgainQuote) {
        QueryWrapper<EvalAgainQuote> evalAgainQuoteQueryWrapper = new QueryWrapper<>();
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getAgainQuoteId()), "again_quote_id", evalAgainQuote.getAgainQuoteId());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getProjectEvaluationId()), "project_evaluation_id", evalAgainQuote.getProjectEvaluationId());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getEntId()), "ent_id", evalAgainQuote.getEntId());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getQuoteNumber()), "quote_number", evalAgainQuote.getQuoteNumber());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getQuoteAmount()), "quote_amount", evalAgainQuote.getQuoteAmount());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getQuoteFile()), "quote_file", evalAgainQuote.getQuoteFile());
        String beginCreateTime = evalAgainQuote.getParams().get("beginCreateTime") != null ? evalAgainQuote.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = evalAgainQuote.getParams().get("endCreateTime") + "" != null ? evalAgainQuote.getParams().get("endCreateTime") + "" : "";
        evalAgainQuoteQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getCreateBy()), "create_by", evalAgainQuote.getCreateBy());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getRemark()), "remark", evalAgainQuote.getRemark());
        evalAgainQuoteQueryWrapper.eq(ObjectUtil.isNotEmpty(evalAgainQuote.getDelFlag()), "del_flag", evalAgainQuote.getDelFlag());
        evalAgainQuoteQueryWrapper.apply(
                ObjectUtil.isNotEmpty(evalAgainQuote.getParams().get("dataScope")),
                evalAgainQuote.getParams().get("dataScope") + ""
        );
        return evalAgainQuoteQueryWrapper;
    }
    //磋商
    @Transactional
    @Override
    public Map<String, Object> getEvalAgainQuote(GetEvalAgainQuoteVo evalAgainQuoteVo) {
        BusiTenderProject project = busiTenderProjectService.getById(evalAgainQuoteVo.getProjectId());
        BusiTenderNotice tenderNoticeByProjectId = busiTenderNoticeService.getTenderNoticeByProjectId(project.getProjectId());
        //返回值map
        Map<String, Object> map = new HashMap<>();
        map.put("tenderNotice",tenderNoticeByProjectId);
        //1是磋商  0是谈判3是询价4是单一来源
        if (project.getTenderMode().equals("1")){
            //获取抽取专家信息（由于专家没有用户，保存记录时使用）
            BusiExtractExpertResult extractExpertResultServiceById = iBusiExtractExpertResultService.getById(evalAgainQuoteVo.getResultId());

            //供应商ID，报价次数，报价
            Map<Long, Map<String, BigDecimal>> resultMap = new HashMap<>();
            //供应商投标信息 (首次报价)
            log.info("供应商投标信息 (首次报价)");
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("is_abandoned_bid", 0));
            List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("del_flag", 0));
            for (BusiBiddingRecord busiBiddingRecord : biddingRecords) {
                for (BusiBidderInfo info : busiBidderInfos) {
                    if (busiBiddingRecord.getBidderId().equals(info.getBidderId())) {
                        Map<String, BigDecimal> firstMap = new HashMap<>();
                        firstMap.put("首次报价", busiBiddingRecord.getBidAmount());
                        resultMap.put(busiBiddingRecord.getBidderId(), firstMap);
                        break;
                    }
                }
            }
            //获取项目对应的评分规则
            ScoringMethodUinfo scoringMethodUinfo = iScoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", evalAgainQuoteVo.getProjectId()));
            //查询评分办法详细信息（item）
            ScoringMethodItem scoringMethodItem = iScoringMethodItemService.getById(evalAgainQuoteVo.getItemId());
            map.put("scoringMethodItem", scoringMethodItem);
            //查询该项目的评审因素（uitem）
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .in("scoring_method_item_id", evalAgainQuoteVo.getItemId())
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );
            //查询项目评审信息
            EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>()
                    .eq("project_id", evalAgainQuoteVo.getProjectId()));
            if (null != evaluationInfo) {
                //获取二次报价次数
                EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                        .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId())
                        .eq("scoring_method_item_id", evalAgainQuoteVo.getItemId())
                );
                //返回第一行标题名
                List<String> col = new ArrayList<>();
                col.add("首次报价");
                for (int i = 1; i <= evaluationProcess.getNum(); i++) {
                    if (i == evaluationProcess.getNum()) {
                        col.add("最终报价");
                    } else {
                        col.add("第" + (i + 1) + "次报价");
                    }
                }
                col.add("评标基准价");
                col.add("投标报价得分");
                map.put("col", col);
                //查询投标报价记录信息
                List<EvalAgainQuote> evalAgainQuotes = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                        .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId()));
                if (evalAgainQuotes != null && !evalAgainQuotes.isEmpty()) {
                    for (EvalAgainQuote evalAgainQuote : evalAgainQuotes) {
                        //如果小微企业包含记录id

                        EvalExpertEvaluationDetail evalExpertEvaluationDetail = iEvalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                                .eq("scoring_method_uitem_id", scoringMethodUitems.get(0).getEntMethodItemId())
                                .eq("expert_result_id", evalAgainQuoteVo.getResultId())
                                .eq("ent_id", evalAgainQuote.getEntId()));
                        boolean b = false;
                        if (evalAgainQuoteVo.getCxbj()) {
                            if (evalAgainQuoteVo.getXwqys().contains(evalAgainQuote.getEntId())) {
                                b = true;
                            }
                        } else {
                            if (null != evalExpertEvaluationDetail) {
                                b = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                            }
                            if (b) {
                                evalAgainQuoteVo.getXwqys().add(evalAgainQuote.getEntId());
                            }
                        }
                        if (b) {
                            evalAgainQuote.setQuoteAmount(evalAgainQuote.getQuoteAmount().multiply(new BigDecimal("0.8")));
                        }
                    }
                    //每个供应商报价集合
                    Map<Long, List<EvalAgainQuote>> evalAgainQuote = evalAgainQuotes.stream().collect(Collectors.groupingBy(EvalAgainQuote::getEntId));
                    for (Long l : resultMap.keySet()) {


                        Map<String, BigDecimal> entMap = resultMap.get(l);
                        for (int i = 1; i <= evaluationProcess.getNum(); i++) {
                            //查询detail值，判断是否存在
                            EvalExpertEvaluationDetail evalExpertEvaluationDetail = iEvalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                                    .eq("scoring_method_uitem_id", scoringMethodUitems.get(0).getEntMethodItemId())
                                    .eq("expert_result_id", evalAgainQuoteVo.getResultId())
                                    .eq("ent_id", l));
                            //如果是重新打分，则重置小微企业标识，否则按原有处理，若detail不存在则视为第一次进入，所有用户均不是小微企业
                            boolean isXwqj = false;
                            if (evalAgainQuoteVo.getCxbj()) {
                                if (evalAgainQuoteVo.getXwqys().contains(l)) {
                                    isXwqj = true;
                                    evalExpertEvaluationDetail.setRemark("xwqy");
                                } else {
                                    isXwqj = false;
                                    evalExpertEvaluationDetail.setRemark("");
                                }
                            }
                            else {
                                if (null == evalExpertEvaluationDetail) {
                                    evalExpertEvaluationDetail = new EvalExpertEvaluationDetail();
                                    evalExpertEvaluationDetail.setScoringMethodUitemId(scoringMethodUitems.get(0).getEntMethodItemId());
                                    evalExpertEvaluationDetail.setExpertResultId(evalAgainQuoteVo.getResultId());
                                    evalExpertEvaluationDetail.setEntId(l);
                                    evalExpertEvaluationDetail.setRemark("");
                                } else {
                                    isXwqj = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                                }
                            }

                            // 假设你的BusiBidderInfo类定义了id和xwqy属性，并且有相应的getter和setter方法
                            for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
                                long bidderId = busiBidderInfo.getBidderId();
                                if (bidderId == l) { // 假设id是int类型
                                    if (isXwqj) {
                                        busiBidderInfo.setXwqy(1);
                                    } else {
                                        busiBidderInfo.setXwqy(0);
                                    }
                                    break;
                                }
                            }
                            //评标基准价
                            BigDecimal minQuoteAmount = evalAgainQuotes.stream().map(EvalAgainQuote::getQuoteAmount).min(BigDecimal::compareTo).orElse(null);
                            // 检查最小值是否存在，并打印它
                            if (minQuoteAmount != null) {
                                entMap.put("评标基准价", minQuoteAmount);
                            }
                            if (!evalAgainQuote.get(l).isEmpty()) {
                                List<EvalAgainQuote> againQuotes = evalAgainQuote.get(l);
                                // 假设 EvalAgainQuote 类有 getNum() 方法返回数值类型（如 int, long, double 等）
                                Optional<EvalAgainQuote> maxQuoteOptional = againQuotes.stream()
                                        .max(Comparator.comparingInt(EvalAgainQuote::getQuoteNumber)); // 或使用 getNum() 返回的类型对应的比较器
                                // 获取最大值记录（需处理可能为空的情况）
                                EvalAgainQuote maxQuote = maxQuoteOptional.orElse(null);
                                if (null!=maxQuote){
                                    entMap.put("最终报价", maxQuote.getQuoteAmount());
                                }
                                for (EvalAgainQuote againQuote : evalAgainQuote.get(l)) {
                                    if (i == evaluationProcess.getNum()) {
                                        entMap.put("最终报价", againQuote.getQuoteAmount());
                                    } else {
                                        entMap.put("第" + (i + 1) + "次报价", againQuote.getQuoteAmount());
                                    }
                                }
                            } else {
                                System.out.println("Map is empty.");
                            }
                            //投标报价得分=评标基准价/最终报价*投标报价最大分值
                            //minBigDecimal/quoteWithMaxQuoteNumber.getQuoteAmount()*scoringMethodUitems.get(0).getMaxScore()
                            BigDecimal divide = minQuoteAmount.divide(entMap.get("最终报价"), BigDecimal.ROUND_HALF_UP);
                            BigDecimal maxScore = BigDecimal.valueOf(scoringMethodUitems.get(0).getMaxScore());
                            // 执行乘法操作
                            BigDecimal result = divide.multiply(maxScore);
                            entMap.put("投标报价得分", result);
//                        if (evalAgainQuoteVo.getCxbj()) {
                            evalExpertEvaluationDetail.setEvaluationResult(result + "");
                            evalExpertEvaluationDetail.getParams().put("opUser", extractExpertResultServiceById.getExpertName());
                            iEvalExpertEvaluationDetailService.saveOrUpdate(evalExpertEvaluationDetail);

                        }
                    }
                } else {
                    // 列表为空，进行相应的处理
                }
            }
            map.put("resultMap", resultMap);
            map.put("busiBidderInfos", busiBidderInfos);
        }
        else if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
            //获取抽取专家信息（由于专家没有用户，保存记录时使用）
            BusiExtractExpertResult extractExpertResultServiceById = iBusiExtractExpertResultService.getById(evalAgainQuoteVo.getResultId());
            //供应商ID，报价次数，报价
            Map<Long, Map<String, BigDecimal>> resultMap = new HashMap<>();
            //供应商投标信息 (首次报价)
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("is_abandoned_bid", 0));
            List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("del_flag", 0));
            for (BusiBiddingRecord busiBiddingRecord : biddingRecords) {
                Map<String, BigDecimal> firstMap = new HashMap<>();
                firstMap.put("最终报价", busiBiddingRecord.getBidAmount());
                resultMap.put(busiBiddingRecord.getBidderId(), firstMap);
            }
            //获取项目对应的评分规则
            ScoringMethodUinfo scoringMethodUinfo = iScoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", evalAgainQuoteVo.getProjectId()));
            //查询评分办法详细信息（item）
            ScoringMethodItem scoringMethodItem = iScoringMethodItemService.getById(evalAgainQuoteVo.getItemId());
            map.put("scoringMethodItem", scoringMethodItem);
            //查询该项目的评审因素（uitem）
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .in("scoring_method_item_id", evalAgainQuoteVo.getItemId())
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );
            //查询项目评审信息
            EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>()
                    .eq("project_id", evalAgainQuoteVo.getProjectId()));

            if (null != evaluationInfo) {
                //获取二次报价次数
                EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                        .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId())
                        .eq("scoring_method_item_id", evalAgainQuoteVo.getItemId())
                );
                //返回第一行标题名
                List<String> col = new ArrayList<>();
                col.add("最终报价");
                /*for (int i = 1; i <= evaluationProcess.getNum(); i++) {
                    if (i == evaluationProcess.getNum()) {
                        col.add("最终报价");
                    } else {
                        col.add("第" + (i + 1) + "次报价");
                    }
                }
                col.add("评标基准价");
                col.add("投标报价得分");*/
                map.put("col", col);
                //查询投标报价记录信息
                //List<EvalAgainQuote> evalAgainQuotes = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                //        .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId()));
                //if (evalAgainQuotes != null && !evalAgainQuotes.isEmpty()) {
                    for (BusiBiddingRecord biddingRecord : biddingRecords) {
                        //如果小微企业包含记录id
                        EvalExpertEvaluationDetail evalExpertEvaluationDetail = iEvalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                                .eq("scoring_method_uitem_id", scoringMethodUitems.get(0).getEntMethodItemId())
                                .eq("expert_result_id", evalAgainQuoteVo.getResultId())
                                .eq("ent_id", biddingRecord.getBidderId()));
                        boolean b = false;
                        if (evalAgainQuoteVo.getCxbj()) {
                            if (evalAgainQuoteVo.getXwqys().contains(biddingRecord.getBidderId())) {
                                b = true;
                            }
                        } else {
                            if (null != evalExpertEvaluationDetail) {
                                b = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                            }
                            if (b) {
                                evalAgainQuoteVo.getXwqys().add(biddingRecord.getBidderId());
                            }
                        }
                        if (b) {
                            biddingRecord.setBidAmount(biddingRecord.getBidAmount().multiply(new BigDecimal("0.8")));
                            resultMap.get(biddingRecord.getBidderId()).put("最终报价",biddingRecord.getBidAmount());
                        }
                        //如果是重新打分，则重置小微企业标识，否则按原有处理，若detail不存在则视为第一次进入，所有用户均不是小微企业
                        boolean isXwqj = false;
                        if (evalAgainQuoteVo.getCxbj()) {
                            if (evalAgainQuoteVo.getXwqys().contains(biddingRecord.getBidderId())) {
                                isXwqj = true;
                                evalExpertEvaluationDetail.setRemark("xwqy");
                            } else {
                                isXwqj = false;
                                evalExpertEvaluationDetail.setRemark("");
                            }
                        }
                        else {
                            if (null == evalExpertEvaluationDetail) {
                                evalExpertEvaluationDetail = new EvalExpertEvaluationDetail();
                                evalExpertEvaluationDetail.setScoringMethodUitemId(scoringMethodUitems.get(0).getEntMethodItemId());
                                evalExpertEvaluationDetail.setExpertResultId(evalAgainQuoteVo.getResultId());
                                evalExpertEvaluationDetail.setEntId(biddingRecord.getBidderId());
                                evalExpertEvaluationDetail.setRemark("");
                            } else {
                                isXwqj = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                            }
                        }
                        // 假设你的BusiBidderInfo类定义了id和xwqy属性，并且有相应的getter和setter方法
                        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
                            long bidderId = busiBidderInfo.getBidderId();
                            if (bidderId == biddingRecord.getBidderId()) { // 假设id是int类型
                                if (isXwqj) {
                                    busiBidderInfo.setXwqy(1);
                                } else {
                                    busiBidderInfo.setXwqy(0);
                                }
                                break;
                            }
                        }
                        evalExpertEvaluationDetail.setEvaluationResult(biddingRecord.getBidAmount() + "");
                        evalExpertEvaluationDetail.getParams().put("opUser", extractExpertResultServiceById.getExpertName());
                        iEvalExpertEvaluationDetailService.saveOrUpdate(evalExpertEvaluationDetail);
                    }
            }
            map.put("resultMap", resultMap);
            map.put("busiBidderInfos", busiBidderInfos);
        }

        return map;
    }
    //询价
    @Transactional
    @Override
    public Map<String, Object> getEvalAgainQuoteXunJia(GetEvalAgainQuoteVo evalAgainQuoteVo) {
        //获取抽取专家信息（由于专家没有用户，保存记录时使用）
        BusiExtractExpertResult extractExpertResultServiceById = iBusiExtractExpertResultService.getById(evalAgainQuoteVo.getResultId());
        //返回值map
        Map<String, Object> map = new HashMap<>();
        //供应商ID，报价次数，报价
        Map<Long, Map<String, BigDecimal>> resultMap = new HashMap<>();
        //供应商投标信息 (首次报价)
        log.info("供应商投标信息 (首次报价)");
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("is_abandoned_bid", 0));
        List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("project_id", evalAgainQuoteVo.getProjectId()).eq("del_flag", 0));
        for (BusiBiddingRecord busiBiddingRecord : biddingRecords) {
            Map<String, BigDecimal> firstMap = new HashMap<>();
            firstMap.put("最终报价", busiBiddingRecord.getBidAmount());
            resultMap.put(busiBiddingRecord.getBidderId(), firstMap);
        }
        //获取项目对应的评分规则
        ScoringMethodUinfo scoringMethodUinfo = iScoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", evalAgainQuoteVo.getProjectId()));
        //查询评分办法详细信息（item）
        ScoringMethodItem scoringMethodItem = iScoringMethodItemService.getById(evalAgainQuoteVo.getItemId());
        map.put("scoringMethodItem", scoringMethodItem);
        //查询该项目的评审因素（uitem）
        List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                .in("scoring_method_item_id", evalAgainQuoteVo.getItemId())
                .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
        );
        //查询项目评审信息
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>()
                .eq("project_id", evalAgainQuoteVo.getProjectId()));
        if (null != evaluationInfo) {
            //获取二次报价次数
            EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                    .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId())
                    .eq("scoring_method_item_id", evalAgainQuoteVo.getItemId())
            );
            //返回第一行标题名
            List<String> col = new ArrayList<>();
            col.add("首次报价");
//            for (int i = 1; i <= evaluationProcess.getNum(); i++) {
//                if (i == evaluationProcess.getNum()) {
//                    col.add("最终报价");
//                } else {
//                    col.add("第" + (i + 1) + "次报价");
//                }
//            }
//            col.add("评标基准价");
            //col.add("投标报价得分");
            map.put("col", col);
            //查询投标报价记录信息
            List<EvalAgainQuote> evalAgainQuotes = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                    .eq("project_evaluation_id", evaluationInfo.getProjectEvaluationId()));
            if (evalAgainQuotes != null && !evalAgainQuotes.isEmpty()) {
                for (EvalAgainQuote evalAgainQuote : evalAgainQuotes) {
                    //如果小微企业包含记录id
                    EvalExpertEvaluationDetail evalExpertEvaluationDetail = iEvalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                            .eq("scoring_method_uitem_id", scoringMethodUitems.get(0).getEntMethodItemId())
                            .eq("expert_result_id", evalAgainQuoteVo.getResultId())
                            .eq("ent_id", evalAgainQuote.getEntId()));
                    boolean b = false;
                    if (evalAgainQuoteVo.getCxbj()) {
                        if (evalAgainQuoteVo.getXwqys().contains(evalAgainQuote.getEntId())) {
                            b = true;
                        }
                    }
                    else {
                        if (null != evalExpertEvaluationDetail) {
                            b = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                        }
                        if (b) {
                            evalAgainQuoteVo.getXwqys().add(evalAgainQuote.getEntId());
                        }
                    }
                    if (b) {
                        evalAgainQuote.setQuoteAmount(evalAgainQuote.getQuoteAmount().multiply(new BigDecimal("0.8")));
                    }
                }
                //每个供应商报价集合
                Map<Long, List<EvalAgainQuote>> evalAgainQuote = evalAgainQuotes.stream().collect(Collectors.groupingBy(EvalAgainQuote::getEntId));
                for (Long l : resultMap.keySet()) {
                    Map<String, BigDecimal> entMap = resultMap.get(l);
                    for (int i = 1; i <= evaluationProcess.getNum(); i++) {
                        //查询detail值，判断是否存在
                        EvalExpertEvaluationDetail evalExpertEvaluationDetail = iEvalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                                .eq("scoring_method_uitem_id", scoringMethodUitems.get(0).getEntMethodItemId())
                                .eq("expert_result_id", evalAgainQuoteVo.getResultId())
                                .eq("ent_id", l));
                        //如果是重新打分，则重置小微企业标识，否则按原有处理，若detail不存在则视为第一次进入，所有用户均不是小微企业
                        boolean isXwqj = false;
                        if (evalAgainQuoteVo.getCxbj()) {
                            if (evalAgainQuoteVo.getXwqys().contains(l)) {
                                isXwqj = true;
                                evalExpertEvaluationDetail.setRemark("xwqy");
                            } else {
                                isXwqj = false;
                                evalExpertEvaluationDetail.setRemark("");
                            }
                        } else {
                            if (null == evalExpertEvaluationDetail) {
                                evalExpertEvaluationDetail = new EvalExpertEvaluationDetail();
                                evalExpertEvaluationDetail.setScoringMethodUitemId(scoringMethodUitems.get(0).getEntMethodItemId());
                                evalExpertEvaluationDetail.setExpertResultId(evalAgainQuoteVo.getResultId());
                                evalExpertEvaluationDetail.setEntId(l);
                                evalExpertEvaluationDetail.setRemark("");
                            } else {
                                isXwqj = "xwqy".equals(evalExpertEvaluationDetail.getRemark());
                            }
                        }

                        // 假设你的BusiBidderInfo类定义了id和xwqy属性，并且有相应的getter和setter方法
                        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
                            long bidderId = busiBidderInfo.getBidderId();
                            if (bidderId == l) { // 假设id是int类型
                                if (isXwqj) {
                                    busiBidderInfo.setXwqy(1);
                                } else {
                                    busiBidderInfo.setXwqy(0);
                                }
                                break;
                            }
                        }
                        //评标基准价
                        BigDecimal minQuoteAmount = evalAgainQuotes.stream().map(EvalAgainQuote::getQuoteAmount).min(BigDecimal::compareTo).orElse(null);
                        // 检查最小值是否存在，并打印它
                        if (minQuoteAmount != null) {
                            entMap.put("评标基准价", minQuoteAmount);
                        }
                        if (!evalAgainQuote.get(l).isEmpty()) {
                            for (EvalAgainQuote againQuote : evalAgainQuote.get(l)) {
                                if (i == evaluationProcess.getNum()) {
                                    entMap.put("最终报价", againQuote.getQuoteAmount());
                                } else {
                                    entMap.put("第" + (i + 1) + "次报价", againQuote.getQuoteAmount());
                                }
                            }
                        } else {
                            System.out.println("Map is empty.");
                        }
                        //投标报价得分=评标基准价/最终报价*投标报价最大分值
                        //minBigDecimal/quoteWithMaxQuoteNumber.getQuoteAmount()*scoringMethodUitems.get(0).getMaxScore()
                        BigDecimal divide = minQuoteAmount.divide(entMap.get("最终报价"), BigDecimal.ROUND_HALF_UP);
                        BigDecimal maxScore = BigDecimal.valueOf(scoringMethodUitems.get(0).getMaxScore());
                        // 执行乘法操作
                        BigDecimal result = divide.multiply(maxScore);
                       // entMap.put("投标报价得分", result);
//                        if (evalAgainQuoteVo.getCxbj()) {
                        evalExpertEvaluationDetail.setEvaluationResult(result + "");
                        evalExpertEvaluationDetail.getParams().put("opUser", extractExpertResultServiceById.getExpertName());
                        iEvalExpertEvaluationDetailService.saveOrUpdate(evalExpertEvaluationDetail);
                    }
                }
            }
            else {
                // 列表为空，进行相应的处理
                //询价没有二次报价记录
            }
        }
        map.put("resultMap", resultMap);
        map.put("busiBidderInfos", busiBidderInfos);
        return map;
    }

    @Override
    public Boolean saveEvalAgainQuote(EvalAgainQuote evalAgainQuote) {


        //先查询是否存在记录
        List<EvalAgainQuote> list = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                .eq("project_evaluation_id", evalAgainQuote.getProjectEvaluationId())
                .eq("ent_id", evalAgainQuote.getEntId())
        );
        if (!list.isEmpty()) {
            // 使用Stream API来找到最大的QuoteNumber
            Optional<Integer> maxQuoteNumber = list.stream()
                    .map(EvalAgainQuote::getQuoteNumber)
                    .max(Integer::compare);
            maxQuoteNumber.ifPresent(number -> System.out.println("最大的QuoteNumber是: " + number));
            evalAgainQuote.setQuoteNumber(maxQuoteNumber.get() + 1);
        } else {
            evalAgainQuote.setQuoteNumber(1);
        }
        // 判断是否过期
        checIsCs(evalAgainQuote);
        return evalAgainQuoteService.saveOrUpdate(evalAgainQuote);
    }

    private void checIsCs(EvalAgainQuote evalAgainQuote) {
        List<EvalProjectEvaluationProcess> list1 = evalProjectEvaluationProcessService.list(new QueryWrapper<EvalProjectEvaluationProcess>()
                .eq("project_evaluation_id", evalAgainQuote.getProjectEvaluationId())
                .eq("remark", "二次报价"));
        if(null!=list1 && list1.size()>0){
            EvalProjectEvaluationProcess evalProjectEvaluationProcess = list1.get(0);
            Date startTime = evalProjectEvaluationProcess.getStartTime();
            Integer minutes = evalProjectEvaluationProcess.getMinutes();
// 将分钟数转换为毫秒数（因为Date的时间计算底层是基于毫秒的）
            long millisecondsToAdd = minutes * 60 * 1000L;
            // 获取当前开始时间对应的毫秒数
            long currentTimeMillis = startTime.getTime();
            // 计算加上分钟数后的毫秒数
            long newTimeMillis = currentTimeMillis + millisecondsToAdd;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 创建新的Date对象表示加上分钟后的时间
            Date newTime = new Date(newTimeMillis);
            System.out.println("原始开始时间: " + sdf.format(startTime));
            System.out.println("加上 " + minutes + " 分钟后的时间: " + sdf.format(newTime));
            // 获取当前时间
            Date currentTime = new Date();
            // 比较当前时间和新生成的时间
            if (currentTime.after(newTime)) {
                System.out.println("已经超时，当前时间：" + sdf.format(currentTime) + "，设定的超时时间：" + sdf.format(newTime));
                throw new ServiceException("已经超时，当前时间：" + sdf.format(currentTime) + "，设定的超时时间：" + sdf.format(newTime));
            } else {
                System.out.println("未超时，当前时间：" + sdf.format(currentTime) + "，设定的超时时间：" + sdf.format(newTime));
            }
        }
    }


    @Override
    public AjaxResult getSecondQuotation(EvalAgainQuote evalAgainQuote) {
        List<EvalAgainQuote> projectEvaluationId = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                .eq("project_evaluation_id", evalAgainQuote.getProjectEvaluationId())
                .eq("ent_id", evalAgainQuote.getEntId())
        );
        return AjaxResult.success(projectEvaluationId);
    }

    @Override
    public BigDecimal getEntAmount(EvalAgainQuote evalAgainQuote) {
        try {
            EvalProjectEvaluationInfo evalProjectEvaluationInfo = evalProjectEvaluationInfoService.selectByProject(evalAgainQuote.getProjectId());
            EvalAgainQuote quote = baseMapper.getAmountByEntAndProjectEvaluationInfo(evalProjectEvaluationInfo.getProjectEvaluationId(), evalAgainQuote.getEntId());
            if (quote != null) {
                return quote.getQuoteAmount();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
