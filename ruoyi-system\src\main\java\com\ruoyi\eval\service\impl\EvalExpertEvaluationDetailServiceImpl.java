package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiBiddingRecord;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertScoreInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.service.*;
import com.ruoyi.eval.vo.CheckZhuanJiaPS;
import com.ruoyi.eval.vo.EvaluationResultVo;
import com.ruoyi.eval.vo.GetEvalAgainQuoteVo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.eval.mapper.EvalExpertEvaluationDetailMapper;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import org.springframework.transaction.annotation.Transactional;

/**
 * 专家打分详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalExpertEvaluationDetailServiceImpl extends ServiceImpl<EvalExpertEvaluationDetailMapper, EvalExpertEvaluationDetail> implements IEvalExpertEvaluationDetailService {
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IScoringMethodItemService scoringMethodItemService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private IEvalExpertEvaluationDetailService evalExpertEvaluationDetailService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    private IEvalProjectEvaluationProcessService evalProjectEvaluationProcessService;
    @Autowired
    private IEvalExpertScoreInfoService iEvalExpertScoreInfoService;
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    @Autowired
    IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IBusiBiddingRecordService iBusiBiddingRecordService;
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;

    /**
     * 查询专家打分详情列表
     *
     * @param evalExpertEvaluationDetail 专家打分详情
     * @return 专家打分详情
     */
    @Override
    public List<EvalExpertEvaluationDetail> selectList(EvalExpertEvaluationDetail evalExpertEvaluationDetail) {
        QueryWrapper<EvalExpertEvaluationDetail> evalExpertEvaluationDetailQueryWrapper = new QueryWrapper<>();
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getExpertEvaluationId()), "expert_evaluation_id", evalExpertEvaluationDetail.getExpertEvaluationId());
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getScoringMethodUitemId()), "scoring_method_uitem_id", evalExpertEvaluationDetail.getScoringMethodUitemId());
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getExpertResultId()), "expert_result_id", evalExpertEvaluationDetail.getExpertResultId());
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getEntId()), "ent_id", evalExpertEvaluationDetail.getEntId());
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getEvaluationResult()), "evaluation_result", evalExpertEvaluationDetail.getEvaluationResult());
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getEvaluationRemark()), "evaluation_remark", evalExpertEvaluationDetail.getEvaluationRemark());
        String beginCreateTime = evalExpertEvaluationDetail.getParams().get("beginCreateTime") != null ? evalExpertEvaluationDetail.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = evalExpertEvaluationDetail.getParams().get("endCreateTime") + "" != null ? evalExpertEvaluationDetail.getParams().get("endCreateTime") + "" : "";
        evalExpertEvaluationDetailQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        evalExpertEvaluationDetailQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getDelFlag()), "del_flag", evalExpertEvaluationDetail.getDelFlag());
        evalExpertEvaluationDetailQueryWrapper.apply(
                ObjectUtil.isNotEmpty(evalExpertEvaluationDetail.getParams().get("dataScope")),
                evalExpertEvaluationDetail.getParams().get("dataScope") + ""
        );
        return list(evalExpertEvaluationDetailQueryWrapper);
    }

    /*  public AjaxResult getByEvalExpertEvaluationDetail(Long projectId, Long itemId) {
          ScoringMethodItem scoringMethodItem = scoringMethodItemService.getById(itemId);
          if (scoringMethodItem == null) {
              return AjaxResult.error("评分细则不存在");
          }

          List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>().eq("scoring_method_item_id", itemId));
          if (CollectionUtils.isEmpty(scoringMethodUitems)) {
              return AjaxResult.error("评分因素集合为空");
          }

          // 如果是评分模式，添加汇总列
          if (scoringMethodItem.getItemMode() == 1) {
              ScoringMethodUitem summaryItem = new ScoringMethodUitem();
              summaryItem.setEntMethodItemId(1L);
              summaryItem.setItemName("汇总");
              scoringMethodUitems.add(summaryItem);
          }

          List<Long> uitemsIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
          List<EvalExpertEvaluationDetail> scoringMethodUitemId = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>().in("scoring_method_uitem_id", uitemsIds));
          Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = scoringMethodUitemId.stream()
                  .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));

          List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));

          Map<String, Object> resultMap = new HashMap<>();
          resultMap.put("tableColumns", scoringMethodUitems);
          resultMap.put("busiBidderInfos", busiBidderInfos);

          List<Map<Object, Object>> tableData = new ArrayList<>();
          for (Long entId : groupedByEntId.keySet()) {
              Map<Object, Object> map = new HashMap<>();
              map.put("gys", entId);
              List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = groupedByEntId.get(entId);

              if (scoringMethodItem.getItemMode() == 1) {
                  addScoreData(map, scoringMethodUitems, evalExpertEvaluationDetails);
              } else if (scoringMethodItem.getItemMode() == 2) {
                  addPassData(map, scoringMethodUitems, evalExpertEvaluationDetails);
              }

              tableData.add(map);
          }
          resultMap.put("tableData", tableData);

          return AjaxResult.success(resultMap);
      }

      private void addScoreData(Map<Object, Object> map, List<ScoringMethodUitem> scoringMethodUitems, List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails) {
          double sum = evalExpertEvaluationDetails.stream()
                  .mapToDouble(this::parseEvaluationResult)
                  .sum();

          scoringMethodUitems.forEach(scoringMethodUitem -> {
              evalExpertEvaluationDetails.stream()
                      .filter(detail -> Objects.equals(scoringMethodUitem.getEntMethodItemId(), detail.getScoringMethodUitemId()))
                      .findFirst()
                      .ifPresent(detail -> map.put(scoringMethodUitem.getEntMethodItemId(), detail.getEvaluationResult()));

              if (scoringMethodUitem.getEntMethodItemId() == 1L) {
                  map.put(scoringMethodUitem.getEntMethodItemId(), sum);
              }
          });
      }

      private void addPassData(Map<Object, Object> map, List<ScoringMethodUitem> scoringMethodUitems, List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails) {
          scoringMethodUitems.forEach(scoringMethodUitem -> {
              evalExpertEvaluationDetails.stream()
                      .filter(detail -> Objects.equals(scoringMethodUitem.getEntMethodItemId(), detail.getScoringMethodUitemId()))
                      .findFirst()
                      .ifPresent(detail -> map.put(scoringMethodUitem.getEntMethodItemId(), detail.getEvaluationResult()));
          });
      }

      private double parseEvaluationResult(EvalExpertEvaluationDetail detail) {
          try {
              return Double.parseDouble(detail.getEvaluationResult());
          } catch (NumberFormatException e) {
              return 0.0;
          }
      }
  */
    @Transactional
    @Override
    //通过汇总
    public Map<String, Object> getByEvalExpertEvaluationDetail(Long projectId, Long itemId, Long resultId) {
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));

        //评分细则
        ScoringMethodItem scoringMethodItem = scoringMethodItemService.getById(itemId);
        Map<String, Object> resultMap = new HashMap<>();

        //1是评分 2是通过
        if (scoringMethodItem.getItemMode() == 1) {
            //通过itemid查询出所有的评分因素集合
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(
                    new QueryWrapper<ScoringMethodUitem>()
                            .eq("scoring_method_item_id", itemId)
                            .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );

            //
            ScoringMethodUitem scoringMethodUitemSum = new ScoringMethodUitem();
            scoringMethodUitemSum.setEntMethodItemId(1L);
            scoringMethodUitemSum.setItemName("汇总");
            scoringMethodUitems.add(scoringMethodUitemSum);

            //获取因素id集合
            List<Long> uitemsIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            //表头数据
            resultMap.put("tableColumns", scoringMethodUitems);
            //查询该因素所有评分信息
            if (uitemsIds == null || uitemsIds.isEmpty()) {
                resultMap.put("tableData", new ArrayList<>());
                //供应商信息
                List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
                resultMap.put("busiBidderInfos", busiBidderInfos);
                return resultMap;
            }
            List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = evalExpertEvaluationDetailService.list(
                    new QueryWrapper<EvalExpertEvaluationDetail>()
                            .in("scoring_method_uitem_id", uitemsIds)
                            .eq("expert_result_id", resultId)
            );
            //表体数据
            List<Map<String, Object>> tableData = new ArrayList<>();
            if (!evalExpertEvaluationDetails.isEmpty()) {
                //获取每个供应商的评分因素评分数据
                Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = evalExpertEvaluationDetails.stream()
                        .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));
                for (Long l : groupedByEntId.keySet()) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("gys", l);

                    //根据因素id进行分组，
                    Map<Long, List<EvalExpertEvaluationDetail>> groupedByUitemIdForEnt = groupedByEntId.get(l).stream()
                            .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getScoringMethodUitemId));
                    //循环因素
                    for (ScoringMethodUitem scoringMethodUitem : scoringMethodUitems) {
                        if (scoringMethodUitem.getEntMethodItemId() == 1L) {
                            double sum = groupedByEntId.get(l).stream()
                                    .mapToDouble(detail -> parseEvaluationResult(detail.getEvaluationResult()))
                                    .sum();
                            map.put(scoringMethodUitem.getEntMethodItemId().toString(), sum);
                        } else {
                            if (groupedByUitemIdForEnt.containsKey(scoringMethodUitem.getEntMethodItemId())) {
                                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails1 = groupedByUitemIdForEnt.get(scoringMethodUitem.getEntMethodItemId());
                                double sum = evalExpertEvaluationDetails1.stream()
                                        .mapToDouble(detail -> parseEvaluationResult(detail.getEvaluationResult()))
                                        .sum();
                                map.put(scoringMethodUitem.getEntMethodItemId().toString(), sum);
                            } else {
                                map.put(scoringMethodUitem.getEntMethodItemId().toString(), 0);
                            }
                        }

                    }

                    tableData.add(map);
                }
                resultMap.put("tableData", tableData);

            } else {
                resultMap.put("tableData", tableData);
            }

            //供应商信息
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
            resultMap.put("busiBidderInfos", busiBidderInfos);
            return resultMap;
        } else if (scoringMethodItem.getItemMode() == 2) {
            //通过itemid查询出所有的评分因素集合
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("scoring_method_item_id", itemId)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())

            );
            //获取因素id集合
            List<Long> uitemsIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            //查询该因素所有评分信息
            if (uitemsIds == null || uitemsIds.isEmpty()) {
                resultMap.put("tableColumns", scoringMethodUitems);
                resultMap.put("tableData", new ArrayList<>());
                //供应商信息
                List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
                resultMap.put("busiBidderInfos", busiBidderInfos);
                return resultMap;
            }
            List<EvalExpertEvaluationDetail> evaluationDetails = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>()
                    .in("scoring_method_uitem_id", uitemsIds)
                    .eq("expert_result_id", resultId)
            );
            // 判断列表不为空
            if (!evaluationDetails.isEmpty()) {
                // 列表不为空，可以进行后续操作
                //获取每个供应商的评分因素评分数据
                Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = evaluationDetails.stream()
                        .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));

                //表头数据
                resultMap.put("tableColumns", scoringMethodUitems);
                //表体数据
                List<Map<Object, Object>> tableData = new ArrayList<>();
                for (Long l : groupedByEntId.keySet()) {
                    Map<Object, Object> map = new HashMap<>();
                    map.put("gys", l);
                    List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = groupedByEntId.get(l);
                    for (ScoringMethodUitem scoringMethodUitem : scoringMethodUitems) {
                        for (EvalExpertEvaluationDetail evalExpertEvaluationDetail : evalExpertEvaluationDetails) {
                            if (Objects.equals(scoringMethodUitem.getEntMethodItemId(), evalExpertEvaluationDetail.getScoringMethodUitemId())) {
                                map.put(scoringMethodUitem.getEntMethodItemId(), evalExpertEvaluationDetail.getEvaluationResult());
                            }
                        }
                    }
                    tableData.add(map);
                }
                resultMap.put("tableData", tableData);

                //供应商信息
                List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
                resultMap.put("busiBidderInfos", busiBidderInfos);
                return resultMap;
            } else {
                return resultMap;

            }
        }
        return resultMap;
    }


    @Transactional
    /*
        //评分汇总
        public AjaxResult getByEvalExpertEvaluationDetail1(Long projectId,Long itemId) {
            //通过itemid查询出所有的评分因素集合
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>().eq("scoring_method_item_id", itemId));

            //
            ScoringMethodUitem scoringMethodUitemSum=new ScoringMethodUitem();
            scoringMethodUitemSum.setEntMethodItemId(1L);
            scoringMethodUitemSum.setItemName("汇总");
            scoringMethodUitems.add(scoringMethodUitemSum);

            //获取因素id集合
            List<Long> uitemsIds=scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            //查询该因素所有评分信息
            List<EvalExpertEvaluationDetail> scoringMethodUitemId = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>().in("scoring_method_uitem_id", uitemsIds));
            //获取每个供应商的评分因素评分数据
            Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = scoringMethodUitemId.stream()
                    .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));

            Map<String,Object> resultMap=new HashMap<>();
            //表头数据
            resultMap.put("tableColumns",scoringMethodUitems);
            //表体数据
            List<Map<Object,Object>> tableData=new ArrayList<>();
            for (Long l : groupedByEntId.keySet()) {
                Map<Object,Object> map=new HashMap<>();
                map.put("gys",l);
                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = groupedByEntId.get(l);
                // 计算总和
                double sum = evalExpertEvaluationDetails.stream()
                        .mapToDouble(detail -> {
                            try {
                                return Double.parseDouble(detail.getEvaluationResult());
                            } catch (NumberFormatException e) {
                                // 如果无法解析，则返回 0.0
                                return 0.0;
                            }
                        })
                        .sum();
                for (ScoringMethodUitem scoringMethodUitem : scoringMethodUitems) {
                    for (EvalExpertEvaluationDetail evalExpertEvaluationDetail : evalExpertEvaluationDetails) {
                        if (Objects.equals(scoringMethodUitem.getEntMethodItemId(), evalExpertEvaluationDetail.getScoringMethodUitemId())){
                            map.put(scoringMethodUitem.getEntMethodItemId(),evalExpertEvaluationDetail.getEvaluationResult());
                        }
                        if (scoringMethodUitem.getEntMethodItemId()==1L){
                            map.put(scoringMethodUitem.getEntMethodItemId(),sum);
                        }
                    }
                }
                tableData.add(map);
            }
            resultMap.put("tableData",tableData);

            //供应商信息
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
            resultMap.put("busiBidderInfos",busiBidderInfos);

            return AjaxResult.success(resultMap);
        }
    */
    @Override
    public Map<String, Object> getEalExpertEvaluationDetailToGroupLeader(Long projectId, Long itemId) throws IOException {
        //评分细则
        ScoringMethodItem scoringMethodItem = scoringMethodItemService.getById(itemId);
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));


        Map<String, Object> resultMap = new HashMap<>();

        if (scoringMethodItem.getItemMode() == 1) {
            //查询该项目所有的专家信息
            BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
            busiExtractExpertResult.setProjectId(projectId);
            List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
            //--去掉汇总列
            if (!busiExtractExpertResults.stream()
                    .anyMatch(result -> result.getResultId().equals(1L))) {
                BusiExtractExpertResult busiExtractExpertResultSum = new BusiExtractExpertResult();
                busiExtractExpertResultSum.setResultId(1L);
                busiExtractExpertResultSum.setExpertName("汇总");
                //  busiExtractExpertResults.add(busiExtractExpertResultSum);
            }
            //添加专家评审内容
            EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
            //查询出评分数据
            List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                    new QueryWrapper<EvalExpertScoreInfo>()
                            .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                            .eq("scoring_method_item_id", itemId)
            );
            //将评分数据转成《专家，数据》
            // 将评分数据转成《专家结果ID, 评分信息对象》的映射
            Map<Long, EvalExpertScoreInfo> expertScoreInfoMap = expertScoreInfos.stream()
                    .collect(Collectors.toMap(
                            EvalExpertScoreInfo::getExpertResultId,  // 使用专家结果ID作为键
                            Function.identity(),                    // 使用对象本身作为值
                            (existing, replacement) -> existing     // 处理键冲突：保留先出现的元素
                    ));
            StringBuffer bjjgsb=new StringBuffer();
            //配置bjjgsb    info.getEvalContent() == null? "" : info.getEvalContent()
            for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                EvalExpertScoreInfo scoreInfo = expertScoreInfoMap.get(extractExpertResult.getResultId());
                String content = Optional.ofNullable(scoreInfo)
                        .map(EvalExpertScoreInfo::getEvalContent)
                        .orElse("");
                bjjgsb.append(extractExpertResult.getExpertName() + "：" + content + "\n");

//                if(StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalContent())) {
//                    bjjgsb.append(extractExpertResult.getExpertName() + "：" + null==expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalContent()?"":expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalContent() + "\n");
//                }
            }
            resultMap.put("bjjgsb", bjjgsb);
            resultMap.put("tableColumns", busiExtractExpertResults);
            //供应商信息
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                    .eq("decode_flag",1).eq("is_abandoned_bid",0));
            resultMap.put("busiBidderInfos", busiBidderInfos);

            //通过itemid查询出所有的评分因素集合
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("scoring_method_item_id", itemId)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );
            //获取因素id集合
            List<Long> uitemsIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            //查询该因素所有评分信息
            List<EvalExpertEvaluationDetail> scoringMethodUitemId = evalExpertEvaluationDetailService.list(
                    new QueryWrapper<EvalExpertEvaluationDetail>().in("scoring_method_uitem_id", uitemsIds)
            );
            //根据专家ID对评分信息进行分组《专家，数据集合》
            Map<Long, List<EvalExpertEvaluationDetail>> groupedByExpertResultId = scoringMethodUitemId.stream()
                    .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getExpertResultId));
            //如果groupedByExpertResultId的数量和专家数量不等于，则显示缺失专家的数量
            if (groupedByExpertResultId.keySet().size()!=busiExtractExpertResults.size()){
                List<BusiExtractExpertResult> unevaluatedExperts = findUnevaluatedExperts(busiExtractExpertResults, groupedByExpertResultId.keySet());
                resultMap.put("wpszj", unevaluatedExperts);
            }

            //获取每个供应商的评分因素评分数据
            Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = scoringMethodUitemId.stream()
                    .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));
            //循环供应商评分数据
            List<Map<Object, Object>> tableData = new ArrayList<>();
            for (Long l : groupedByEntId.keySet()) {
                Map<Object, Object> map = new HashMap<>();
                map.put("gys", l);
                //每个供应商所有因素的评价
                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = groupedByEntId.get(l);

                Map<Long, List<EvalExpertEvaluationDetail>> zhuanjia = evalExpertEvaluationDetails.stream()
                        .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getExpertResultId));
                double extractExpertResultSum = 0.0;
                Set<Long> keys = zhuanjia.keySet();
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if (expertScoreInfoMap.get(extractExpertResult.getResultId())!=null && expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalState()>0){
                        if (keys.contains(extractExpertResult.getResultId())) {
                            if (extractExpertResult.getResultId() == 1) {
                                continue;
                            } else {
                                //获取单个专家对该供应商的所有评分因素集合
                                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails1 = zhuanjia.get(extractExpertResult.getResultId());
                                if(evalExpertEvaluationDetails1.size()>0){
                                    double sum = evalExpertEvaluationDetails1.stream()
                                            .mapToDouble(detail -> parseEvaluationResult(detail.getEvaluationResult()))
                                            .sum();
                                    // System.out.println("Sum of evaluation results: " + sum);
                                    map.put(extractExpertResult.getResultId(), sum);
                                    extractExpertResultSum += sum;
                                }else {
                                    map.put(extractExpertResult.getResultId(), "/");

                                }
                            }
                        }
                    }
                }
                // map.put("1",extractExpertResultSum);
                tableData.add(map);
            }

            resultMap.put("tableData", tableData);
            //  ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));

            List<EvalProjectEvaluationInfo> projects = evalProjectEvaluationInfoService.list(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
            EvalProjectEvaluationProcess evaluationProcess = null;
            if (ObjectUtil.isNotEmpty(projects)) {
                EvalProjectEvaluationInfo evalProjectEvaluationInfo = projects.get(projects.size() - 1);
                //查询项目细则进度
                evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>().eq("scoring_method_item_id", itemId)
                        .eq("project_evaluation_id", evalProjectEvaluationInfo.getProjectEvaluationId()));
            }
            resultMap.put("evaluationProcess", evaluationProcess);

            return resultMap;

        }
        else if (scoringMethodItem.getItemMode() == 2) {
            //查询该项目所有的专家信息
            BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
            busiExtractExpertResult.setProjectId(projectId);
            List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
            //添加专家评审内容
            EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));

            List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                    new QueryWrapper<EvalExpertScoreInfo>()
                            .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                            .eq("scoring_method_item_id", itemId)
            );
            // 将评分数据转成《专家结果ID, 评分信息对象》的映射
            Map<Long, EvalExpertScoreInfo> expertScoreInfoMap = expertScoreInfos.stream()
                    .collect(Collectors.toMap(
                            EvalExpertScoreInfo::getExpertResultId,  // 使用专家结果ID作为键
                            Function.identity(),                    // 使用对象本身作为值
                            (existing, replacement) -> existing     // 处理键冲突：保留先出现的元素
                    ));
            // 筛选评审状态为1（已提交）的专家评分信息
            List<EvalExpertScoreInfo> submittedExpertScores = expertScoreInfos.stream()
                    .filter(info -> info.getEvalState() != null && info.getEvalState() > 1)
                    .collect(Collectors.toList());

            Map<Long, String> submittedExpertScoreInfoMap = submittedExpertScores.stream()
                    .collect(Collectors.toMap(
                            EvalExpertScoreInfo::getExpertResultId,
                            info -> info.getEvalContent() != null ? info.getEvalContent() : "",
                            (existing, replacement) -> existing // 处理键冲突的合并函数
                    ));
//            Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
//                    .collect(Collectors.toMap(EvalExpertScoreInfo::getExpertResultId, EvalExpertScoreInfo::getEvalContent));

//            Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
//                    .collect(Collectors.toMap(
//                            EvalExpertScoreInfo::getExpertResultId,
//                            info -> info.getEvalContent() != null ? info.getEvalContent() : "",
//                            (existing, replacement) -> existing // 处理键冲突的合并函数
//                    ));
            if (submittedExpertScoreInfoMap.keySet().size()!=busiExtractExpertResults.size()){
                List<BusiExtractExpertResult> unevaluatedExperts = findUnevaluatedExperts(busiExtractExpertResults, submittedExpertScoreInfoMap.keySet());
                resultMap.put("wpszj", unevaluatedExperts);
            }

            StringBuffer bjjgsb=new StringBuffer();
            for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                if(expertScoreInfoMap.get(extractExpertResult.getResultId())!=null && StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalContent())) {
                    bjjgsb.append(extractExpertResult.getExpertName() + "：" + expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalContent() + "\n");
                }
            }
            resultMap.put("bjjgsb", bjjgsb);
            //通过itemid查询出所有的评分因素集合
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("scoring_method_item_id", itemId)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );
            //获取因素id集合
            List<Long> uitemsIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            //查询该因素所有评分信息
            List<EvalExpertEvaluationDetail> scoringMethodUitemId = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>().in("scoring_method_uitem_id", uitemsIds));


            //获取每个供应商的评分因素评分数据
            Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = scoringMethodUitemId.stream()
                    .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));

            List<Map<Object, Object>> tableData = new ArrayList<>();
            resultMap.put("tableColumns", busiExtractExpertResults);
            //供应商信息
            List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));
            resultMap.put("busiBidderInfos", busiBidderInfos);

            for (Long l : groupedByEntId.keySet()) {
                Map<Object, Object> map = new HashMap<>();
                map.put("gys", l);
                //每个供应商所有因素的评价
                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = groupedByEntId.get(l);

                Map<Long, List<EvalExpertEvaluationDetail>> zhuanjia = evalExpertEvaluationDetails.stream()
                        .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getExpertResultId));
                //ItemMode  1是评分2是通过
                //循环专家信息
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if (expertScoreInfoMap.get(extractExpertResult.getResultId())!=null && expertScoreInfoMap.get(extractExpertResult.getResultId()).getEvalState()>0){
                        if (zhuanjia.containsKey(extractExpertResult.getResultId())) {
                            List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails1 = zhuanjia.get(extractExpertResult.getResultId());

                            boolean b = checkEvaluationResult(evalExpertEvaluationDetails1);
                            if (b) {
                                map.put(extractExpertResult.getResultId(), 1);
                            } else {
                                map.put(extractExpertResult.getResultId(), "0");
                            }
                        } else {
                            map.put(extractExpertResult.getResultId(), "/");
                        }
                    }

                }
                tableData.add(map);
            }
            resultMap.put("tableData", tableData);
            //  ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));
            //查询项目细则进度
            EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>().eq("scoring_method_item_id", itemId)
                    .eq("project_evaluation_id", scoringMethodUinfo.getScoringMethodId()));
            resultMap.put("evaluationProcess", evaluationProcess);
            return resultMap;
        }
        return null;
    }
    /**
     * 找出未评审的专家列表
     * @param allExperts 全部专家列表
     * @param evaluatedExpertIds 已评审专家ID集合
     * @return 未评审的专家列表
     */
    public static List<BusiExtractExpertResult> findUnevaluatedExperts(
            List<BusiExtractExpertResult> allExperts,
            Set<Long> evaluatedExpertIds) {
        return allExperts.stream()
                .filter(expert -> {
                    Long resultId = expert.getResultId();  // 假设有 getResultId() 方法
                    return resultId == null || !evaluatedExpertIds.contains(resultId);
                })
                .collect(Collectors.toList());
    }
    @Override
    public Boolean saveEvalExpertEvaluationDetails(List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails) {

        List<Long> expertResultIds = evalExpertEvaluationDetails.stream().map(EvalExpertEvaluationDetail::getExpertResultId).collect(Collectors.toList());
        List<Long> entIds = evalExpertEvaluationDetails.stream().map(EvalExpertEvaluationDetail::getEntId).collect(Collectors.toList());
        List<Long> scoringMethodUitemIds = evalExpertEvaluationDetails.stream().map(EvalExpertEvaluationDetail::getScoringMethodUitemId).collect(Collectors.toList());

        List<EvalExpertEvaluationDetail> list = evalExpertEvaluationDetailService.list(
                new QueryWrapper<EvalExpertEvaluationDetail>().in("ent_id", entIds)
                        .in("expert_result_id", expertResultIds)
                        .in("scoring_method_uitem_id", scoringMethodUitemIds)
        );
        if (!list.isEmpty()) {
            evalExpertEvaluationDetailService.remove(new QueryWrapper<EvalExpertEvaluationDetail>().in("ent_id", entIds)
                    .in("expert_result_id", expertResultIds)
                    .in("scoring_method_uitem_id", scoringMethodUitemIds));
        }
        return evalExpertEvaluationDetailService.saveBatch(evalExpertEvaluationDetails);
    }

    @Transactional
    @Override
    public AjaxResult getCheckExpertEvaluationDetail(CheckZhuanJiaPS checkZhuanJiaPS) throws IOException {
        /*//查找项目专家数量
        BusiExtractExpertResult busiExtractExpertResult=new BusiExtractExpertResult();
        busiExtractExpertResult.setProjectId(checkZhuanJiaPS.getProjectId());
        List<BusiExtractExpertResult> zhuanJiaByProjectId = busiExtractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
        //获取当前项目的评分规则
        //scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>().eq("",checkZhuanJiaPS.getScoringMethodItemId()));*/
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", checkZhuanJiaPS.getProjectId()));


        //查询当前项目的所有供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", checkZhuanJiaPS.getProjectId()));
        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
            //获取当前评审因素的uitem条数
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(
                    new QueryWrapper<ScoringMethodUitem>()
                            .eq("scoring_method_item_id", checkZhuanJiaPS.getScoringMethodItemId())
                            .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
            );
            List<Long> uitemIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());

            //查找评分信息
            List<EvalExpertEvaluationDetail> list = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>()
                    .eq("expert_result_id", checkZhuanJiaPS.getExpertResultId())
                    .eq("ent_id", busiBidderInfo.getBidderId()
                    ).in("scoring_method_uitem_id", uitemIds)
            );
            if (busiBidderInfo.getIsAbandonedBid()==0){
                if (scoringMethodUitems.size() > list.size()) {
                    return AjaxResult.error("当前用户未完全评审" + "[" + busiBidderInfo.getBidderName() + "]");
                }
            }
        }
        return AjaxResult.success();
       /* //获取当前评审因素的uitem条数
        List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>().eq("scoring_method_item_id", checkZhuanJiaPS.getScoringMethodItemId()));
        List<Long> uitemIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getScoringMethodItemId).collect(Collectors.toList());

        //查找评分信息
        List<EvalExpertEvaluationDetail> list = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>()
                .eq("expert_result_id", checkZhuanJiaPS.getExpertResultId())
                .eq("ent_id", checkZhuanJiaPS.getEntId()
                ).in("scoring_method_uitem_id", uitemIds)
        );
        if (scoringMethodUitems.size()>list.size()){
            return AjaxResult.error("当前用户未完全评审");
        }else {
            return AjaxResult.success("");

        }*/

    }

    @Override
    public AjaxResult reviewSummaryConfirmation(CheckZhuanJiaPS checkZhuanJiaPS) {
        //通过项目id找到绑定的评分办法规则
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", checkZhuanJiaPS.getProjectId()));
        //查询评分item
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId()));
        Map<Long, List<ScoringMethodUitem>> uitems = new HashMap<>();
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(
                    new QueryWrapper<ScoringMethodUitem>()
                            .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                            .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                            .eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));
//            if (scoringMethodItem.getItemCode().equals("tbbjdf")){
//                GetEvalAgainQuoteVo getEvalAgainQuoteVo=new GetEvalAgainQuoteVo();
//                getEvalAgainQuoteVo.setItemId(scoringMethodItem.getScoringMethodItemId());
//                getEvalAgainQuoteVo.setProjectId(checkZhuanJiaPS.getProjectId());
//                Map<String, Object> evalAgainQuote = evalAgainQuoteService.getEvalAgainQuote(getEvalAgainQuoteVo);
//                // 使用方法重组数据
//                Map<String, Map<Long, BigDecimal>> restructuredData = restructureData(evalAgainQuote);
//                 scoringMethodUitems.get(0).setMap(restructuredData);
//            }else {
            for (ScoringMethodUitem scoringMethodUitem : scoringMethodUitems) {
                //评审记录
                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = evalExpertEvaluationDetailService.list(
                        new QueryWrapper<EvalExpertEvaluationDetail>()
                                .eq("scoring_method_uitem_id", scoringMethodUitem.getEntMethodItemId())//当前评分因素id
                                .eq("expert_result_id", checkZhuanJiaPS.getExpertResultId())//专家id

                );
                scoringMethodUitem.setEvalExpertEvaluationDetails(evalExpertEvaluationDetails);
            }
            //将此行挪至下一个}后，增加投标报价打分显示
            uitems.put(scoringMethodItem.getScoringMethodItemId(), scoringMethodUitems);
        }
        // }
        //查询当前项目的所有供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", checkZhuanJiaPS.getProjectId())
                .eq("decode_flag",1).eq("is_abandoned_bid",0));
        Map<String, Object> map = new HashMap<>();
        map.put("busiBidderInfos", busiBidderInfos);
        map.put("uitems", uitems);
        map.put("scoringMethodItems", scoringMethodItems);

        return AjaxResult.success(map);
    }

    @Override
    public AjaxResult getDetailByPsxx(CheckZhuanJiaPS checkZhuanJiaPS) {
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", checkZhuanJiaPS.getProjectId()));
        //获取当前评审因素的uitem条数
        List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(
                new QueryWrapper<ScoringMethodUitem>()
                        .eq("scoring_method_item_id", checkZhuanJiaPS.getScoringMethodItemId())
                        .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
        );
        List<Long> uitemIds = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());


        //查询当前项目的所有供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", checkZhuanJiaPS.getProjectId()));
        if (uitemIds == null || uitemIds.isEmpty()) {
            return AjaxResult.success(busiBidderInfos);
        }
        List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>()
                .in("scoring_method_uitem_id", uitemIds)
                .eq("expert_result_id", checkZhuanJiaPS.getExpertResultId())
        );
        Map<Long, List<EvalExpertEvaluationDetail>> groupedByEntId = evalExpertEvaluationDetails.stream()
                .collect(Collectors.groupingBy(EvalExpertEvaluationDetail::getEntId));
        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
            busiBidderInfo.setEvalExpertEvaluationDetails(groupedByEntId.get(busiBidderInfo.getBidderId()));
        }

        return AjaxResult.success(busiBidderInfos);
    }

    private static double parseEvaluationResult(String evaluationResult) {
        try {
            return Double.parseDouble(evaluationResult);
        } catch (NumberFormatException e) {
            // 如果无法解析为Double，可以返回0或者抛出异常
            return 0.0;
        }
    }

    //通过制评分  判断是否通过
    private static boolean checkEvaluationResult(List<EvalExpertEvaluationDetail> details) {
        if (details == null) {
            return true; // 如果列表为空或不存在，我们可以假设没有 "0" 值
        }
        return details.stream()
                .noneMatch(detail -> "0".equals(detail.getEvaluationResult()));
    }

    public static Map<String, Map<Long, BigDecimal>> restructureData(Map<String, Object> originalMap) {
        // 提取列名列表
        List<String> col = (List<String>) originalMap.get("col");
        // 提取结果映射
        Map<Long, Map<String, Object>> resultMap = (Map<Long, Map<String, Object>>) originalMap.get("resultMap");

        // 创建新的结果映射字典
        Map<String, Map<Long, BigDecimal>> newResultMap = new HashMap<>();

        // 遍历列名，并根据列名重组数据
        for (String column : col) {
            Map<Long, BigDecimal> columnMap = new HashMap<>();
            for (Map.Entry<Long, Map<String, Object>> entry : resultMap.entrySet()) {
                Long bidderId = entry.getKey();
                Map<String, Object> values = entry.getValue();
                // 类型转换，确保值是Double类型
                // Double value = (Double) ;
                columnMap.put(bidderId, new BigDecimal(values.get(column).toString()));
            }
            newResultMap.put(column, columnMap);
        }

        return newResultMap;
    }

    /**
     * 最终评审汇总
     *备份
     * @param projectId
     * @return
     */

   /* public Map<String, Object> getReviewSummary1(Long projectId, Long resultId) {
        BusiTenderProject busiTenderProject = busiTenderProjectService.getById(projectId);

        //查询项目所绑定的评分方法
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));
        //获取全部评分细则(评分)--表头
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .in("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                .eq("item_mode", 1)
        );
        Map<String, Object> returnMap = new HashMap<>();
        //竖轴列头
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));

        EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));

        Map<Long,Map<Long, String>> itemMap=new HashMap<>();

        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
            EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                    .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                    .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
            );
            if (!"{}".equals(evaluationProcess.getEvaluationResult())){
                List<EvaluationResultVo> evaluationResultVoList = JSON.parseObject(evaluationProcess.getEvaluationResult(),new TypeReference<List<EvaluationResultVo>>() {});
                if (evaluationResultVoList!=null){
                    Map<Long, String> map = evaluationResultVoList.stream()
                            .collect(Collectors.toMap(
                                    EvaluationResultVo::getBidder,
                                    EvaluationResultVo::getResult
                            ));
                    itemMap.put(scoringMethodItem.getScoringMethodItemId(),map);
                }
            }
        }

        Map<Long, Map<Long, String>> resultMap = new HashMap<>();

        for (Map.Entry<Long, Map<Long, String>> outerEntry : itemMap.entrySet()) {
            Long outerKey = outerEntry.getKey();
            Map<Long, String> innerMap = outerEntry.getValue();
            for (Map.Entry<Long, String> innerEntry : innerMap.entrySet()) {
                Long innerKey = innerEntry.getKey();
                String value = innerEntry.getValue();
                resultMap.computeIfAbsent(innerKey, k -> new HashMap<>()).put(outerKey, value);
            }
        }

        for (Long l : resultMap.keySet()) {
            Map<Long, String> longStringMap = resultMap.get(l);
            double sum = 0;
            for (String value : longStringMap.values()) {
                sum += Double.parseDouble(value);
            }
            // 四舍五入到两位小数
            double roundedValue = Math.round(sum * 100.0) / 100.0;
            longStringMap.put(1l,roundedValue+"");
        }
        ScoringMethodItem scoringMethodItem = new ScoringMethodItem();
        scoringMethodItem.setScoringMethodItemId(1L);
        scoringMethodItem.setItemName("总分");
        scoringMethodItems.add(scoringMethodItem);
        returnMap.put("resultMap",resultMap);
        returnMap.put("busiBidderInfos", busiBidderInfos);
        returnMap.put("scoringMethodItems", scoringMethodItems);
        return returnMap;
    }*/


    @Transactional
    @Override
    public Map<String, Object> getReviewSummary(Long projectId, Long resultId){
        BusiTenderProject busiTenderProject = busiTenderProjectService.getById(projectId);
        Map<String, Object> returnMap = new HashMap<>();
        //查询项目所绑定的评分方法
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));
        //获取全部评分细则(评分)--表头
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .in("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                .eq("item_mode", 1)
        );
        //竖轴列头
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                .eq("decode_flag",1).eq("is_abandoned_bid",0));

        if (busiTenderProject.getTenderMode().equals("0")||busiTenderProject.getTenderMode().equals("3")||busiTenderProject.getTenderMode().equals("4")) {
            //供应商投标信息 (首次报价)
            List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                    .eq("project_id", projectId).eq("del_flag", 0));
            Map<Long, String> bidderAmountMap = biddingRecords.stream()
                    .collect(Collectors.toMap(
                            BusiBiddingRecord::getBidderId,
                            item->item.getBidAmount().toString()
                    ));
            Map<Long, Map<Long, String>> resultMap = new HashMap<>();

            for (Long l : bidderAmountMap.keySet()) {
                Map<Long, String> longStringMap =new HashMap<>();
                // 四舍五入到两位小数
                longStringMap.put(1l,bidderAmountMap.get(l)+"");
                resultMap.put(l,longStringMap);
            }

            ScoringMethodItem scoringMethodItem = new ScoringMethodItem();
            scoringMethodItem.setScoringMethodItemId(1L);
            scoringMethodItem.setItemName("价格");
            scoringMethodItems.clear();
            scoringMethodItems.add(scoringMethodItem);
            returnMap.put("resultMap",resultMap);
            returnMap.put("busiBidderInfos", busiBidderInfos);
            returnMap.put("scoringMethodItems", scoringMethodItems);
        }else if (busiTenderProject.getTenderMode().equals("1")){


            EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));

            Map<Long,Map<Long, String>> itemMap=new HashMap<>();

            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                EvalProjectEvaluationProcess evaluationProcess = evalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                        .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                        .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                );
                if (!"{}".equals(evaluationProcess.getEvaluationResult())){
                    List<EvaluationResultVo> evaluationResultVoList = JSON.parseObject(evaluationProcess.getEvaluationResult(),new TypeReference<List<EvaluationResultVo>>() {});
                    if (evaluationResultVoList!=null){
                        Map<Long, String> map = evaluationResultVoList.stream()
                                .collect(Collectors.toMap(
                                        EvaluationResultVo::getBidder,
                                        EvaluationResultVo::getResult
                                ));
                        itemMap.put(scoringMethodItem.getScoringMethodItemId(),map);
                    }
                }
            }

            Map<Long, Map<Long, String>> resultMap = new HashMap<>();

            for (Map.Entry<Long, Map<Long, String>> outerEntry : itemMap.entrySet()) {
                Long outerKey = outerEntry.getKey();
                Map<Long, String> innerMap = outerEntry.getValue();
                for (Map.Entry<Long, String> innerEntry : innerMap.entrySet()) {
                    Long innerKey = innerEntry.getKey();
                    String value = innerEntry.getValue();
                    resultMap.computeIfAbsent(innerKey, k -> new HashMap<>()).put(outerKey, value);
                }
            }

            for (Long l : resultMap.keySet()) {
                Map<Long, String> longStringMap = resultMap.get(l);
                double sum = 0;
                for (String value : longStringMap.values()) {
                    sum += Double.parseDouble(value);
                }
                // 四舍五入到两位小数
                double roundedValue = Math.round(sum * 100.0) / 100.0;
                longStringMap.put(1l,roundedValue+"");
            }
            ScoringMethodItem scoringMethodItem = new ScoringMethodItem();
            scoringMethodItem.setScoringMethodItemId(1L);
            scoringMethodItem.setItemName("总分");
            scoringMethodItems.add(scoringMethodItem);
            returnMap.put("resultMap",resultMap);
            returnMap.put("busiBidderInfos", busiBidderInfos);
            returnMap.put("scoringMethodItems", scoringMethodItems);
        }
        return returnMap;
    }

}
