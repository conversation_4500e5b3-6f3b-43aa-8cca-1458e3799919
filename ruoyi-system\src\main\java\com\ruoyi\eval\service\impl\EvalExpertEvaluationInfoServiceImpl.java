package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertEvaluationInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.mapper.EvalExpertEvaluationInfoMapper;
import com.ruoyi.eval.service.IEvalExpertEvaluationInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 专家打分详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalExpertEvaluationInfoServiceImpl extends ServiceImpl<EvalExpertEvaluationInfoMapper, EvalExpertEvaluationInfo> implements IEvalExpertEvaluationInfoService {

    @Autowired
    private IBusiExtractExpertResultService extractExpertResultService;
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    /**
     * 查询专家打分详情列表
     *
     * @param evalExpertEvaluationInfo 专家打分详情
     * @return 专家打分详情
     */
    @Override
    public List<EvalExpertEvaluationInfo> selectList(EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        QueryWrapper<EvalExpertEvaluationInfo> evalExpertEvaluationInfoQueryWrapper = new QueryWrapper<>();
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationInfo.getEvalExpertEvaluationInfoId()),
                "eval_expert_evaluation_info_id",evalExpertEvaluationInfo.getEvalExpertEvaluationInfoId());
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertEvaluationInfo.getExpertResultId()),
                "expert_result_id",evalExpertEvaluationInfo.getExpertResultId());
        return list(evalExpertEvaluationInfoQueryWrapper);
    }

    @Override
    public AjaxResult updateNode(EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        if(updateById(evalExpertEvaluationInfo)){
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult getInfo(EvalExpertEvaluationInfo evalExpertEvaluationInfo) {
        QueryWrapper<EvalExpertEvaluationInfo> evalExpertEvaluationInfoQueryWrapper = new QueryWrapper<>();
        evalExpertEvaluationInfoQueryWrapper.eq(
                ObjectUtil.isNotEmpty(evalExpertEvaluationInfo.getEvalExpertEvaluationInfoId()),
                "eval_expert_evaluation_info_id",evalExpertEvaluationInfo.getEvalExpertEvaluationInfoId());
        evalExpertEvaluationInfoQueryWrapper.eq(
                ObjectUtil.isNotEmpty(evalExpertEvaluationInfo.getExpertResultId()),
                "expert_result_id",evalExpertEvaluationInfo.getExpertResultId());
        evalExpertEvaluationInfoQueryWrapper.eq(
                ObjectUtil.isNotEmpty(evalExpertEvaluationInfo.getProjectEvaluationId()),
                "project_evaluation_id",evalExpertEvaluationInfo.getProjectEvaluationId());
        EvalExpertEvaluationInfo info = getOne(evalExpertEvaluationInfoQueryWrapper);
        if (info != null) {
            return AjaxResult.success(info);
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult getInfo2(EvalExpertEvaluationInfo evalExpertEvaluationInfo) {

        try {
            // 第一步：创建专家抽取结果对象，设置项目ID
            BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
            busiExtractExpertResult.setProjectId(evalExpertEvaluationInfo.getProjectId());
            
            // 第二步：根据项目ID查询该项目的所有专家抽取结果
            List<BusiExtractExpertResult> expertList = extractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
            
            // 第三步：提取所有专家的编号列表
            List<String> collect = expertList.stream().map(BusiExtractExpertResult::getExpertCode).collect(Collectors.toList());
            
            // 第四步：检查当前专家是否在项目专家列表中
            if (!collect.contains(evalExpertEvaluationInfo.getExpertCode())){
                // 如果不在专家列表中，说明是业主代表
                return AjaxResult.success("业主代表");
            }
            
            // 第五步：查找当前专家的具体抽取结果记录
            BusiExtractExpertResult result = null;
            if (expertList == null || expertList.isEmpty()) {
                return AjaxResult.error("专家为空");
            }
            
            // 遍历专家列表，找到匹配的专家记录
            for (BusiExtractExpertResult r : expertList) {
                if (r.getExpertCode().equals(evalExpertEvaluationInfo.getExpertCode())) {
                    result = r;
                    break;
                }
            }
            
            // 第六步：查询项目的评审信息
            QueryWrapper<EvalProjectEvaluationInfo> evalProjectEvaluationInfoQueryWrapper = new QueryWrapper<>();
            evalProjectEvaluationInfoQueryWrapper.eq("project_id", evalExpertEvaluationInfo.getProjectId());
            EvalProjectEvaluationInfo evalProjectEvaluationInfo = evalProjectEvaluationInfoService.getOne(evalProjectEvaluationInfoQueryWrapper);
            
            // 检查项目评审信息是否存在
            if (evalProjectEvaluationInfo == null) {
                return AjaxResult.error("评审项目为空");
            }
            
            // 第七步：查询专家的评审信息
            QueryWrapper<EvalExpertEvaluationInfo> evalExpertEvaluationInfoQueryWrapper = new QueryWrapper<>();
            evalExpertEvaluationInfoQueryWrapper.eq("project_evaluation_id", evalProjectEvaluationInfo.getProjectEvaluationId());  // 根据项目评审ID查询
            evalExpertEvaluationInfoQueryWrapper.eq("expert_result_id", result.getResultId());  // 根据专家结果ID查询
            EvalExpertEvaluationInfo info = getOne(evalExpertEvaluationInfoQueryWrapper);
            
            // 第八步：返回查询结果
            if (info != null) {
                // 找到专家的评审信息，返回成功
                return AjaxResult.success(info);
            }
        } catch (Exception e) {
            // 异常处理：打印异常堆栈信息
            e.printStackTrace();
        }
        
        // 默认返回错误结果（当没有找到专家评审信息或发生异常时）
        return AjaxResult.error();
    }

}
