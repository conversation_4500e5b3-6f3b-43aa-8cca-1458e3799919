package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalInquiringBidInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.mapper.EvalInquiringBidInfoMapper;
import com.ruoyi.eval.service.IEvalInquiringBidInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.eval.vo.EvalInquiringVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 询标信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalInquiringBidInfoServiceImpl extends ServiceImpl<EvalInquiringBidInfoMapper, EvalInquiringBidInfo> implements IEvalInquiringBidInfoService {
    @Autowired
    private IEvalInquiringBidInfoService evalInquiringBidInfoService;
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    /**
     * 查询询标信息列表
     *
     * @param evalInquiringBidInfo 询标信息
     * @return 询标信息
     */
    @Override
    public List<EvalInquiringBidInfo> selectList(EvalInquiringBidInfo evalInquiringBidInfo) {
        QueryWrapper<EvalInquiringBidInfo> evalInquiringBidInfoQueryWrapper = new QueryWrapper<>();
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getInquiringBidId()), "inquiring_bid_id", evalInquiringBidInfo.getInquiringBidId());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getProjectEvaluationId()), "project_evaluation_id", evalInquiringBidInfo.getProjectEvaluationId());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getEntId()), "ent_id", evalInquiringBidInfo.getEntId());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getExpertResultId()), "expert_result_id", evalInquiringBidInfo.getExpertResultId());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getInquiringContent()), "inquiring_content", evalInquiringBidInfo.getInquiringContent());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getInquiringTime()), "inquiring_time", evalInquiringBidInfo.getInquiringTime());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getReplyContent()), "reply_content", evalInquiringBidInfo.getReplyContent());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getReplyTime()), "reply_time", evalInquiringBidInfo.getReplyTime());
        evalInquiringBidInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalInquiringBidInfo.getReplyFile()), "reply_file", evalInquiringBidInfo.getReplyFile());
        String beginCreateTime = evalInquiringBidInfo.getParams().get("beginCreateTime") != null ? evalInquiringBidInfo.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = evalInquiringBidInfo.getParams().get("endCreateTime") + "" != null ? evalInquiringBidInfo.getParams().get("endCreateTime") + "" : "";
        evalInquiringBidInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        evalInquiringBidInfoQueryWrapper.apply(
                ObjectUtil.isNotEmpty(evalInquiringBidInfo.getParams().get("dataScope")),
                evalInquiringBidInfo.getParams().get("dataScope") + ""
        ).orderByAsc("inquiring_time"); // 添加这一行以进行升序排序

        return list(evalInquiringBidInfoQueryWrapper);
    }

    @Override
    public AjaxResult getHistoryMessage(EvalInquiringVo evalInquiringVo) {

        EvalProjectEvaluationInfo one = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>()
                .eq("project_id", evalInquiringVo.getProjectId()));

       /* List<EvalInquiringBidInfo> list = evalInquiringBidInfoService.list(new QueryWrapper<EvalInquiringBidInfo>()
                .eq("project_evaluation_id", one.getProjectEvaluationId())
                .eq("ent_id", evalInquiringVo.getEntId())
                .eq("expert_result_id", evalInquiringVo.getExpertResultId())
                .isNull("reply_content")
                .orderByAsc("create_time")
        );*/
        QueryWrapper<EvalInquiringBidInfo> queryWrapper=new QueryWrapper();
        queryWrapper.eq("project_evaluation_id", one.getProjectEvaluationId());
        queryWrapper.eq("ent_id", evalInquiringVo.getEntId());
        if (null!=evalInquiringVo.getExpertResultId()){
            queryWrapper.eq("expert_result_id", evalInquiringVo.getExpertResultId());
        }
        //queryWrapper.isNull("reply_content");
        queryWrapper.orderByAsc("create_time");
        List<EvalInquiringBidInfo> list = evalInquiringBidInfoService.list(queryWrapper);
        return AjaxResult.success(list);
    }
}
