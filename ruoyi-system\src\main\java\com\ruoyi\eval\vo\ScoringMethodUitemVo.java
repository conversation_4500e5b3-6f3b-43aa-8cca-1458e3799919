package com.ruoyi.eval.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;

public class ScoringMethodUitemVo {
    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entMethodItemId;
    /**
     * 评分办法细则名称
     */
    @ApiModelProperty("评分办法细则名称")
    @Excel(name = "评分办法细则名称")
    private Long scoringMethodItemId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 评审因素
     */
    @ApiModelProperty("评审因素")
    @Excel(name = "评审因素")
    private String itemName;
}
