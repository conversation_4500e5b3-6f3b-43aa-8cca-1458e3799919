package com.ruoyi.kaifangqian.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.kaifangqian.controller.vo.OpenSignVo;
import com.ruoyi.kaifangqian.controller.vo.request.*;
import com.ruoyi.kaifangqian.controller.vo.response.SealResponse;
import com.ruoyi.kaifangqian.controller.vo.response.VerifyResponse;
import com.ruoyi.kaifangqian.enums.SignTypeEnum;
import com.ruoyi.kaifangqian.sdkservice.enums.APIResultEnum;
import com.ruoyi.kaifangqian.sdkservice.enums.SDKSignTypeEnum;
import com.ruoyi.kaifangqian.sdkservice.service.CalculatePositionService;
import com.ruoyi.kaifangqian.sdkservice.service.EntSealGenerateService;
import com.ruoyi.kaifangqian.sdkservice.service.SDKService;
import com.ruoyi.kaifangqian.sdkservice.service.pojo.RealPositionProperty;
import com.ruoyi.kaifangqian.sdkservice.service.pojo.SourcePositionProperty;
import com.ruoyi.kaifangqian.sdkservice.utils.Base64;
import com.ruoyi.kaifangqian.sdkservice.vo.base.Result;
import com.ruoyi.kaifangqian.sdkservice.vo.base.SignLocation;
import com.ruoyi.kaifangqian.sdkservice.vo.request.CertEventRequest;
import com.ruoyi.kaifangqian.sdkservice.vo.request.DocumentSignRequest;
import com.ruoyi.kaifangqian.sdkservice.vo.response.CertEventResponse;
import com.ruoyi.kaifangqian.sdkservice.vo.response.DocumentSignResponse;
import com.ruoyi.kaifangqian.service.image.EntSealClipService;
import com.ruoyi.kaifangqian.service.verify.SignVerifyService;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.service.IDocResponseEntInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.utils.AttachmentUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * @Description: OpenSignController
 * @Package: org.resrun.controller
 * @ClassName: OpenSignController
 * @copyright 北京资源律动科技有限公司
 */
@Api(tags = "开放签-演示demo")
@RequestMapping("/kaifangqian")
@Log4j2
@RestController
public class OpenSignController {
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    IDocResponseEntInfoService iDocResponseEntInfoService;
    @Autowired
    IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private SignVerifyService signVerifyService ;
    @Autowired
    private CalculatePositionService calculatePositionService ;
    @Autowired
    private IBaseEntInfoService baseEntInfoService;
    @Autowired
    ISysConfigService iSysConfigService;
    @Autowired
    private SDKService sdkService;
    @Autowired
    private EntSealGenerateService entSealGenerateService ;
    @Autowired
    private EntSealClipService entSealClipService ;


    @ApiOperation("生成企业签章-上传生成")
    @RequestMapping(value = "/clip/seal",method = RequestMethod.POST)
    public AjaxResult generateUpload(@RequestBody ClipSealRequest request){

        if(request.getImage() == null || request.getImage().length() == 0){
            return AjaxResult.error("图片数据为空");
        }
        byte[] decode = Base64.decode(request.getImage());
        if(decode == null || decode.length == 0){
            return AjaxResult.error("签章制作失败");

        }

        byte[] entSealByte = entSealClipService.clip(decode, request.getColorRange(), request.getType());
        if(entSealByte == null){
            return AjaxResult.error("签章制作失败");
        }

        BaseEntInfo entInfo = baseEntInfoService.getById(request.getEntId());
        if ("legalPerson".equals(request.getType())) {
            entInfo.setLegalRepreSignature(entSealByte);
        }else{
            entInfo.setEnterpriseSignature(entSealByte);
        }
        entInfo.getParams().put("opUser", entInfo.getEntName());
        baseEntInfoService.saveOrUpdate(entInfo);


        String encode = Base64.encode(entSealByte);
        SealResponse response = new SealResponse();
        response.setEntSeal(encode);
        return AjaxResult.success(response) ;

    }

    @ApiOperation("生成企业签章-重置")
    @RequestMapping(value = "/clip/resetSeal",method = RequestMethod.POST)
    public AjaxResult resetSeal(@RequestBody ClipSealRequest request){

        BaseEntInfo entInfo = baseEntInfoService.getById(request.getEntId());
        byte[] bytes = new byte[0];
        if ("legalRepreSignature".equals(request.getType())) {
            entInfo.setLegalRepreSignature(bytes);
        }else{
            entInfo.setEnterpriseSignature(bytes);
        }
        entInfo.getParams().put("opUser", entInfo.getEntName());
        baseEntInfoService.saveOrUpdate(entInfo);
        return AjaxResult.success() ;

    }

    @ApiOperation("生成企业签章-参数生成")
    @RequestMapping(value = "/generate/seal",method = RequestMethod.POST)
    public AjaxResult seal(@RequestBody GenerateSealRequest request){


        if(request == null || request.getMiddleText() == null || request.getTopText() == null){
            return AjaxResult.error("参数缺失");

        }
        byte[] entSealByte = entSealGenerateService.generateEntSeal(request.getTopText(), request.getMiddleText());
        if(entSealByte == null){
            return AjaxResult.error("签章制作失败");
        }
        BaseEntInfo entInfo = baseEntInfoService.getById(request.getEntId());
        entInfo.setEnterpriseSignature(entSealByte);
        entInfo.getParams().put("opUser", entInfo.getEntName());
        baseEntInfoService.saveOrUpdate(entInfo);

        String encode = Base64.encode(entSealByte);
        SealResponse response = new SealResponse();
        response.setEntSeal(encode);
        return AjaxResult.success(response);
    }

    @ApiOperation("签署")
    @RequestMapping(value = "/sign",method = RequestMethod.POST)
    public AjaxResult sign(@RequestBody SignRequest request){
        String fileName = "";
        BusiAttachment attachment = null;
        DocResponseEntInfo one = null;
        BaseEntInfo entInfo = baseEntInfoService.getById(request.getEntId());
        log.debug("type:"+request.getType());
        if(request.getType()==0) {
            BusiTenderNotice tenderNoticeByProjectId = iBusiTenderNoticeService.getTenderNoticeByProjectId(request.getProjectId());
            Long noticeId = tenderNoticeByProjectId.getNoticeId();
            attachment = iBusiAttachmentService.getOne(new QueryWrapper<BusiAttachment>()
                    .eq("busi_id", noticeId).eq("file_type", 5).eq("file_suffix", "pdf")
            );
            if (null == attachment) {
                // 如果是空的，返回错误信息
                return AjaxResult.error("签署失败,没有找到磋商采购文件");
            }
            System.out.println("源路径：" + attachment.getFilePath());
            fileName = AttachmentUtil.urlToReal(attachment.getFilePath());
            System.out.println("转换后：" + fileName);
        }else{
            one = iDocResponseEntInfoService.getOne(new QueryWrapper<DocResponseEntInfo>()
                    .eq("ent_id", request.getEntId())
                    .eq("project_id", request.getProjectId())
            );
            if (null == one) {
                // 如果是空的，返回错误信息
                return AjaxResult.error("签署失败,没有找到供应商响应文件");
            }
            fileName = AttachmentUtil.urlToReal(one.getPdfPath());
        }
        //String fileName = "磋商采购文件.pdf" ;
        byte[] signFileBytes = null ;
        byte[] entSealBytes = null ;
//        byte[] personalBytes = null ;
        byte[] legalPersonalSealBytes = null ;
        Result<CertEventResponse> entCert = null ;
//       Result<CertEventResponse> personalCert = null ;
       Result<CertEventResponse> legalPersonalCert = null ;
        List<RealPositionProperty> entPositionList = null;
        List<RealPositionProperty> personalPositionList = null;
        List<RealPositionProperty> legalPersonalPositionList = null;
        int entSealWidth = 200 ;
        int entSealHeight = 200 ;
        int personalSealWidth = 150 ;
        int personalSealHeight = 70 ;
        int legalPersonalSealWidth = 100 ;
        int legalPersonalSealHeight = 100 ;
        //获取本地签署文件
        try {
            signFileBytes = getResourceFiles(fileName);
           // signFileBytes = getResourceFiles(fileName);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if(signFileBytes == null){
            return AjaxResult.error("签署失败，未获取到响应文件");
        }
        //生成企业证书和个人证书
        try {
            if(request.getEntName() != null && request.getEntName().length() > 0){
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("政府采购限额以下交易平台@" + request.getEntName());
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                entCert =  sdkService.certEvent(certEventRequest);
                if(entCert == null || !entCert.getCode().equals(APIResultEnum.SUCCESS.getCode())){
                    return AjaxResult.error(entCert.getMessage());
                }
            }
            String legalPersonalName = "";
            if(StringUtils.isNotBlank(entInfo.getEntLegalPerson())) {
                legalPersonalName = entInfo.getEntLegalPerson();
            }else {
                legalPersonalName = entInfo.getEntName();
            }
            if(StringUtils.isNotBlank(legalPersonalName)){
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("政府采购限额以下交易平台@" + legalPersonalName);
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                legalPersonalCert =  sdkService.certEvent(certEventRequest);
                if(legalPersonalCert == null || !legalPersonalCert.getCode().equals(APIResultEnum.SUCCESS.getCode())){
                    return AjaxResult.error(legalPersonalCert.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        //生成企业签章和个人签章
        if(request.getEntSeal() != null){
            entSealBytes = Base64.decode(request.getEntSeal());
        }
        if(request.getLegalPersonalSeal() != null){
            legalPersonalSealBytes = Base64.decode(request.getLegalPersonalSeal());
        }
//        if(request.getPersonalSeal() != null){
//            personalBytes = Base64.decode(request.getPersonalSeal());
//        }
        //进行签署操作
        byte[] operationByte = signFileBytes ;

        //计算企业签署位置和个人签署位置
        if(SignTypeEnum.POSITION.getCode().equals(request.getSignType())){
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.POSITION.getCode());
            log.debug("--------------------sign-------------------------");
            log.debug(request.getEntPositionList());
            log.debug(request.getEntPositionList().size());
            log.debug(request.getLegalPersonPositionList());
            log.debug(request.getLegalPersonPositionList().size());
            if((request.getEntPositionList() == null || request.getEntPositionList().size() == 0 ) &&
                    (request.getLegalPersonPositionList() == null || request.getLegalPersonPositionList().size() == 0)){
                return AjaxResult.error("签署失败，未获取到签章位置信息");
            }
            //计算企业签署位置
            if(request.getEntPositionList() != null && request.getEntPositionList().size() > 0){
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(entSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for(PositionRequest positionRequest : request.getEntPositionList()){
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
               Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                }else {
                    return AjaxResult.error("签署失败，单页签署企业签章失败");
                }

            }
            //计算个人签署位置
            if(request.getLegalPersonPositionList() != null && request.getLegalPersonPositionList().size() > 0){

                signRequest.setCertPassword(legalPersonalCert.getData().getCertPassword());
                signRequest.setPfx(legalPersonalCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for(PositionRequest positionRequest : request.getLegalPersonPositionList()){
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();

                }else {
                    return AjaxResult.error("签署失败，单页签署个人签章失败");
                }
            }

        }
        else if(SignTypeEnum.KEYWORDS.getCode().equals(request.getSignType())){
            if(StringUtils.isBlank(request.getEntKeyword()) && StringUtils.isBlank(request.getLegalKeyword())){
                return AjaxResult.error("签署失败");
            }
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.KEYWORDS.getCode());
            //根据关键字计算所有企业签署位置
            if(request.getEntKeyword() != null && request.getEntKeyword().length() > 0){
                entPositionList = calculatePositionService.getAllPositionByKeyWords(signFileBytes, request.getEntKeyword(), entSealWidth, entSealHeight, 1);
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(entSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));
                signRequest.setKeywords(request.getEntKeyword());
                signRequest.setKeywordType(1);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                }else {
                    return AjaxResult.error("企业关键字签署失败");
                }
            }
            //根据关键字计算所有个人签署位置
            if(request.getLegalKeyword() != null && request.getLegalKeyword().length() > 0){
                personalPositionList = calculatePositionService.getAllPositionByKeyWords(signFileBytes,request.getLegalKeyword(),legalPersonalSealWidth,legalPersonalSealHeight, 2);
                signRequest.setCertPassword(legalPersonalCert.getData().getCertPassword());
                signRequest.setPfx(legalPersonalCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));
                signRequest.setKeywords(request.getLegalKeyword());
                signRequest.setKeywordType(2);
                signRequest.setKeywordSealWidth(110);
                signRequest.setKeywordSealHeight(110);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                }else {
                    return AjaxResult.error("法定代表人关键字签署失败");
                }
            }

            if((personalPositionList == null || personalPositionList.size() == 0 ) &&
                    (entPositionList == null || entPositionList.size() == 0)){
                return AjaxResult.error("签署失败！签署关键字在文件中不存在，请准确设置关键字后再签署");
            }
        }
        else if(SignTypeEnum.ALL.getCode().equals(request.getSignType())){
            DocumentSignRequest signRequest = new DocumentSignRequest();
            signRequest.setUniqueCode(UUID.randomUUID().toString());
            signRequest.setSignType(SDKSignTypeEnum.POSITION.getCode());
            if((request.getEntPositionList() == null || request.getEntPositionList().size() == 0 ) &&
                    (request.getPersonalPositionList() == null || request.getPersonalPositionList().size() == 0)){
                return AjaxResult.error("签署失败");
            }
            //计算企业签署位置
            if(request.getEntPositionList() != null && request.getEntPositionList().size() > 0){
                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(entSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for(PositionRequest positionRequest : request.getEntPositionList()){
                    for(int i=1;i<=request.getPageSize();i++) {
                        SignLocation signLocation = new SignLocation();
                        signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                        signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                        signLocation.setPage(i);
                        signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                        signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                        signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                        signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                        signLocations.add(signLocation);
                    }
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();
                }else {
                    return AjaxResult.error("签署失败");
                }

            }
            //计算个人签署位置
            if(request.getLegalPersonPositionList() != null && request.getLegalPersonPositionList().size() > 0){

                signRequest.setCertPassword(entCert.getData().getCertPassword());
                signRequest.setPfx(entCert.getData().getPfx());
                signRequest.setSignatureFile(Base64.encode(legalPersonalSealBytes));
                signRequest.setDocumentFile(Base64.encode(operationByte));

                List<SignLocation> signLocations = new ArrayList<>();
                for(PositionRequest positionRequest : request.getLegalPersonPositionList()){
                    SignLocation signLocation = new SignLocation();
                    signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                    signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                    signLocation.setPage(positionRequest.getPage());
                    signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                    signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                    signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                    signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                    signLocations.add(signLocation);
                }
                signRequest.setSignLocationList(signLocations);
                Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
                if(signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())){
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                    operationByte = signResponse.getData().getDocumentFile();

                }else {
                    return AjaxResult.error("签署失败");
                }
            }

        }

//        String encode = Base64.encode(operationByte);
//        SignResponse response = new SignResponse();
//        response.setSignFile(encode);
       // return Result.OK(response);

        // 新的文件保存路径
        // 使用Paths类获取Path对象
        Path path = Paths.get(fileName);
        // 获取目录路径
        Path directoryPath = path.getParent();
        // 生成UUID作为文件名
        String uuidFileName = UUID.randomUUID().toString() + ".pdf";
        // 创建新的文件路径
        Path newFilePath = directoryPath.resolve(uuidFileName);
        System.out.println(newFilePath.toString());
        System.out.println(AttachmentUtil.realToUrl(newFilePath.toString()));
        System.out.println(AttachmentUtil.urlToReal(newFilePath.toString()));
        try (ByteArrayInputStream is = new ByteArrayInputStream(operationByte);
             PdfReader reader = new PdfReader(is);
             ByteArrayOutputStream os = new ByteArrayOutputStream();
             PdfWriter writer = new PdfWriter(os);
             PdfDocument pdfDocument = new PdfDocument(reader, writer);
             FileOutputStream fos = new FileOutputStream(newFilePath.toString())) {
             // FileOutputStream fos = new FileOutputStream("D:\\ruoyi\\uploadPath\\222.pdf")) {
            // 这里可以添加对PDF文档的操作，例如添加内容、修改属性等
            // 关闭文档
            pdfDocument.close();
            // 将 ByteArrayOutputStream 中的内容写入文件
            os.writeTo(fos);

        } catch (IOException e) {
            e.printStackTrace();
        }
        if(request.getType()==0) {
            attachment.setFilePath(AttachmentUtil.realToUrl(newFilePath.toString()).toString().replace('\\', '/'));
            attachment.getParams().put("opUser", entInfo.getEntName());
            iBusiAttachmentService.saveOrUpdate(attachment);
            return AjaxResult.success(attachment);
        } else if (request.getType() == 1) {
            one.setPdfPath(AttachmentUtil.realToUrl(newFilePath.toString()).toString().replace('\\', '/'));
            one.getParams().put("opUser", entInfo.getEntName());
            iDocResponseEntInfoService.updateById(one);
            return AjaxResult.success(one);
        }
        return AjaxResult.error();
    }

    @ApiOperation("文件验签")
    @RequestMapping(value = "/verify",method = RequestMethod.POST)
    public AjaxResult verify(@RequestBody VerifyRequest request){

        String verifyFile = request.getVerifyFile();
        byte[] decode = Base64.decode(verifyFile);
        VerifyResponse verify = signVerifyService.verify(decode, request.getFileName());
       return AjaxResult.success(verify);
    }
    @ApiOperation("文件预览")
    @RequestMapping(value = "/getiBusiAttachment",method = RequestMethod.POST)
    public AjaxResult getiBusiAttachment(@RequestBody OpenSignVo signVo) {
        BaseEntInfo entInfo = baseEntInfoService.getById(signVo.getEntId());
        Map<String,Object> map=new HashMap<>();
        map.put("entInfo",entInfo);

        //采购
        if (signVo.getType()==0){
            BusiTenderNotice tenderNoticeByProjectId = iBusiTenderNoticeService.getTenderNoticeByProjectId(signVo.getProjectId());
            Long noticeId =tenderNoticeByProjectId.getNoticeId();
            BusiAttachment attachment = iBusiAttachmentService.getOne(new QueryWrapper<BusiAttachment>()
                    .eq("busi_id", noticeId).eq("file_type", 5).eq("file_suffix", "pdf")
            );
            if (null == attachment) {
                // 如果是空的，返回错误信息
                return AjaxResult.error("签署失败,没有找到磋商采购文件");
            }
//            String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
            String fileUrl = "";
            map.put("filePath",fileUrl+attachment.getFilePath());
            map.put("baseFilePath",fileUrl+attachment.getRemark());
            map.put("attachmentID",attachment.getAttachmentId());
        }
        //响应
        if (signVo.getType()==1){
            DocResponseEntInfo one = iDocResponseEntInfoService.getOne(new QueryWrapper<DocResponseEntInfo>()
                    .eq("ent_id", signVo.getEntId())
                    .eq("project_id", signVo.getProjectId())
            );
            if (null == one) {
                // 如果是空的，返回错误信息
                return AjaxResult.error("签署失败,没有找到供应商响应文件");
            }
//            String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
            String fileUrl = "";
            map.put("filePath",fileUrl+one.getPdfPath());
            map.put("baseFilePath",fileUrl+one.getBasePdfPath());
            map.put("attachmentID",one.getId());

            //    return AjaxResult.success("查询成功",fileUrl+one.getFilePath());
        }
        return AjaxResult.success(map);
    }
    @GetMapping(value = "/resetBiddingDocuments/{attachmentId}/{type}")
    public AjaxResult resetBiddingDocuments(@PathVariable("attachmentId")Long attachmentId,@PathVariable("type")Integer type){
        if (type==0){
             BusiAttachment byId = iBusiAttachmentService.getById(attachmentId);
             byId.setFilePath(byId.getRemark());
            byId.getParams().put("opUser", byId.getUpdateBy());
            return AjaxResult.success(iBusiAttachmentService.saveOrUpdate(byId));
        }
        if (type==1){
            DocResponseEntInfo byId1 = iDocResponseEntInfoService.getById(attachmentId);
            byId1.setPdfPath(byId1.getBasePdfPath());
            byId1.getParams().put("opUser", byId1.getUpdateBy());
            return AjaxResult.success(iDocResponseEntInfoService.saveOrUpdate(byId1));
        }
        return AjaxResult.success();
    }
    public List<SourcePositionProperty> convert(List<PositionRequest> positionRequestList){
        List<SourcePositionProperty> list = new ArrayList<>();
        for(PositionRequest request : positionRequestList){
            SourcePositionProperty position = new SourcePositionProperty();
            position.setOffsetX(Float.valueOf(request.getOffsetX()));
            position.setOffsetY(Float.valueOf(request.getOffsetY()));
            position.setPage(request.getPage());
            position.setWidth(Float.valueOf(request.getWidth()));
            position.setHeight(Float.valueOf(request.getHeight()));
            position.setPageHeight(Float.valueOf(request.getPageHeight()));
            position.setPageWidth(Float.valueOf(request.getPageWidth()));
            list.add(position);
        }
        return list ;
    }


    public byte [] getResourceFiles(String path) {
        try {
          //  InputStream inputStream = ResourceUtils.class.getClassLoader()
                 //   .getResourceAsStream(path);
            //String realPath = AttachmentUtil.urlToReal(path);
            InputStream inputStream =Files.newInputStream(Paths.get(path));
            return read(inputStream);
        }catch (Exception e){
            System.err.println(path);
            e.printStackTrace();
        }
        return null;
    }


    public byte[] read(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }



}
