package com.ruoyi.kaifangqian.enums;

/**
 * @Description: 签署类型 1:关键字签署；2:指定位置签署
 * @Package: org.resrun.enums
 * @ClassName: SignTypeEnum
 * @author: Feng<PERSON>ai_Gong
 */
public enum SignTypeEnum {


    POSITION(1,"指定位置签署"),
    KEYWORDS(2,"关键字签署"),
    ALL(3,"全部签署"),



    ;

    private Integer code  ;

    private String name ;

    SignTypeEnum(Integer code, String name){
        this.code = code ;
        this.name = name;
    }


    public Integer getCode(){
        return this.code;
    }

    public String getName(){
        return this.name ;
    }


}
