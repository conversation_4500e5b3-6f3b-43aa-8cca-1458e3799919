package com.ruoyi.kaifangqian.sdkservice.exception;

import com.ruoyi.kaifangqian.sdkservice.enums.APIResultEnum;
import com.ruoyi.kaifangqian.sdkservice.enums.ParamFormatErrorEnum;

/**
 * @Description: PaasException
 * @Package: org.resrun.exception
 * @ClassName: PaasException
 * @author: Feng<PERSON>ai_Gong
 */
public class ParamFormatException extends RuntimeException{

    protected final transient APIResultEnum resultEnum ;

    protected final transient ParamFormatErrorEnum paramFormatErrorEnum ;


    public ParamFormatException(APIResultEnum resultEnum, ParamFormatErrorEnum paramFormatErrorEnum){
        this.resultEnum = resultEnum ;
        this.paramFormatErrorEnum = paramFormatErrorEnum;
    }





}
