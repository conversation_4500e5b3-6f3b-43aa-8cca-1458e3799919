package com.ruoyi.kaifangqian.service.image;

import com.ruoyi.kaifangqian.service.pojo.ConvertImage;
import org.apache.pdfbox.Loader;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: pdf文件转图片业务
 * @Package: org.resrun.service.image
 * @ClassName: Pdf2ImageService
 * @copyright 北京资源律动科技有限公司
 */
@Service
public class PdfConvertImageService {

    private Float resolution = 90f ;
    /**图片格式为jpg类型**/
    public static final String IMG_JPE = "jpg";
    /**图片格式为png类型**/
    public static final String IMG_PNG = "png";

    public List<ConvertImage> convertImage(byte[] signFileByte,String filePathWithName){
        Date d = new Date();
//        if(signFileByte == null){
//            return null ;
//        }
        List<ConvertImage> responseList = new ArrayList<>();
        PDDocument doc = null;
        try {
            //加载pdf文件
//            doc = Loader.loadPDF(signFileByte);
            String originalFileName = null;
            String imgPath = null;
            String fileAddress = null;
            File file = new File(filePathWithName);
            originalFileName = file.getName().replace(".pdf", "");
            fileAddress = file.getParent();

            doc = Loader.loadPDF(file);
            PDFRenderer renderer = new PDFRenderer(doc);
            int pageCount = doc.getNumberOfPages();
            if(pageCount == 0){
                return responseList;
            }

            for (int i = 0; i < pageCount; ++i) {
                int page = i ;
                BufferedImage image = renderer.renderImageWithDPI(page, resolution);
                ByteArrayOutputStream stream =  new ByteArrayOutputStream();
                ImageIO.write(image, IMG_PNG, stream);
                byte[] bytes = stream.toByteArray();
                if(bytes != null){
                    //String filePath = "d:/111/"+page+"."+IMG_PNG; // 文件路径
                    String fileName = originalFileName + "_" + (i + 1) + "." + IMG_PNG;
                    imgPath = fileAddress + "\\" + fileName;
                    try (FileOutputStream fos = new FileOutputStream(imgPath)) {
                        stream.writeTo(fos); // 将数据写入文件
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    ConvertImage response = new ConvertImage();
                    response.setImageByte(bytes);
                    response.setFilePath(imgPath);
                    response.setPage(page);
                    responseList.add(response);
//                    System.out.println(filePath);

                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("page:"+responseList.size()+"   time:"+(new Date().getTime()-d.getTime())+"  ms");
        return responseList ;
    }

    public static void main(String[] args) {
        PdfConvertImageService s = new PdfConvertImageService();
        System.out.println(s.convertImage(null,""));
    }

}
