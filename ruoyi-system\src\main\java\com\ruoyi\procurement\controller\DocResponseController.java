package com.ruoyi.procurement.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.procurement.domain.DocResponseInfo;
import com.ruoyi.procurement.domain.DocResponseItem;
import com.ruoyi.procurement.service.IDocResponseInfoService;
import com.ruoyi.procurement.service.IDocResponseItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 响应文件基础信息管理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "响应文件基础信息管理")
@RestController
@RequestMapping("/docResponse")
public class DocResponseController extends BaseController {
    @Autowired
    private IDocResponseInfoService docResponseInfoService;
    @Autowired
    private IDocResponseItemService docResponseItemService;

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询响应文件信息列表")
    @GetMapping("/list")
    public AjaxResult list(DocResponseInfo info) {
        try {
            List<DocResponseInfo> list = docResponseInfoService.selectList(info);
            return AjaxResult.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询响应文件信息")
    @GetMapping("/info")
    public AjaxResult info(Long infoId) {
        try {
            DocResponseInfo info = docResponseInfoService.getById(infoId);
            return AjaxResult.success(info);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询响应文件信息")
    @GetMapping("/getItemTree")
    public AjaxResult getItemList(Long infoId) {
        try {
            List<DocResponseItem> itemTree = docResponseItemService.selectTree(infoId);
            return AjaxResult.success(itemTree);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询响应文件信息")
    @GetMapping("/getItemMap")
    public AjaxResult getItemMap(Long infoId) {
        try {
            Map<String, Long> itemMap = docResponseItemService.getItemMap(infoId);
            return AjaxResult.success(itemMap);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

}
