package com.ruoyi.procurement.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.service.IDocResponseEntDetailService;
import com.ruoyi.procurement.service.IDocResponseEntInfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.procurement.vo.BidOpenIngVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户编响应文件信息
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "用户编响应文件信息")
@RestController
@RequestMapping("/docResponseEnt")
public class DocResponseEntController extends BaseController {
    @Autowired
    private IDocResponseEntInfoService docResponseEntInfoService;
    @Autowired
    private IDocResponseEntDetailService docResponseEntDetailService;
    @Autowired
    private IProcurementDocumentsUinfoService procurementDocumentsUinfoService;

    /**
     * 用户响应文件列表，已下载过采购文件的项目为基础，展示所有待制作和已制作的响应文件
     */
    @ApiOperation(value = "用户响应文件列表")
    @GetMapping("/entList")
    public TableDataInfo entList(DocResponseEntInfo info) {
        startPage();
        info.setEntId(getEntId());
        List<DocResponseEntInfo> list = docResponseEntInfoService.entList(info);
        return getDataTable(list);
    }

    @ApiOperation(value = "用户响应文件项目信息")
    @GetMapping("/entInfo")
    public AjaxResult entInfo(Long projectId) {
        try {
            DocResponseEntInfo info = docResponseEntInfoService.entInfo(projectId);
            return AjaxResult.success(info);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "用户编响应文件信息列表")
    @GetMapping("/list")
    public AjaxResult list(DocResponseEntInfo info) {
        try {
            List<DocResponseEntInfo> list = docResponseEntInfoService.selectList(info);
            return AjaxResult.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "用户编响应文件信息")
    @GetMapping("/getById")
    public AjaxResult getById(Long infoId) {
        try {
            DocResponseEntInfo info = docResponseEntInfoService.selectById(infoId);
            return AjaxResult.success(info);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "用户编响应文件信息列表")
    @PostMapping("/saveInfo")
    public AjaxResult saveInfo(DocResponseEntInfo info) {
        try {
            info.setEntId(getEntId());
            return AjaxResult.success(docResponseEntInfoService.saveInfo(info));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询用户编制采购文件信息保存列表")
    @GetMapping("/getDetailList")
    public AjaxResult getDetailList(DocResponseEntDetail detail) {
        try {
            List<DocResponseEntDetail> detailList = docResponseEntDetailService.selectList(detail);
            return AjaxResult.success(detailList);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询用户编制采购文件信息保存列表")
    @PostMapping("/saveDetail")
    public AjaxResult saveDetail(@RequestBody DocResponseEntDetail detail) {
        try {
            DocResponseEntDetail d = docResponseEntDetailService.saveDetail(detail);
            return AjaxResult.success(d);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询用户编制采购文件信息保存列表
     */
    @ApiOperation(value = "查询用户编制采购文件信息保存列表")
    @GetMapping("/delDetail")
    public AjaxResult delDetail(Long detailId) {
        try {
            docResponseEntDetailService.removeById(detailId);
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 删除中小企业声明函货物内容
     */
    @ApiOperation(value = "删除中小企业声明函货物内容")
    @GetMapping("/delZxqysmhDetail")
    public AjaxResult delZxqysmhDetail(Long detailId, String detailQyId) {
        try {
            String detail = docResponseEntDetailService.removeZxqysmhDetail(detailId,detailQyId);
            return AjaxResult.success("删除成功",detail);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 生成响应文件
     */
    @ApiOperation(value = "生成响应文件")
//    @PostMapping("/createDocResponse")
    public AjaxResult createDocResponse(@RequestBody JSONObject data) {
        try {
            JSONObject d = new JSONObject();
            return docResponseEntInfoService.createDocResponse(data);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 生成货物类响应文件
     */
    @ApiOperation(value = "生成货物类响应文件")
    @PostMapping("/createGoodsDocResponse")
    public AjaxResult createGoodsDocResponse(@RequestBody JSONObject data) {
        try {
            return docResponseEntInfoService.createGoodsDocResponse(data);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 生成服务类响应文件
     */
    @ApiOperation(value = "生成服务类响应文件")
    @PostMapping("/createServiceDocResponse")
    public AjaxResult createServiceDocResponse(@RequestBody JSONObject data) {
        try {
            return docResponseEntInfoService.createServiceDocResponse(data);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 生成询价货物类响应文件
     */
    @ApiOperation(value = "生成询价货物类响应文件")
    @PostMapping("/createInquiryGoodsDocResponse")
    public AjaxResult createInquiryGoodsDocResponse(@RequestBody JSONObject data) {
        try {
            return docResponseEntInfoService.createInquiryGoodsDocResponse(data);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 生成加密响应文件
     */
    @ApiOperation(value = "生成加密响应文件")
    @GetMapping("/createSecurityDocResponse")
    public AjaxResult createSecurityDocResponse(Long infoId) {
        try {
            return docResponseEntInfoService.createSecurityDocResponse(infoId);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 根据项目返回所有供应商的响应文件页码
     */
    @ApiOperation(value = "根据项目返回所有供应商的响应文件页码")
    @GetMapping("/getPesponseDocPageByProject")
    public AjaxResult getPesponseDocPageByProject(Long projectId) {
        try {
            return AjaxResult.success(docResponseEntInfoService.getPesponseDocPageByProject(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 查询采购公告信息
     */
    @ApiOperation(value = "查询采购公告信息")
    @GetMapping("/getProcurementInfo")
    public AjaxResult getProcurementInfo(Long infoId) {
        try {
            List<BidOpenIngVo> info = docResponseEntInfoService.getProcurementInfo(infoId);
            return AjaxResult.success(info);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /********************************************************/

    /**
     * 生成响应文件
     */
    @ApiOperation(value = "生成响应文件")
    @PostMapping("/createDocResponse")
    public AjaxResult createDocResponse1(@RequestBody JSONObject data) {
        try {
            return docResponseEntInfoService.createDocResponse1(data);
        } catch (ServiceException e) {
            logger.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }

    /**
     * 响应文件评审因素校验
     */
    @ApiOperation(value = "响应文件评审因素校验")
    @PostMapping("/resDocReviewFactorsDecision")
    public AjaxResult resDocReviewFactorsDecision(@RequestBody JSONObject data) {
        try {
            return docResponseEntInfoService.resDocReviewFactorsDecision(data);
        } catch (ServiceException e) {
            logger.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("系统异常请联系管理员");
        }
    }


}
