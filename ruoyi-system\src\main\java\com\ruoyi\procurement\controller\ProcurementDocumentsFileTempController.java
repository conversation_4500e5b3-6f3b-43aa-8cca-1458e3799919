package com.ruoyi.procurement.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.procurement.domain.ProcurementDocumentsFileTemp;
import com.ruoyi.procurement.service.IProcurementDocumentsFileTempService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 采购/响应文件模板Controller
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Api(tags = "采购/响应文件模板管理")
@RestController
@RequestMapping("/file/temp")
public class ProcurementDocumentsFileTempController extends BaseController {
    @Autowired
    private IProcurementDocumentsFileTempService procurementDocumentsFileTempService;

/**
 * 查询采购/响应文件模板列表
 */
@PreAuthorize("@ss.hasPermi('file:temp:list')")
@ApiOperation(value = "查询采购/响应文件模板列表")
@GetMapping("/list")
    public TableDataInfo list(ProcurementDocumentsFileTemp procurementDocumentsFileTemp) {
        startPage();
        List<ProcurementDocumentsFileTemp> list = procurementDocumentsFileTempService.selectList(procurementDocumentsFileTemp);
        return getDataTable(list);
    }

    /**
     * 导出采购/响应文件模板列表
     */
    @PreAuthorize("@ss.hasPermi('file:temp:export')")
    @Log(title = "采购/响应文件模板", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购/响应文件模板列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementDocumentsFileTemp procurementDocumentsFileTemp) {
        List<ProcurementDocumentsFileTemp> list = procurementDocumentsFileTempService.selectList(procurementDocumentsFileTemp);
        ExcelUtil<ProcurementDocumentsFileTemp> util = new ExcelUtil<ProcurementDocumentsFileTemp>(ProcurementDocumentsFileTemp. class);
        util.exportExcel(response, list, "采购/响应文件模板数据");
    }

    /**
     * 获取采购/响应文件模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('file:temp:query')")
    @ApiOperation(value = "获取采购/响应文件模板详细信息")
    @ApiImplicitParam(name = "documentsFileTempId", value = "文件模板id", required = true, dataType = "Long")
    @GetMapping(value = "/{documentsFileTempId}")
    public AjaxResult getInfo(@PathVariable("documentsFileTempId")Long documentsFileTempId) {
        return success(procurementDocumentsFileTempService.getById(documentsFileTempId));
    }

    /**
     * 新增采购/响应文件模板
     */
    @PreAuthorize("@ss.hasPermi('file:temp:add')")
    @Log(title = "采购/响应文件模板", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购/响应文件模板")
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementDocumentsFileTemp procurementDocumentsFileTemp) {
        return toAjax(procurementDocumentsFileTempService.save(procurementDocumentsFileTemp));
    }

    /**
     * 修改采购/响应文件模板
     */
    @PreAuthorize("@ss.hasPermi('file:temp:edit')")
    @Log(title = "采购/响应文件模板", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购/响应文件模板")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementDocumentsFileTemp procurementDocumentsFileTemp) {
        return toAjax(procurementDocumentsFileTempService.updateById(procurementDocumentsFileTemp));
    }

    /**
     * 删除采购/响应文件模板
     */
    @PreAuthorize("@ss.hasPermi('file:temp:remove')")
    @Log(title = "采购/响应文件模板", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购/响应文件模板")
    @DeleteMapping("/{documentsFileTempIds}")
    public AjaxResult remove(@PathVariable Long[] documentsFileTempIds) {
        return toAjax(procurementDocumentsFileTempService.removeByIds(Arrays.asList(documentsFileTempIds)));
    }
}
