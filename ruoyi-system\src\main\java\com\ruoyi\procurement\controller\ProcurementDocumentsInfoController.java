package com.ruoyi.procurement.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.service.IProcurementDocumentsInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 采购文件编制基础信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "采购文件编制基础信息管理")
@RestController
@RequestMapping("/documents/info")
public class ProcurementDocumentsInfoController extends BaseController {
    @Autowired
    private IProcurementDocumentsInfoService procurementDocumentsInfoService;

    /**
     * 查询采购文件编制基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:info:list')")
    @ApiOperation(value = "查询采购文件编制基础信息列表")
    @GetMapping("/list")
    public TableDataInfo list(ProcurementDocumentsInfo procurementDocumentsInfo) {
        startPage();
        List<ProcurementDocumentsInfo> list = procurementDocumentsInfoService.selectList(procurementDocumentsInfo);
        return getDataTable(list);
    }

    /**
     * 查询采购文件编制基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:info:list')")
    @ApiOperation(value = "查询采购文件编制基础信息列表")
    @GetMapping("/infoByParams")
    public AjaxResult infoByParams(ProcurementDocumentsInfo procurementDocumentsInfo) {
        ProcurementDocumentsInfo info = procurementDocumentsInfoService.infoByParams(procurementDocumentsInfo);
        return success(info);
    }

    /**
     * 导出采购文件编制基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:info:export')")
    @Log(title = "采购文件编制基础信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购文件编制基础信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementDocumentsInfo procurementDocumentsInfo) {
        List<ProcurementDocumentsInfo> list = procurementDocumentsInfoService.selectList(procurementDocumentsInfo);
        ExcelUtil<ProcurementDocumentsInfo> util = new ExcelUtil<ProcurementDocumentsInfo>(ProcurementDocumentsInfo.class);
        util.exportExcel(response, list, "采购文件编制基础信息数据");
    }


    /**
     * 获取采购文件编制基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:info:query')")
    @ApiOperation(value = "获取采购文件编制基础信息详细信息")
    @ApiImplicitParam(name = "projectFileId", value = "采购文件id", required = true, dataType = "Long")
    @GetMapping(value = "/{projectFileId}")
    public AjaxResult getInfo(@PathVariable("projectFileId") Long projectFileId) {
        return success(procurementDocumentsInfoService.getById(projectFileId));
    }

    /**
     * 新增采购文件编制基础信息
     */
    @PreAuthorize("@ss.hasPermi('documents:info:add')")
    @Log(title = "采购文件编制基础信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购文件编制基础信息")
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementDocumentsInfo procurementDocumentsInfo) {
        return toAjax(procurementDocumentsInfoService.save(procurementDocumentsInfo));
    }

    /**
     * 修改采购文件编制基础信息
     */
    @PreAuthorize("@ss.hasPermi('documents:info:edit')")
    @Log(title = "采购文件编制基础信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购文件编制基础信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementDocumentsInfo procurementDocumentsInfo) {
        return toAjax(procurementDocumentsInfoService.updateById(procurementDocumentsInfo));
    }

    /**
     * 删除采购文件编制基础信息
     */
    @PreAuthorize("@ss.hasPermi('documents:info:remove')")
    @Log(title = "采购文件编制基础信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购文件编制基础信息")
    @DeleteMapping("/{projectFileIds}")
    public AjaxResult remove(@PathVariable Long[] projectFileIds) {
        return toAjax(procurementDocumentsInfoService.removeByIds(Arrays.asList(projectFileIds)));
    }
}
