package com.ruoyi.procurement.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import com.ruoyi.procurement.service.IProcurementDocumentsItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 采购文件编制详细信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "采购文件编制详细信息管理")
@RestController
@RequestMapping("/documents/item")
public class ProcurementDocumentsItemController extends BaseController {
    @Autowired
    private IProcurementDocumentsItemService procurementDocumentsItemService;

/**
 * 查询采购文件编制详细信息列表
 */
@PreAuthorize("@ss.hasPermi('documents:item:list')")
@ApiOperation(value = "查询采购文件编制详细信息列表")
@GetMapping("/list")
    public TableDataInfo list(ProcurementDocumentsItem procurementDocumentsItem) {
        startPage();
        List<ProcurementDocumentsItem> list = procurementDocumentsItemService.selectList(procurementDocumentsItem);
        return getDataTable(list);
    }

    /**
     * 导出采购文件编制详细信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:item:export')")
    @Log(title = "采购文件编制详细信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购文件编制详细信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementDocumentsItem procurementDocumentsItem) {
        List<ProcurementDocumentsItem> list = procurementDocumentsItemService.selectList(procurementDocumentsItem);
        ExcelUtil<ProcurementDocumentsItem> util = new ExcelUtil<ProcurementDocumentsItem>(ProcurementDocumentsItem. class);
        util.exportExcel(response, list, "采购文件编制详细信息数据");
    }

    /**
     * 获取采购文件编制详细信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:item:query')")
    @ApiOperation(value = "获取采购文件编制详细信息详细信息")
    @ApiImplicitParam(name = "projectFileItemId", value = "采购文件详情id", required = true, dataType = "Long")
    @GetMapping(value = "/{projectFileItemId}")
    public AjaxResult getInfo(@PathVariable("projectFileItemId")Long projectFileItemId) {
        return success(procurementDocumentsItemService.getById(projectFileItemId));
    }

    /**
     * 新增采购文件编制详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:item:add')")
    @Log(title = "采购文件编制详细信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购文件编制详细信息")
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementDocumentsItem procurementDocumentsItem) {
        return toAjax(procurementDocumentsItemService.save(procurementDocumentsItem));
    }

    /**
     * 修改采购文件编制详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:item:edit')")
    @Log(title = "采购文件编制详细信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购文件编制详细信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementDocumentsItem procurementDocumentsItem) {
        return toAjax(procurementDocumentsItemService.updateById(procurementDocumentsItem));
    }

    /**
     * 删除采购文件编制详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:item:remove')")
    @Log(title = "采购文件编制详细信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购文件编制详细信息")
    @DeleteMapping("/{projectFileItemIds}")
    public AjaxResult remove(@PathVariable Long[] projectFileItemIds) {
        return toAjax(procurementDocumentsItemService.removeByIds(Arrays.asList(projectFileItemIds)));
    }
}
