package com.ruoyi.procurement.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.procurement.vo.CheckisOverExpVo;
import com.ruoyi.procurement.vo.GenProjectResponseVo;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import com.ruoyi.utils.ZipUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用户编制采购文件信息保存Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "用户编制采购文件信息保存管理")
@RestController
@RequestMapping("/documents/uinfo")
public class ProcurementDocumentsUinfoController extends BaseController {
    @Autowired
    private IProcurementDocumentsUinfoService procurementDocumentsUinfoService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    /**
     * 查询用户编制采购文件信息保存列表
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:list')")
    @ApiOperation(value = "查询用户编制采购文件信息保存列表")
    @GetMapping("/list")
    public TableDataInfo list(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        startPage();
        List<ProcurementDocumentsUinfo> list = procurementDocumentsUinfoService.selectList(procurementDocumentsUinfo);
        return getDataTable(list);
    }

    /**
     * 导出用户编制采购文件信息保存列表
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:export')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出用户编制采购文件信息保存列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        List<ProcurementDocumentsUinfo> list = procurementDocumentsUinfoService.selectList(procurementDocumentsUinfo);
        ExcelUtil<ProcurementDocumentsUinfo> util = new ExcelUtil<ProcurementDocumentsUinfo>(ProcurementDocumentsUinfo.class);
        util.exportExcel(response, list, "用户编制采购文件信息保存数据");
    }

    /**
     * 获取用户编制采购文件信息保存详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:query')")
    @ApiOperation(value = "检查采购文件是否制作完成")
    @GetMapping(value = "/checkIsOver")
    public AjaxResult checkIsOver(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        CheckisOverExpVo data = procurementDocumentsUinfoService.checkIsOver(procurementDocumentsUinfo);
        return success(data);
    }

    /**
     * 生成采购文件
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:query')")
    @ApiOperation(value = "生成采购文件zip")
    @GetMapping(value = "/generateProjectFileZip")
    public AjaxResult generateProjectFileZip(ProcurementDocumentsUinfo queryUinfo) {
        AjaxResult ajax = null;
        try {
            ajax = procurementDocumentsUinfoService.generateProjectFileZip(queryUinfo);
        } catch (IOException e) {
            throw new ServiceException("生成采购文件.zip失败");
        }
        return ajax;
    }

    /**
     * 生成采购文件
     */
    @ApiOperation(value = "生成采购文件")
    @PostMapping(value = "/test")
    public AjaxResult test(@RequestBody ProcurementDocumentsUinfo queryUinfo) throws IOException {
        // type  0工程，1服务，2货物
        if (queryUinfo.getType()==0){
            AjaxResult ajax = procurementDocumentsUinfoService.generateProjectFile(queryUinfo);
            return ajax;
        }else if (queryUinfo.getType()==1){
            AjaxResult ajax = procurementDocumentsUinfoService.generateServiceProjectFile(queryUinfo);
            return ajax;
        }else if (queryUinfo.getType()==2){
            AjaxResult ajax = procurementDocumentsUinfoService.generateGoodsProjectFile(queryUinfo);
            return ajax;
        }
        return null;
    }

    /**
     * 生成采购文件和采购文件正文
     */
    @ApiOperation(value = "生成采购文件")
    @PostMapping(value = "/createProjectFileAndCgwj")
    public AjaxResult createProjectFileAndCgwj(@RequestBody ProcurementDocumentsUinfo queryUinfo) throws IOException {
        // type  0工程，1服务，2货物
        /*AjaxResult ajax = procurementDocumentsUinfoService.generateProjectFile(queryUinfo);
        if (ajax.get("code").equals(200)) {
            ajax = procurementDocumentsUinfoService.generateProjectFileZip(queryUinfo);
        }*/
        // type  0工程，1服务，2货物
        BusiTenderProject project = iBusiTenderProjectService.getById(Long.parseLong(queryUinfo.getParams().get("projectId") + ""));

        if (project.getTenderMode().equals("1")){
            if (queryUinfo.getType()==0){
                AjaxResult ajax = procurementDocumentsUinfoService.generateProjectFile(queryUinfo);
                return ajax;
            }else if (queryUinfo.getType()==1){
                AjaxResult ajax = procurementDocumentsUinfoService.generateServiceProjectFile(queryUinfo);
                return ajax;
            }else if (queryUinfo.getType()==2){
                AjaxResult ajax = procurementDocumentsUinfoService.generateGoodsProjectFile(queryUinfo);
                return ajax;
            }
        }else  if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
            AjaxResult ajax = procurementDocumentsUinfoService.generateTenderModeGoodsProjectFile(queryUinfo);
            return ajax;
        }


        return null;
    }



    /**
     * 编制响应文件
     */
    @ApiOperation(value = "编制响应文件")
    @PostMapping(value = "/prepareResponseDocuments")
    public AjaxResult prepareResponseDocuments(@RequestBody ProcurementDocumentVo procurementDocumentVo) {
        procurementDocumentVo.setEntId(getEntId());
        AjaxResult ajax = AjaxResult.success(procurementDocumentsUinfoService.prepareResponseDocuments(procurementDocumentVo));
        return ajax;
    }


    @PreAuthorize("@ss.hasPermi('documents:uinfo:query')")
    @ApiOperation(value = "生成响应文件zip")
    @PostMapping(value = "/generateResponseZip")
    public ResponseEntity<FileSystemResource> generateResponseZip(
            HttpServletResponse response,
            HttpServletRequest request,
            @RequestBody GenProjectResponseVo genProjectResponseVo) throws Exception {
        String pathStr = procurementDocumentsUinfoService.generateResponseZip(genProjectResponseVo);
        // ZIP文件的完整路径
        Path path = Paths.get(pathStr);

        // 检查文件是否存在
        if (!Files.exists(path)) {
            return ResponseEntity.notFound().build();
        }

        // 设置HTTP响应头
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + path.getFileName() + "\"");

        // 创建FileSystemResource
        FileSystemResource resource = new FileSystemResource(path.toFile());

        // 返回ResponseEntity对象
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @ApiOperation(value = "根据projectId下载采购文件")
    @GetMapping("/downloadProjectFile/{projectId}")
    public ResponseEntity<FileSystemResource> downloadProjectFile(HttpServletRequest request, HttpServletResponse response, @PathVariable(value = "projectId") Long projectId) {
        ProcurementDocumentsUinfo uinfo = procurementDocumentsUinfoService.getOne(
                new QueryWrapper<ProcurementDocumentsUinfo>().eq("project_id", projectId));
        String zipFilePath = uinfo.getFilePath();
        // ZIP文件的完整路径
        Path path = Paths.get(zipFilePath);

        // 检查文件是否存在
        if (!Files.exists(path)) {
            return ResponseEntity.notFound().build();
        }

        // 设置HTTP响应头
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + path.getFileName() + "\"");

        // 创建FileSystemResource
        FileSystemResource resource = new FileSystemResource(path.toFile());

        // 返回ResponseEntity对象
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }


    @ApiOperation(value = "下载加密后的响应文件")
    @GetMapping("/downloadProjectResponseFile")
    public ResponseEntity<FileSystemResource> downloadProjectResponseFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "responseFilePath") String responseFilePath) {
        String secretKey = SecurityUtils.getLoginUser().getUser().getEnt().getSecretKey();
        if (StringUtils.isBlank(secretKey)) {
            throw new ServiceException("请先维护密钥");
        }
        String[] split = responseFilePath.split("\\/");
        String regex = split[split.length - 1];
        String[] split1 = regex.split("\\.");
        split[split.length - 1] = split1[0] + ".tbwj";
        String bidEncodeFile = StrUtil.join("\\/", split);
        try {
            ZipUtil.encrypt(responseFilePath, bidEncodeFile, Md5Utils.hash(SecurityUtils.getLoginUser().getUser().getEnt().getEntCode()).substring(0, 16));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("加密失败");
        }
        // ZIP文件的完整路径
        Path path = Paths.get(bidEncodeFile);

        // 检查文件是否存在
        if (!Files.exists(path)) {
            return ResponseEntity.notFound().build();
        }

        // 设置HTTP响应头
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + path.getFileName() + "\"");

        // 创建FileSystemResource
        FileSystemResource resource = new FileSystemResource(path.toFile());

        // 返回ResponseEntity对象
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }


    /**
     * 获取用户编制采购文件信息保存详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:query')")
    @ApiOperation(value = "获取用户编制采购文件信息保存详细信息")
    @ApiImplicitParam(name = "entFileId", value = "用户采购文件id", required = true, dataType = "Long")
    @GetMapping(value = "/{entFileId}")
    public AjaxResult getInfo(@PathVariable("entFileId") Long entFileId) {
        return success(procurementDocumentsUinfoService.getById(entFileId));
    }

    /**
     * 获取用户编制采购文件信息保存详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:query')")
    @ApiOperation(value = "获取用户编制采购文件信息保存详细信息")
    @ApiImplicitParam(name = "entFileId", value = "用户采购文件id", required = true, dataType = "Long")
    @GetMapping(value = "/getInfoById/{entFileId}")
    public AjaxResult getInfoById(@PathVariable("entFileId") Long entFileId) {
        return success(procurementDocumentsUinfoService.getInfoById(entFileId));
    }

    /**
     * 新增用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:add')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户编制采购文件信息保存")
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        procurementDocumentsUinfo.setEntId(getEntId());
        return toAjax(procurementDocumentsUinfoService.save(procurementDocumentsUinfo));
    }

    /**
     * 新增用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:add')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户编制采购文件信息保存")
    @PostMapping("/saveInfo")
    public AjaxResult saveInfo(@RequestBody ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        return success(procurementDocumentsUinfoService.saveInfo(procurementDocumentsUinfo));
    }

    /**
     * 修改用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:edit')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改用户编制采购文件信息保存")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        return toAjax(procurementDocumentsUinfoService.updateById(procurementDocumentsUinfo));
    }

    /**
     * 查询采购文件编制基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:info:list')")
    @ApiOperation(value = "查询采购文件编制基础信息列表")
    @GetMapping("/infoByParams")
    public AjaxResult infoByParams(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        ProcurementDocumentsUinfo info = procurementDocumentsUinfoService.infoByParams(procurementDocumentsUinfo);
        return success(info);
    }

    /**
     * 删除用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uinfo:remove')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除用户编制采购文件信息保存")
    @DeleteMapping("/{entFileIds}")
    public AjaxResult remove(@PathVariable Long[] entFileIds) {
        return toAjax(procurementDocumentsUinfoService.removeByIds(Arrays.asList(entFileIds)));
    }
}
