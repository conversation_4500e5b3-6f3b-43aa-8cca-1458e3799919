package com.ruoyi.procurement.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.service.IProcurementDocumentsUitemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 用户编制采购文件信息保存Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "用户编制采购文件信息保存管理")
@RestController
@RequestMapping("/documents/uitem")
public class ProcurementDocumentsUitemController extends BaseController {
    @Autowired
    private IProcurementDocumentsUitemService procurementDocumentsUitemService;

/**
 * 查询用户编制采购文件信息保存列表
 */
@PreAuthorize("@ss.hasPermi('documents:uitem:list')")
@ApiOperation(value = "查询用户编制采购文件信息保存列表")
@GetMapping("/list")
    public TableDataInfo list(ProcurementDocumentsUitem procurementDocumentsUitem) {
        startPage();
        List<ProcurementDocumentsUitem> list = procurementDocumentsUitemService.selectList(procurementDocumentsUitem);
        return getDataTable(list);
    }

    /**
     * 导出用户编制采购文件信息保存列表
     */
    @PreAuthorize("@ss.hasPermi('documents:uitem:export')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出用户编制采购文件信息保存列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementDocumentsUitem procurementDocumentsUitem) {
        List<ProcurementDocumentsUitem> list = procurementDocumentsUitemService.selectList(procurementDocumentsUitem);
        ExcelUtil<ProcurementDocumentsUitem> util = new ExcelUtil<ProcurementDocumentsUitem>(ProcurementDocumentsUitem. class);
        util.exportExcel(response, list, "用户编制采购文件信息保存数据");
    }

    /**
     * 获取用户编制采购文件信息保存详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:uitem:query')")
    @ApiOperation(value = "获取用户编制采购文件信息保存详细信息")
    @ApiImplicitParam(name = "entFileId", value = "用户采购文件id", required = true, dataType = "Long")
    @GetMapping(value = "/{entFileId}")
    public AjaxResult getInfo(@PathVariable("entFileId")Long entFileId) {
        return success(procurementDocumentsUitemService.getById(entFileId));
    }

    /**
     * 新增用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uitem:add')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户编制采购文件信息保存")
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementDocumentsUitem procurementDocumentsUitem) {
        procurementDocumentsUitem.setEntId(getEntId());
        return toAjax(procurementDocumentsUitemService.save(procurementDocumentsUitem));
    }

    /**
     * 修改用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uitem:edit')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改用户编制采购文件信息保存")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementDocumentsUitem procurementDocumentsUitem) {
        return toAjax(procurementDocumentsUitemService.updateById(procurementDocumentsUitem));
    }

    /**
     * 删除用户编制采购文件信息保存
     */
    @PreAuthorize("@ss.hasPermi('documents:uitem:remove')")
    @Log(title = "用户编制采购文件信息保存", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除用户编制采购文件信息保存")
    @DeleteMapping("/{entFileIds}")
    public AjaxResult remove(@PathVariable Long[] entFileIds) {
        return toAjax(procurementDocumentsUitemService.removeByIds(Arrays.asList(entFileIds)));
    }
}
