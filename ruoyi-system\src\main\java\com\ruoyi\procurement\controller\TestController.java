package com.ruoyi.procurement.controller;

import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigInteger;

public class TestController {

    public static void main(String[] args)throws Exception {

        XWPFDocument doc = new XWPFDocument(new FileInputStream("C:\\Users\\<USER>\\Pictures\\shuiyin\\限额以下模板文件\\磋商采购文件.docx"));


    }

}
