package com.ruoyi.procurement.designs.factory;

import com.ruoyi.common.enums.ResponseDocType;
import com.ruoyi.procurement.designs.strategy.InquiryPriceGoodsDoc;
import com.ruoyi.procurement.designs.strategy.NegotiateGoodsDoc;
import com.ruoyi.procurement.designs.strategy.NegotiateProjectDoc;
import com.ruoyi.procurement.designs.strategy.NegotiateServiceDoc;
import com.ruoyi.procurement.designs.template.AbstractDocResponseStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class DocResponseFactory implements ApplicationContextAware {
    @Autowired
    ApplicationContext applicationContext;

    public AbstractDocResponseStrategy getDocResponse(long docType) throws IllegalAccessException {

        if (ResponseDocType.CS_PROJECT_RES_DOC.getCode() == docType) {
            return applicationContext.getBean(NegotiateProjectDoc.class);
        } else if (ResponseDocType.CS_GOODS_RES_DOC.getCode() == docType) {
            return applicationContext.getBean(NegotiateGoodsDoc.class);
        } else if (ResponseDocType.CS_SERVICE_RES_DOC.getCode() == docType) {
            return applicationContext.getBean(NegotiateServiceDoc.class);
        } else if (ResponseDocType.XJ_GOODS_RES_DOC.getCode() == docType) {
            return applicationContext.getBean(InquiryPriceGoodsDoc.class);
        } else {
            throw new IllegalAccessException("无效的参数");
        }

    }

    public void setApplicationContext(ApplicationContext applicationContext){
        this.applicationContext = applicationContext;
    }




}




