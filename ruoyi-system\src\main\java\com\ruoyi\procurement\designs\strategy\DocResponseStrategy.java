package com.ruoyi.procurement.designs.strategy;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;

import java.util.List;
import java.util.Map;

public interface DocResponseStrategy {

    void customizeCheckData(JSONObject data, DocResponseEntInfo docEntInfo);

    Map<String,String> customizeLogicalDecision (BusiTenderProject project, List<ProcurementDocumentsUitem> pUitemList, DocResponseEntInfo docEntInfo, Map<String,String> resultMap);

    // 准备自定义模板文档的数据
    ProcurementDocumentVo prepareCustomizeTemplateDocData(ProcurementDocumentVo vo, JSONObject data, BaseEntInfo ent, DocResponseEntInfo docEntInfo);

}
