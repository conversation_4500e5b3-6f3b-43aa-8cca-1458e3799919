package com.ruoyi.procurement.designs.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.enums.ResponseDocType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.procurement.designs.template.AbstractDocResponseStrategy;
import com.ruoyi.procurement.designs.template.DetailContent;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.ExcelReader;
import com.ruoyi.utils.ExcelToPdfConverter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 询价 货物响应文档
 */
@Log4j2
@Component
public class InquiryPriceGoodsDoc extends AbstractDocResponseStrategy {

    @Override
    public void customizeCheckData(JSONObject data, DocResponseEntInfo docEntInfo) {
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();

        // 明细报价表
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.MXBJB.getCode())) {
            throw new ServiceException(ResponseDocEnum.MXBJB.getInfo()+"信息不能为空");
        }
        // 信用查询部分
        if(data.getString("xylx").equals("jt")){
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.XYZG.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.XYZG.getCode())) {
                throw new ServiceException(ResponseDocEnum.XYZG.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZXXXGKW.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZFCGW.getInfo() + "信用查询截图不能为空");
            }
        }
        // 技术部分
        // 1．技术偏离表
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.JSPLB.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.JSPLB.getCode())) {
            throw new ServiceException(ResponseDocEnum.JSPLB.getInfo() + "信息不能为空");
        }
    }

    @Override
    public ProcurementDocumentVo prepareCustomizeTemplateDocData(ProcurementDocumentVo vo, JSONObject data, BaseEntInfo ent, DocResponseEntInfo docEntInfo) {
        vo.setTemplateName(ResponseDocType.XJ_GOODS_RES_DOC.getInfo());
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        setJsbf(vo, detailMap);
        setMxbjb(vo, detailMap);
        setXycx(data, vo, detailMap);
        setZgsc(data, vo, detailMap);
        return vo;
    }

    @Override
    public Map<String,String> customizeLogicalDecision(BusiTenderProject project, List<ProcurementDocumentsUitem> pUitemList, DocResponseEntInfo docEntInfo, Map<String,String> resultMap) {
        StringBuffer resultMsg = new StringBuffer();
        // 一、 报价明细判定
        // 读取供应商上传的报价明细表，统计单项报价，计算合计并与报价进行对比，一致视为通过。
        // 获取明细报价表总价
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        DocResponseEntDetail mxbjbDetails  = detailMap.get("mxbjb").get(0);

        String mxbjbFilePath = AttachmentUtil.urlToReal(mxbjbDetails.getFilePath());
        // 获取投标报价
        List<DocResponseEntDetail> kbylbDetail = detailMap.get(ResponseDocEnum.KBYLB.getCode());
        JSONObject kbylbObject = JSONObject.parseObject(kbylbDetail.get(0).getDetailContent());
        String resBidPrice = kbylbObject.getString("bidPrice");
        boolean isTrueMxbjbPrice = mxbjbPriceDecision(mxbjbFilePath, resBidPrice);
        if (isTrueMxbjbPrice) {
            resultMsg.append("\n ||明细报价表分项总和与投标报价校验 - 通过;");
            resultMap.put("mxbjb", "1");
        } else {
            resultMsg.append("\n ||明细报价表分项总和与投标报价校验 - 不通过;");
            resultMap.put("mxbjb", "0");
        }

        // 二、 技术偏离表偏离说名判定
        // 偏离说明中包含“无”,“正偏离”通过，否则不通过
        DocResponseEntDetail jsplbDetails  = detailMap.get("jsplb").get(0);
        String jsplbFilePath = AttachmentUtil.urlToReal(jsplbDetails.getFilePath());
        boolean isTrueJsplbDecision = jsplbDecision(jsplbFilePath);
        if (isTrueJsplbDecision) {
            resultMsg.append("\n ||技术偏离表中偏离情况校验 - 通过;");
            resultMap.put("jsplb", "1");
        } else {
            resultMsg.append("\n ||技术偏离表中偏离情况校验 - 不通过;");
            resultMap.put("jsplb", "1");
        }
        resultMap.put(ResponseDocEnum.RES_DOC_DECISION.getCode(), resultMsg.toString());
        return resultMap;
    }

    /**
     * 技术偏离表偏离情况校验
     * @param jsplbFilePath 技术偏离表完整访问路径
     * @return
     */
    public static boolean jsplbDecision(String jsplbFilePath) {
        List<String> deviationList = null;
        try {
            deviationList = ExcelReader.getDeviationFromJsplb(jsplbFilePath);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("读取技术偏离表数据异常");
        }
        if (deviationList.size()<=0) {
            throw new ServiceException("技术偏离表偏离说明不能为空");
        }
        boolean allContainWords = deviationList.stream()
                .allMatch(item -> item.contains("无") || item.contains("正"));
        if (allContainWords) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 明细报价表总价与投标总价校验
     * @param mxbjbFilePath 明细报价表完整访问路径
     * @param resBidPrice 投标报价
     * @return 一致返回 true
     */
    public static boolean mxbjbPriceDecision(String mxbjbFilePath, String resBidPrice) {
        double totalPrice = 0.0;
        try {
            List<String> priceList = ExcelReader.getUnitPriceFromMxbjb(mxbjbFilePath);
            totalPrice = calculateSum(priceList);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("读取明细报价表数据异常");
        }
        double tbPrice = Double.parseDouble(resBidPrice);
        if (totalPrice == tbPrice) {
            return true;
        } else {
            return false;
        }
    }

    public static double calculateSum(List<String> values) {
        double sum = 0;
        for (String value : values) {
            if (DetailContent.isDouble(value)) {
                double num = Double.parseDouble(value);
                sum += num;
            }
        }
        return sum;
    }

    public void setMxbjb(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        DocResponseEntDetail mxbjbDetails = detailMap.get("mxbjb").get(0);
//        vo.getContentMap().put("mxbjb", removeTags(mxbjbDetails.getDetailContent()));
        String mxbjbFilePath = mxbjbDetails.getFilePath();
        vo.getPdfFiles().put("）明细报价表-e", AttachmentUtil.urlToReal(mxbjbFilePath));
        // 判断是否有多个文件
        if (StringUtils.isNotEmpty(mxbjbFilePath)) {
            String[] mxbjbArr = mxbjbFilePath.split(",");
            for (String filePath:mxbjbArr) {
                String mxbjbPath = AttachmentUtil.urlToReal(filePath);
                String mxbjbPdfPath = RuoYiConfig.getUploadPath() + "/" + UUID.randomUUID().toString().replaceAll("-","") + ".pdf";
                if (filePath.contains("xlsx")) { // 是xlsx文件，需要转pdf
                    try {
                        ExcelToPdfConverter.convertExcelToPdf(mxbjbPath, mxbjbPdfPath);
                    } catch (Exception e) {
                        log.error("excel文件转pdf文件异常",e.getMessage());
                        e.printStackTrace();
                        throw new ServiceException("excel文件转pdf文件异常");
                    }
                    vo.getPdfFiles().put("）明细报价表", mxbjbPdfPath);
                } else {
                    vo.getPdfFiles().put("）明细报价表", mxbjbPath);
                }
            }
        }
    }

    private void setXycx(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }
        if(data.getString("xylx").equals("jt")){
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }
    }

    private void setJsbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        DocResponseEntDetail jsplbDetail = detailMap.get(ResponseDocEnum.JSPLB.getCode()).get(0);
//        vo.getContentMap().put("jsplb", removeTags(jsplbDetail.getDetailContent()));
        String jsplbFilePath = jsplbDetail.getFilePath();
        // 判断是否有多个文件
        if (StringUtils.isNotEmpty(jsplbFilePath)) {
            String[] jsplbArr = jsplbFilePath.split(",");
            for (String filePath : jsplbArr) {
                String jsplbPath = AttachmentUtil.urlToReal(filePath);
                String jsplbPdfPath = RuoYiConfig.getUploadPath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".pdf";
                if (filePath.contains("xlsx")) { // 是xlsx文件，需要转pdf
                    try {
                        ExcelToPdfConverter.convertExcelToPdf(jsplbPath, jsplbPdfPath);
                    } catch (Exception e) {
                        log.error("excel文件转pdf文件异常", e.getMessage());
                        e.printStackTrace();
                        throw new ServiceException("excel文件转pdf文件异常");
                    }
                    vo.getPdfFiles().put("、技术偏离表", jsplbPdfPath);
                } else {
                    vo.getPdfFiles().put("、技术偏离表", jsplbPath);
                }
            }
        }
    }

    // 设置资格审查文件
    private void setZgsc(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if(data.getString("tsqylx").equals("zxqy")){
            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            StringBuffer zxqysmhxx = new StringBuffer();
            for (int i=0; i<zxqysmhArry.size(); i++) {
                JSONObject obj = zxqysmhArry.getJSONObject(i);
                zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
            };
            vo.setZxqysmhxx(zxqysmhxx.toString());
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            vo.setJyqy(true);
            vo.addPdfFile("：监狱企业", jyqyDetail.getFilePath());
        }
    }


}
