package com.ruoyi.procurement.designs.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.enums.ResponseDocType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.procurement.designs.template.AbstractDocResponseStrategy;
import com.ruoyi.procurement.designs.template.DetailContent;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 磋商货物响应文档
 */
@Log4j2
@Component
public class NegotiateGoodsDoc extends AbstractDocResponseStrategy {

    @Override
    public void customizeCheckData(JSONObject data, DocResponseEntInfo docEntInfo) {
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        List<DocResponseEntDetail> businessPartList = docEntInfo.getBusinessPart();

        // 明细报价表
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.MXBJB.getCode())) {
            throw new ServiceException(ResponseDocEnum.MXBJB.getInfo()+"信息不能为空");
        }
        // 信用查询部分
        if(data.getString("xylx").equals("jt")){
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.XYZG.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.XYZG.getCode())) {
                throw new ServiceException(ResponseDocEnum.XYZG.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZXXXGKW.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZFCGW.getInfo() + "信用查询截图不能为空");
            }
        }

        // 技术部分
        // 1.技术方案
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.JSFA.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.JSFA.getCode())) {
            throw new ServiceException(ResponseDocEnum.JSFA.getInfo() + "信息不能为空");
        }
        // 2.技术偏离表
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.JSPLB.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.JSPLB.getCode())) {
            throw new ServiceException(ResponseDocEnum.JSPLB.getInfo() + "信息不能为空");
        }

        // 商务部分
        if (businessPartList==null || businessPartList.size()==0) {
            throw new ServiceException("商务部分内容不能为空");
        }
        for (DocResponseEntDetail part:businessPartList) {
            if (DetailContent.isEmpty(detailMap, part.getDetailCode()) || DetailContent.isFileEmpty(detailMap,part.getDetailCode())) {
                throw new ServiceException(part.getDetailName() + "不能为空");
            }
        }

    }

    @Override
    public ProcurementDocumentVo prepareCustomizeTemplateDocData(ProcurementDocumentVo vo, JSONObject data, BaseEntInfo ent, DocResponseEntInfo docEntInfo) {
        vo.setTemplateName(ResponseDocType.CS_GOODS_RES_DOC.getInfo());
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        List<DocResponseEntDetail> businessPartList = docEntInfo.getBusinessPart();
        setMxbjb(vo, detailMap);
        setXycx(data, vo, detailMap);
        setQtzgzmwj(vo, detailMap);
        setJsbf(vo, detailMap);
        setSwbf(vo, detailMap, businessPartList);
        setZgsc(data, vo, detailMap);
        return vo;
    }

    @Override
    public Map<String,String> customizeLogicalDecision(BusiTenderProject project, List<ProcurementDocumentsUitem> pUitemList, DocResponseEntInfo docEntInfo, Map<String,String> resultMap) {
        return resultMap;
    }

    private void setMxbjb(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        DocResponseEntDetail mxbjbDetails = detailMap.get("mxbjb").get(0);
//        vo.getContentMap().put("mxbjb", removeTags(mxbjbDetails.getDetailContent()));
        String mxbjbFilePath = mxbjbDetails.getFilePath();
        vo.getPdfFiles().put("）明细报价表", mxbjbFilePath);
    }

    private void setXycx(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }
        if(data.getString("xylx").equals("jt")){
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }
    }

    // 其他资格证明文件
    private void setQtzgzmwj(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if (detailMap.get("qtzgzmwj") != null) {
            List<DocResponseEntDetail> qtzgzmwjDetails = detailMap.get("qtzgzmwj");
            for (DocResponseEntDetail detail : qtzgzmwjDetails) {
//                if (detail.getFilePath().isEmpty()) {
//                    throw new ServiceException("其他资格证明文件不能为空");
//                }
                vo.getPdfFiles().put("、其他资格证明文件", detail.getFilePath());
            }
        }
    }

    private void setJsbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        // 1.技术方案
        DocResponseEntDetail jsfaDetail = detailMap.get(ResponseDocEnum.JSFA.getCode()).get(0);
        vo.getPdfFiles().put("1、技术方案", jsfaDetail.getFilePath());
        // 2．技术偏离表
        DocResponseEntDetail jsplbDetail = detailMap.get(ResponseDocEnum.JSPLB.getCode()).get(0);
        vo.getPdfFiles().put("2、技术偏离表", jsplbDetail.getFilePath());
    }

    private void setSwbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap, List<DocResponseEntDetail> businessPartList) {
        int i = 1;
        for (DocResponseEntDetail part:businessPartList) {
            DocResponseEntDetail businessPartDetail = detailMap.get(part.getDetailCode()).get(0);
            vo.getPdfFiles().put(i+"、"+part.getDetailName(), businessPartDetail.getFilePath());
            if (i==1) {
                vo.setSwbf1(true);
                vo.setSwbfName1(i+"、"+part.getDetailName());
            }
            if (i==2) {
                vo.setSwbf2(true);
                vo.setSwbfName2(i+"、"+part.getDetailName());
            }
            if (i==3) {
                vo.setSwbf3(true);
                vo.setSwbfName3(i+"、"+part.getDetailName());
            }
            if (i==4) {
                vo.setSwbf4(true);
                vo.setSwbfName4(i+"、"+part.getDetailName());
            }
            if (i==5) {
                vo.setSwbf5(true);
                vo.setSwbfName5(i+"、"+part.getDetailName());
            }
            if (i==6) {
                vo.setSwbf6(true);
                vo.setSwbfName6(i+"、"+part.getDetailName());
            }
            i++;
        }

    }

    // 设置资格审查文件
    private void setZgsc(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if(data.getString("tsqylx").equals("zxqy")){
            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            StringBuffer zxqysmhxx = new StringBuffer();
            for (int i=0; i<zxqysmhArry.size(); i++) {
                JSONObject obj = zxqysmhArry.getJSONObject(i);
                zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
            };
            vo.setZxqysmhxx(zxqysmhxx.toString());
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            vo.setJyqy(true);
            vo.addPdfFile("：监狱企业", jyqyDetail.getFilePath());
        }
    }



}
