package com.ruoyi.procurement.designs.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.enums.ResponseDocType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.procurement.designs.template.AbstractDocResponseStrategy;
import com.ruoyi.procurement.designs.template.DetailContent;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 磋商-工程响应文档
 */
@Log4j2
@Component
public class NegotiateProjectDoc extends AbstractDocResponseStrategy {

    @Override
    public void customizeCheckData(JSONObject data, DocResponseEntInfo docEntInfo) {
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        List<DocResponseEntDetail> businessPartList = docEntInfo.getBusinessPart();
        // 信用查询部分
        if(data.getString("xylx").equals("jt")){
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZFCGW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZFCGW.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.ZGZXXXGKW.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZGZXXXGKW.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.XYZG.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.XYZG.getCode())) {
                throw new ServiceException(ResponseDocEnum.XYZG.getInfo() + "信用查询截图不能为空");
            }
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.QGJZSCJGGGFWPT.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.QGJZSCJGGGFWPT.getCode())) {
                throw new ServiceException(ResponseDocEnum.QGJZSCJGGGFWPT.getInfo() + "信用查询截图不能为空");
            }
        }

        // 已标价工程量清单
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.YBJGCLQD.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.YBJGCLQD.getCode())) {
            throw new ServiceException(ResponseDocEnum.YBJGCLQD.getInfo() + "信息不能为空");
        }

        // 技术部分
        // 1、施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.SGFAJJSCS.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.SGFAJJSCS.getCode())) {
            throw new ServiceException(ResponseDocEnum.SGFAJJSCS.getInfo() + "信息不能为空");
        }
        // 2、工程进度计划与措施、施工进度或施工网络图、施工总平面布置图
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.GCJDJHYCS.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.GCJDJHYCS.getCode())) {
            throw new ServiceException(ResponseDocEnum.GCJDJHYCS.getInfo() + "信息不能为空");
        }
        // 3、节能减排（绿色施工、工艺创新）在本工程的具体应用措施
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.JNJP.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.JNJP.getCode())) {
            throw new ServiceException(ResponseDocEnum.JNJP.getInfo() + "信息不能为空");
        }
        // 4、新工艺（新技术、新设备、新材料）的采用程度
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.XGY.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.XGY.getCode())) {
            throw new ServiceException(ResponseDocEnum.XGY.getInfo() + "信息不能为空");
        }
        // 5、风险管理措施
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.FXGLCS.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.FXGLCS.getCode())) {
            throw new ServiceException(ResponseDocEnum.FXGLCS.getInfo() + "信息不能为空");
        }

        // 商务部分
        // 1、体系认证
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.TXRZ.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.TXRZ.getCode())) {
            throw new ServiceException(ResponseDocEnum.TXRZ.getInfo() + "信息不能为空");
        }
        // 拟派项目管理人员
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.NPXMGLRY.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.NPXMGLRY.getCode())) {
            throw new ServiceException(ResponseDocEnum.NPXMGLRY.getInfo() + "信息不能为空");
        }
        // 项目技术负责人
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.XMJSFZR.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.XMJSFZR.getCode())) {
            throw new ServiceException(ResponseDocEnum.XMJSFZR.getInfo() + "信息不能为空");
        }
        // 类似业绩
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.LSYJ.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.LSYJ.getCode())) {
            throw new ServiceException(ResponseDocEnum.LSYJ.getInfo() + "信息不能为空");
        }
    }

    @Override
    public ProcurementDocumentVo prepareCustomizeTemplateDocData(ProcurementDocumentVo vo, JSONObject data, BaseEntInfo ent, DocResponseEntInfo docEntInfo) {
        vo.setTemplateName(ResponseDocType.CS_PROJECT_RES_DOC.getInfo());
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        List<DocResponseEntDetail> businessPartList = docEntInfo.getBusinessPart();
        setZgsc(data, vo, detailMap);
        setXycx(data, vo, detailMap);
        setQtzgzmwj(vo, detailMap);
        setFhxps(vo, detailMap);
        setJsbf(vo, detailMap);
        setSwbf(vo, detailMap, businessPartList);
        return vo;
    }

    @Override
    public Map<String,String> customizeLogicalDecision(BusiTenderProject project, List<ProcurementDocumentsUitem> pUitemList, DocResponseEntInfo docEntInfo, Map<String,String> resultMap) {
        return resultMap;
    }

    // 设置资格审查文件
    private void setZgsc(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if(data.getString("tsqylx").equals("zxqy")){
//            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            JSONObject zxqysmh = JSONObject.parseObject(detailMap.get("zxqysmh").get(0).getDetailContent());
            vo.setTrade(zxqysmh.getString("sshy"));
            vo.setPractitionerNum(zxqysmh.getString("cyryrs"));
            vo.setOperatingRevenue(zxqysmh.getBigDecimal("yysr"));
            vo.setTotalAssets(zxqysmh.getBigDecimal("zcze"));
            vo.setCompanyType(zxqysmh.getString("qylx"));
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            vo.setJyqy(true);
            vo.addPdfFile("：监狱企业", jyqyDetail.getFilePath());
        }
    }

    private void setXycx(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }
        if(data.getString("xylx").equals("jt")){
            DocResponseEntDetail qgjzscjgggfwptDetail = detailMap.get("qgjzscjgggfwpt").get(0);
            vo.getPdfFiles().put("全国建筑市场监管公共服务平台", qgjzscjgggfwptDetail.getFilePath());
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }
    }

    // 其他资格证明文件
    private void setQtzgzmwj(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if (detailMap.get("qtzgzmwj") != null) {
            List<DocResponseEntDetail> qtzgzmwjDetails = detailMap.get("qtzgzmwj");
            for (DocResponseEntDetail detail : qtzgzmwjDetails) {
//                if (detail.getFilePath().isEmpty()) {
//                    throw new ServiceException("其他资格证明文件不能为空");
//                }
                vo.getPdfFiles().put("、其他资格证明文件", detail.getFilePath());
            }
        }
    }

    // 符合性审查
    private void setFhxps(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        List<DocResponseEntDetail> ybjgclqdDetails = detailMap.get("ybjgclqd");
        for (DocResponseEntDetail detail : ybjgclqdDetails) {
            vo.getPdfFiles().put("响应人应按根据第五章“工程量清单”的要求", detail.getFilePath());
        }
        // 其他符合性因素
//        if (detailMap.get("qtfhxys") != null) {
//            List<DocResponseEntDetail> qtfhxysDetails = detailMap.get("qtfhxys");
//            for (DocResponseEntDetail detail : qtfhxysDetails) {
//                setAtts(vo, detail.getFilePath(), "QTSHXFYS");
//            }
//        }
    }

    private void setJsbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        // 1. 施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划
        DocResponseEntDetail sgfajjscsDetail = detailMap.get("sgfajjscs").get(0);
        vo.getPdfFiles().put("1、施工方案及技术措施", sgfajjscsDetail.getFilePath());
        // 2．工程进度计划与措施、施工进度或施工网络图、施工总平面布置图
        DocResponseEntDetail gcjdjhycsDetail = detailMap.get("gcjdjhycs").get(0);
        vo.getPdfFiles().put("2、工程进度计划与措施", gcjdjhycsDetail.getFilePath());
        // 3. 节能减排（绿色施工、工艺创新）在本工程的具体应用措施
        DocResponseEntDetail jnjpDetail = detailMap.get("jnjp").get(0);
        vo.getPdfFiles().put("3、节能减排", jnjpDetail.getFilePath());
        // 4. 新工艺（新技术、新设备、新材料）的采用程度
        DocResponseEntDetail xgyDetail = detailMap.get("xgy").get(0);
        vo.getPdfFiles().put("4、新工艺", xgyDetail.getFilePath());
        // 5. 风险管理措施
        DocResponseEntDetail fxglcsDetail = detailMap.get("fxglcs").get(0);
        vo.getPdfFiles().put("5、风险管理措施", fxglcsDetail.getFilePath());
    }

    private void setSwbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap, List<DocResponseEntDetail> businessPartList) {
        // 1.体系认证
        DocResponseEntDetail txrzDetail = detailMap.get("txrz").get(0);
        vo.getContentMap().put("txrz", txrzDetail.getDetailContent());
        vo.getPdfFiles().put("1、体系认证", txrzDetail.getFilePath());
        // 2.拟派项目管理人员
        DocResponseEntDetail npxmglryDetail = detailMap.get("npxmglry").get(0);
        vo.getContentMap().put("npxmglry", npxmglryDetail.getDetailContent());
        vo.getPdfFiles().put("2、拟派项目管理人员", npxmglryDetail.getFilePath());
        // 3.项目技术负责人
        DocResponseEntDetail xmjsfzrDetail = detailMap.get("xmjsfzr").get(0);
        vo.getContentMap().put("xmjsfzr", xmjsfzrDetail.getDetailContent());
        vo.getPdfFiles().put("3、项目技术负责人", xmjsfzrDetail.getFilePath());
        // 4.类似业绩
        DocResponseEntDetail lsyjDetail = detailMap.get("lsyj").get(0);
        vo.getContentMap().put("lsyj", lsyjDetail.getDetailContent());
        vo.getPdfFiles().put("4、类似业绩", lsyjDetail.getFilePath());
    }


}
