package com.ruoyi.procurement.designs.template;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.designs.factory.DocResponseFactory;
import com.ruoyi.procurement.designs.strategy.DocResponseStrategy;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.mapper.ProcurementDocumentsUitemMapper;
import com.ruoyi.procurement.service.IDocResponseEntDetailService;
import com.ruoyi.procurement.service.IDocResponseEntInfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import com.ruoyi.procurement.vo.ScoreItemKeywordEnum;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.PdfUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 统一处理了以下内容
 *   1 开标一览表
 *   2 法定代表人身份证明或授权书
 *   3 特定资格要求 （营业执照、资质证明）
 * */
@Log4j2
public abstract class AbstractDocResponseStrategy implements DocResponseStrategy {

    @Autowired
    DocResponseFactory docResponseFactory;
    @Autowired
    IDocResponseEntDetailService docResponseEntDetailService;
    @Autowired
    IDocResponseEntInfoService docResponseEntInfoService;
    @Autowired
    IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    ProcurementDocumentsUitemMapper procurementDocumentsUitemMapper;
    @Autowired
    private IProcurementDocumentsUinfoService procurementDocumentsUinfoService;


    // 响应文件校验
    public final Map<String, String> checkAndDecision(JSONObject data, DocResponseEntInfo docEntInfo, BusiTenderProject project,List<ProcurementDocumentsUitem> pUitemList) throws Exception {
        // 检查数据
        checkData(data, docEntInfo);
        // 逻辑判定
        Map<String, String> resultMsg = logicalDecision(project, pUitemList, docEntInfo);
        return resultMsg;
    }

    // 生成响应文件
    public final String createDocResponse(JSONObject data, BaseEntInfo baseEntInfo, DocResponseEntInfo docEntInfo, BusiTenderProject project) throws Exception {
        // 准备通用模板文档数据
        ProcurementDocumentVo vo = prepareCommonTemplateDocData(data, baseEntInfo, docEntInfo);
        // 生成文档
        String url = generateDoc(vo, docEntInfo, project, baseEntInfo);
        return url;
    }

    public void checkData(JSONObject data, DocResponseEntInfo docEntInfo) {
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        // 开标一览表
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.KBYLB.getCode())) {
            throw new ServiceException(ResponseDocEnum.KBYLB.getInfo()+"信息不能为空");
        }

        // 法定代表人
        if("fr".equals(data.getString("frorsqs"))){
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.FDDBRSFZM.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.FDDBRSFZM.getCode())) {
                throw new ServiceException(ResponseDocEnum.FDDBRSFZM.getInfo() + "信息不能为空");
            }
        }
        if("sqs".equals(data.getString("frorsqs"))){
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.FDDBRSQS.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.FDDBRSQS.getCode())) {
                throw new ServiceException(ResponseDocEnum.FDDBRSQS.getInfo() + "信息不能为空");
            }
        }

        // 资格审查及评审资料
        if("zxqy".equals(data.getString("tsqylx"))){ // 中小企业
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZXQYSMH.getCode())) {
                throw new ServiceException(ResponseDocEnum.ZXQYSMH.getInfo() + "信息不能为空");
            }
        }
        if("jyqy".equals(data.getString("tsqylx"))){ // 监狱企业
            if (DetailContent.isEmpty(detailMap, ResponseDocEnum.JYQY.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.JYQY.getCode())) {
                throw new ServiceException(ResponseDocEnum.JYQY.getInfo() + "信息不能为空");
            }
        }

        // 特定资格要求
        // 营业执照
        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.YYZZ.getCode()) || DetailContent.isFileEmpty(detailMap, ResponseDocEnum.YYZZ.getCode())) {
            throw new ServiceException(ResponseDocEnum.YYZZ.getInfo() + "信息不能为空");
        }
        DocResponseEntDetail yyzzDetail  = detailMap.get(ResponseDocEnum.YYZZ.getCode()).get(0);
        if (!yyzzDetail.getFilePath().contains(".pdf")) {
            throw new ServiceException(ResponseDocEnum.YYZZ.getInfo() + "必须是pdf文件");
        }
        // 资质证明
//        if (DetailContent.isEmpty(detailMap, ResponseDocEnum.ZZZM.getCode())) {
//            throw new ServiceException(ResponseDocEnum.ZZZM.getInfo() + "信息不能为空");
//        }

        // 自定义检查数据
        customizeCheckData(data, docEntInfo);
    }

    public Map<String, String> logicalDecision (BusiTenderProject project ,List<ProcurementDocumentsUitem> pUitemList, DocResponseEntInfo docEntInfo) {
        StringBuffer checkMsg = new StringBuffer();
        Map<String, List<DocResponseEntDetail>> detailMap = docEntInfo.getDetailMap();
        Map<String,String> resultMap = new HashMap<>();
        // 一、 特定资格要求判定
        // 将项目资格要求与供应商填写的资格证书名称进行比对，如名称一致且无漏项视为通过。
        if (!DetailContent.isEmpty(detailMap, ResponseDocEnum.ZZZM.getCode())) {
            String projectCertificate = project.getBidderQualification();
            List<DocResponseEntDetail> zzzmsDetail = detailMap.get(ResponseDocEnum.ZZZM.getCode());
            boolean isTrueCertName = certNameDecision(projectCertificate, zzzmsDetail);
            if (isTrueCertName) {
                checkMsg.append("\n ||资质证书名称一致性校验 - 通过;");
                resultMap.put("zgzs","1");
            } else {
                checkMsg.append("\n ||资质证书名称一致性校验 - 不通过;");
                resultMap.put("zgzs","0");
            }
        }

        // 二、供货期判定
        // 获取项目中的供货期，与供应商填写的供货期进行对比，供应商供货期等于或小于项目要求的供货期，视为通过。
        // 三、投标报价判定
        // 获取项目中的预算金额，与供应商填写的报价进行对比，等于或小于预算价视为通过。
        Optional<ProcurementDocumentsUitem> proDocUitem = pUitemList.stream()
                .filter(ele -> ele.getItemName().equals(ResponseDocEnum.KBYLB.getInfo()))
                .findFirst();
        String jsonStr = proDocUitem.get().getItemContent();
        JSONArray jsonArray = JSONArray.parseArray(jsonStr);
        String proOverTimeLimit = null; // 工期/供货期
        String proBidPrice = null; // 投标报价

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            // 检查当前对象的 code 是否为 overTimeLimit
            if ("overTimeLimit".equals(jsonObject.getString("code"))) {
                proOverTimeLimit = jsonObject.getString("defaultValue");
            }
            if ("bidPrice".equals(jsonObject.getString("code"))) {
                proBidPrice = jsonObject.getString("defaultValue");
            }
        }
        if (StringUtils.isEmpty(proOverTimeLimit) || !DetailContent.isInteger(proOverTimeLimit)) {
            throw new ServiceException("系统校验：(采购文件)工期/供货期不能为空且必须为数字");
        }
        if (StringUtils.isEmpty(proBidPrice) || !DetailContent.isDouble(proBidPrice)) {
            throw new ServiceException("系统校验：(采购文件)投标报价不能为空且必须为数字");
        }

        List<DocResponseEntDetail> kbylbDetail = detailMap.get(ResponseDocEnum.KBYLB.getCode());
        String kbylbContent = kbylbDetail.get(0).getDetailContent();
        JSONObject kbylbObject = JSONObject.parseObject(kbylbContent);
        String resOverTimeLimit = kbylbObject.getString("overTimeLimit");
        String resBidPrice = kbylbObject.getString("bidPrice");
        if (StringUtils.isEmpty(resOverTimeLimit) || !DetailContent.isInteger(resOverTimeLimit)) {
            throw new ServiceException("系统校验：(响应文件)工期/供货期不能为空且必须为数字");
        }
        if (StringUtils.isEmpty(resBidPrice) || !DetailContent.isDouble(resBidPrice)) {
            throw new ServiceException("系统校验：(响应文件)投标报价不能为空且必须为数字");
        }
        boolean isTrueGhq = durationDecision(proOverTimeLimit, resOverTimeLimit);
        if (isTrueGhq) {
            checkMsg.append("\n ||工期/供货期时间校验 - 通过;");
            resultMap.put("ghqx","1");
        } else {
            checkMsg.append("\n ||工期/供货期时间校验 - 不通过;");
            resultMap.put("ghqx","0");
        }

        boolean isTrueTbbj = tbPriceDecision(proBidPrice, resBidPrice);
        if (isTrueTbbj) {
            checkMsg.append("\n ||投标报价校验 - 通过;");
            resultMap.put("tbbj","1");
        } else {
            checkMsg.append("\n ||投标报价校验 - 不通过;");
            resultMap.put("tbbj","0");
        }

        Map<String,String> cldMsg = customizeLogicalDecision(project, pUitemList, docEntInfo, resultMap);
        String customizeDocNotice = "";
        if (StringUtils.isNotEmpty(cldMsg.get(ResponseDocEnum.RES_DOC_DECISION.getCode()))) {
            customizeDocNotice = cldMsg.get(ResponseDocEnum.RES_DOC_DECISION.getCode());
        }
        String resDocNotice = replaceStr(checkMsg + customizeDocNotice);
        resultMap.put(ResponseDocEnum.RES_DOC_DECISION.getCode(), resDocNotice);
        return resultMap;
    }


    /**
     * 校验证书名称是否一致
     * @param projectCertificate 项目配置的证书名称（传入格式： 证书1;证书2;证书3）
     * @param zzzmsDetail 响应文件中配置的所有证书详情
     * @return 通过返回 true
     */
    public static boolean certNameDecision(String projectCertificate, List<DocResponseEntDetail> zzzmsDetail) {
        String[] pcArr = projectCertificate.split(";");
        if (pcArr==null || pcArr.length==0) {
            throw new ServiceException("系统校验：项目中未设定资格要求证书名称");
//            checkMsg.append("\n ||项目中未设定资格要求证书名称");
        }
        List<String> proCertList = Arrays.asList(pcArr);
        // 获取响应文件中的资格证书名称
        List<String> zzzmList = new ArrayList<String>();

        zzzmsDetail.forEach(ele -> zzzmList.add(ele.getDetailName()));
        Collections.sort(proCertList);
        Collections.sort(zzzmList);
        if (proCertList.equals(zzzmList)) { // 证书名字不同，判定不通过
            return true;
        } else {
            return false;
        }
    }

    /**
     * 校验工期
     * @param proOverTimeLimit 采购文件要求工期
     * @param resOverTimeLimit 响应文件工期
     * @return 通过返回 true
     */
    public static boolean durationDecision(String proOverTimeLimit, String resOverTimeLimit) {
        if (StringUtils.isEmpty(proOverTimeLimit) || !DetailContent.isInteger(proOverTimeLimit)) {
            throw new ServiceException("系统校验：(采购文件)工期/供货期不能为空且必须为数字");
        }
        if (StringUtils.isEmpty(resOverTimeLimit) || !DetailContent.isInteger(resOverTimeLimit)) {
            throw new ServiceException("系统校验：(响应文件)工期/供货期不能为空且必须为数字");
        }
        Integer proGhq = Integer.parseInt(proOverTimeLimit);
        Integer resGhq = Integer.parseInt(resOverTimeLimit);
        if (resGhq>proGhq) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 校验投标报价
     * @param proBidPrice 采购文件要求报价
     * @param resBidPrice 响应文件报价
     * @return 通过返回 true
     */
    public static boolean tbPriceDecision(String proBidPrice, String resBidPrice) {
        if (StringUtils.isEmpty(proBidPrice) || !DetailContent.isDouble(proBidPrice)) {
            throw new ServiceException("系统校验：(采购文件)投标报价不能为空且必须为数字");
        }
        if (StringUtils.isEmpty(resBidPrice) || !DetailContent.isDouble(resBidPrice)) {
            throw new ServiceException("系统校验：(响应文件)投标报价不能为空且必须为数字");
        }
        double proTbjg = Double.parseDouble(proBidPrice);
        double resTbjg = Double.parseDouble(resBidPrice);
        if (resTbjg>proTbjg) {
            return false;
        } else {
            return true;
        }
    }

    public static String replaceStr(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        StringBuilder output = new StringBuilder();
        int index = 1;
        int start = 0;
        while (true) {
            int pos = input.indexOf("||", start);
            if (pos == -1) {
                output.append(input.substring(start));
                break;
            }
            output.append(input, start, pos);
            output.append("提示").append(index).append("：");
            start = pos + 2;
            index++;
        }
        return output.toString();
    }

    public ProcurementDocumentVo prepareCommonTemplateDocData(JSONObject data, BaseEntInfo ent, DocResponseEntInfo info) {
        ProcurementDocumentVo vo = new ProcurementDocumentVo();
        vo.setEntId(SecurityUtils.getLoginUser().getEntId());
        vo.setBidderName(ent.getEntName());
        vo.setProjectId(info.getProjectId());
        Map<String, List<DocResponseEntDetail>> detailMap = info.getDetailMap();
//        BusiTenderProject project = busiTenderProjectService.getById(info.getProjectId());

        // 配置开标一览表数据
        DetailContent.setKbylb(vo, detailMap);
        // 配置明细报价表数据
//        if (project.getTenderMode().equals("3")) {
//            DetailContent.setMxbjb(vo, detailMap);
//        }
//        DetailContent.setMxbjb(vo, detailMap);
        // 配置法定代表人身份证明或授权书
        DetailContent.setFddbrOrSqs(data, vo, detailMap);
        // 配置资格审查及评审资料
//        DetailContent.setZgsc(data, vo, detailMap);
        // 配置营业执照
        DetailContent.setYyzz(vo, detailMap);
        // 配置资质证明
        DetailContent.setZzzm(vo, detailMap);

        // 技术部分
//        DetailContent.setJsbf(vo, detailMap);
        // 商务部分
//        DetailContent.setSwbf(vo, detailMap);
        // 其他资料
        DetailContent.setQtzl(vo, detailMap);

        // 准备自定义模板文档数据
        ProcurementDocumentVo pvo = prepareCustomizeTemplateDocData(vo, data, ent, info);
        return pvo;
    }



    private String generateDoc (ProcurementDocumentVo vo, DocResponseEntInfo docInfo, BusiTenderProject project, BaseEntInfo entInfo) throws Exception {
        if (StringUtils.isEmpty(vo.getTemplateName())) {
            throw new ServiceException("响应文件模板未设置");
        }
        String pdfPath = procurementDocumentsUinfoService.prepareResponseDocuments1(vo, project, entInfo);
        String url = AttachmentUtil.realToUrl(pdfPath);
        docInfo.setPdfPath(url);
        docInfo.setBasePdfPath(url);
        docInfo.setRemark(PdfUtil.getPageNum(pdfPath, ScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
        docResponseEntInfoService.updateById(docInfo);
        return url;
    }

    public static void main(String[] args) {
        List<Integer> listA = new ArrayList<>();
        listA.add(1);
        listA.add(2);
        listA.add(3);

        List<Integer> listB = new ArrayList<>();
        listB.add(3);
        listB.add(2);
        listB.add(1);
//        Collections.sort(listA);
//        Collections.sort(listB);
        boolean isSame = listA.equals(listB);
        System.out.println("两个 List 的内容是否完全相同: " + isSame);
    }



}
