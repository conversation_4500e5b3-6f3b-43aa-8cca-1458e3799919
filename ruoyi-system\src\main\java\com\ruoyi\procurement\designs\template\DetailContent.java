package com.ruoyi.procurement.designs.template;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.enums.ResponseDocType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.ExcelToPdfConverter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Log4j2
public class DetailContent {


    public static void setKbylb(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        JSONObject kbylb = JSONObject.parseObject(detailMap.get(ResponseDocEnum.KBYLB.getCode()).get(0).getDetailContent());
        vo.setBidAmountCapitalization(kbylb.getString("tbbjdx"));
        vo.setBidAmount(kbylb.getInteger("bidPrice"));
        vo.setOverTimeLimit(kbylb.getInteger("overTimeLimit"));
        vo.setWarrantyPeriod(kbylb.getInteger("warrantyPeriod"));
        vo.setQualityDemand(kbylb.getString("qualityDemand"));
        vo.setBidValidPeriod(kbylb.getString("tbyxq"));
    }

    public static void setFddbrOrSqs(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if(data.getString("frorsqs").equals("fr")){
            if (detailMap.get("fddbrsfzm") == null || detailMap.get("fddbrsfzm").isEmpty() ||
                    detailMap.get("fddbrsfzm").get(0).getFilePath()==null || detailMap.get("fddbrsfzm").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            //2 法定代表人身份证明   {"fddbrxm":"4","fddbrxb":"3","fddbrnl":"2","fddbrzw":"1","id":1200227728098309}
            DocResponseEntDetail fddbrsfzmDetail = detailMap.get("fddbrsfzm").get(0);
            JSONObject fddbrsfzm = JSONObject.parseObject(fddbrsfzmDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsfzm.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsfzm.getString("fddbrxb"));
            vo.setLegalPersonAge(fddbrsfzm.getInteger("fddbrnl"));
            vo.setLegalPersonPosition(fddbrsfzm.getString("fddbrzw"));
//            setAtts(vo, fddbrsfzmDetail.getFilePath(), "FDDBRJZSFZ");
            vo.getPdfFiles().put("附：法定代表人身份证复印件（正反面）", fddbrsfzmDetail.getFilePath());
            vo.setFddbrsfzm(true);
        }else{
            if (detailMap.get("fddbrsqs") == null || detailMap.get("fddbrsqs").isEmpty() ||
                    detailMap.get("fddbrsqs").get(0).getFilePath()==null || detailMap.get("fddbrsqs").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            //3 法定代表人授权书   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            DocResponseEntDetail fddbrsqsDetail = detailMap.get("fddbrsqs").get(0);
            JSONObject fddbrsqs = JSONObject.parseObject(fddbrsqsDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsqs.getString("fddbrxm"));
            vo.setEntrustPersonName(fddbrsqs.getString("wtrxm"));
            vo.setEntrustDeadline(fddbrsqs.getString("wtqx"));
//            setAtts(vo, fddbrsqsDetail.getFilePath(), "FDDBRSQS");
            vo.getPdfFiles().put("附：法定代表人和授权代表身份证复印件(正反面)", fddbrsqsDetail.getFilePath());
            vo.setSqwts(true);
        }
    }

    // 设置资格审查文件
    public static void setZgsc(JSONObject data, ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if(data.getString("tsqylx").equals("zxqy")){
            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            StringBuffer zxqysmhxx = new StringBuffer();
            for (int i=0; i<zxqysmhArry.size(); i++) {
                JSONObject obj = zxqysmhArry.getJSONObject(i);
                zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
            };
            vo.setZxqysmhxx(zxqysmhxx.toString());
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            vo.setJyqy(true);
            vo.addPdfFile("：监狱企业", jyqyDetail.getFilePath());
        }
    }

    private void generateGoodsZxqysmhxx (ProcurementDocumentVo vo, JSONArray zxqysmhArry) {
        StringBuffer zxqysmhxx = new StringBuffer();
        for (int i=0; i<zxqysmhArry.size(); i++) {
            JSONObject obj = zxqysmhArry.getJSONObject(i);
            zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
        };
        vo.setZxqysmhxx(zxqysmhxx.toString());
        vo.setZxqysmh(true);
    }

    private void generateOtherZxqysmhxx (ProcurementDocumentVo vo, JSONArray zxqysmhArry) {
        JSONObject zxqysmh = zxqysmhArry.getJSONObject(0);
        vo.setTrade(zxqysmh.getString("sshy"));
        vo.setPractitionerNum(zxqysmh.getString("cyryrs"));
        vo.setOperatingRevenue(zxqysmh.getBigDecimal("yysr"));
        vo.setTotalAssets(zxqysmh.getBigDecimal("zcze"));
        vo.setCompanyType(zxqysmh.getString("qylx"));
        vo.setZxqysmh(true);
    }

    public static void setYyzz(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        DocResponseEntDetail yyzzDetail = detailMap.get("yyzz").get(0);
        vo.addPdfFile("）营业执照", yyzzDetail.getFilePath());
    }

    public static void setZzzm(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        List<DocResponseEntDetail> zzzms = detailMap.get("zzzm");
        if (zzzms!=null && zzzms.size()>0) {
            for (DocResponseEntDetail zzzm : zzzms) {
                vo.addPdfFile("）资质证明", zzzm.getFilePath());
            }
        }
    }

    public static void setJsbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        DocResponseEntDetail jsplbDetail = detailMap.get("jsplb").get(0);
//        vo.getContentMap().put("jsplb", removeTags(jsplbDetail.getDetailContent()));
        String jsplbFilePath = jsplbDetail.getFilePath();
        // 判断是否有多个文件
        if (StringUtils.isNotEmpty(jsplbFilePath)) {
            String[] jsplbArr = jsplbFilePath.split(",");
            for (String filePath : jsplbArr) {
                String jsplbPath = AttachmentUtil.urlToReal(filePath);
                String jsplbPdfPath = RuoYiConfig.getUploadPath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".pdf";
                if (filePath.contains("xlsx")) { // 是xlsx文件，需要转pdf
                    try {
                        ExcelToPdfConverter.convertExcelToPdf(jsplbPath, jsplbPdfPath);
                    } catch (Exception e) {
                        log.error("excel文件转pdf文件异常", e.getMessage());
                        e.printStackTrace();
                        throw new ServiceException("excel文件转pdf文件异常");
                    }
                    vo.getPdfFiles().put("、技术偏离表", jsplbPdfPath);
                } else {
                    vo.getPdfFiles().put("、技术偏离表", jsplbPath);
                }
            }
        }
    }

    public static void setSwbf(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {

    }

    public static void setQtzl(ProcurementDocumentVo vo, Map<String, List<DocResponseEntDetail>> detailMap) {
        if (detailMap.get("tbrrwxytgdqtzl") != null) {
            DocResponseEntDetail lsyjDetail = detailMap.get("tbrrwxytgdqtzl").get(0);
            vo.getContentMap().put("tbrrwxytgdqtzl", lsyjDetail.getDetailContent());
//            setAtts(vo, lsyjDetail.getFilePath(), "TBRRWXYTGDQTZL");
            vo.getPdfFiles().put("、其他资料", lsyjDetail.getFilePath());
        }
    }


    public static boolean isEmpty(Map<String, List<DocResponseEntDetail>> detailMap, String code) {
        if (detailMap.get(code)==null || detailMap.get(code).isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isFileEmpty(Map<String, List<DocResponseEntDetail>> detailMap, String code) {
        if (detailMap.get(code).get(0).getFilePath()==null || detailMap.get(code).get(0).getFilePath().isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isInteger(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isDouble(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }



}
