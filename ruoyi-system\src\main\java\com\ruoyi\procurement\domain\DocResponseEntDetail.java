package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 企业响应文件基础条目 doc_response_item
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("响应文件基础条目")
@TableName(resultMap = "com.ruoyi.procurement.mapper.DocResponseEntDetailMapper.DocResponseEntDetailResult")
public class DocResponseEntDetail extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业响应文件明细id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 基础条目id
     */
    private Long docResponseItemId;
    /**
     * 基础条目编号
     */
    private String docResponseItemCode;
    /**
     * 企业响应文件id
     */
    private Long docResponseEntId;
    /**
     * 明细编号
     */
    private String detailCode;
    /**
     * 明细名称
     */
    private String detailName;
    /**
     * 明细内容
     */
    private String detailContent;
    /**
     * 明细类型(用于区分项目类型：0-工程，1-服务，2-货物)
     */
    private String detailType;
    /**
     * 评分办法id
     */
    private Long entMethodItemId;
    /**
     * 明细排序
     */
    private Integer detailSort;
    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
//    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private Map<String, String> detailMap;
}
