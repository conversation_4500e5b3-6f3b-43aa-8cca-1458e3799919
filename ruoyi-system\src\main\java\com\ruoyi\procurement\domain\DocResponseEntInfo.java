package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 响应文件基础条目 doc_response_item
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("响应文件基础条目")
@TableName(resultMap = "com.ruoyi.procurement.mapper.DocResponseEntInfoMapper.DocResponseEntInfoResult")
public class DocResponseEntInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业响应文件明细id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 基础信息id
     */
    private Long docResponseId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 文件路径
     */
    @ApiModelProperty("文件路径")
    @Excel(name = "文件路径")
    private String filePath;
    /**
     * 文件路径
     */
    @ApiModelProperty("pdf文件路径")
    @Excel(name = "pdf文件路径")
    private String pdfPath;
    //原始pdf地址，用于签章回滚用
    private String basePdfPath;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    @JsonFormat(pattern = "yyyy年MM月dd日 HH时mm分")
    @TableField(exist = false)
    private Date bidOpeningTime;
    @TableField(exist = false)
    private String projectName;
    @TableField(exist = false)
    private String projectCode;
    @TableField(exist = false)
    private String projectType;
    @TableField(exist = false)
    private String bidOpeningPlace;
    @TableField(exist = false)
    private String tenderMode;
    @TableField(exist = false)
    private Map<String, List<DocResponseEntDetail>> detailMap;
    @TableField(exist = false)
    private List<DocResponseEntDetail> businessPart;
    @TableField(exist = false)
    private String entLegalPerson;
    @TableField(exist = false)
    private Date startTime;
    @TableField(exist = false)
    private Date endTime;
}
