package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 响应文件基础信息 doc_response_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("响应文件基础信息")
@TableName(resultMap = "com.ruoyi.procurement.mapper.DocResponseInfoMapper.DocResponseInfoResult")
public class DocResponseInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 采购文件id
     */
    @ApiModelProperty("响应文件基础信息id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 采购方式（字典）
     */
    @ApiModelProperty("采购方式（字典）")
    @Excel(name = "采购方式", readConverterExp = "字典")
    private String tenderMode;
    /**
     * 项目类别（字典）
     */
    @ApiModelProperty("项目类别（字典）")
    @Excel(name = "项目类别", readConverterExp = "字典")
    private String projectType;

    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    @TableField(exist = false)
    private List<DocResponseItem> docResponseItemList;

}
