package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 响应文件基础条目 doc_response_item
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("响应文件基础条目")
@TableName(resultMap = "com.ruoyi.procurement.mapper.DocResponseItemMapper.DocResponseItemResult")
public class DocResponseItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应文件基础条目id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 上级条目id
     */
    private Long pId;
    /**
     * 响应文件基础信息id
     */
    private Long docResponseId;
    /**
     * 条目编号
     */
    private String itemCode;
    /**
     * 条目名称
     */
    private String itemName;
    /**
     * 条目内容
     */
    private String itemContent;
    /**
     * 条目类型
     */
    private String itemType;
    /**
     * 条目排序
     */
    private Integer itemSort;
    /**
     * 条目级别
     */
    private Integer itemLevel;
    /**
     * 是否叶子节点
     */
    private Integer isLeaf;

    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private List<DocResponseItem> children;

    public void putChild(DocResponseItem item){
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(item);
    }
}
