package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购/响应文件模板对象 procurement_documents_file_temp
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
@ApiModel("采购/响应文件模板对象")
@TableName(resultMap = "com.ruoyi.procurement.mapper.ProcurementDocumentsFileTempMapper.ProcurementDocumentsFileTempResult")
public class ProcurementDocumentsFileTemp extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 文件模板id
     */
    @ApiModelProperty("文件模板id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long documentsFileTempId;
    /**
     * 采购方式
     */
    @ApiModelProperty("采购方式")
            @Excel(name = "采购方式")
    private Integer tenderMode;
    /**
     * 文件网络地址
     */
    @ApiModelProperty("文件网络地址")
            @Excel(name = "文件网络地址")
    private String fileUrl;
    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
            @Excel(name = "项目类型")
    private Integer projectType;
    /**
     * 0采购文件1响应文件
     */
    @ApiModelProperty("0采购文件1响应文件")
            @Excel(name = "0采购文件1响应文件")
    private Integer type;
    /**
     * 文件物理路径
     */
    @ApiModelProperty("文件物理路径")
            @Excel(name = "文件物理路径")
    private String dirPath;

        }
