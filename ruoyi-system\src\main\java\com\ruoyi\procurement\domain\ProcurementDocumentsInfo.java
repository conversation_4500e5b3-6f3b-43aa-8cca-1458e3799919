package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;
import java.util.List;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购文件编制基础信息对象 procurement_documents_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("采购文件编制基础信息对象")
@TableName(resultMap = "com.ruoyi.procurement.mapper.ProcurementDocumentsInfoMapper.ProcurementDocumentsInfoResult")
public class ProcurementDocumentsInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 采购文件id
     */
    @ApiModelProperty("采购文件id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long projectFileId;
    /**
     * 采购方式（字典）
     */
    @ApiModelProperty("采购方式（字典）")
    @Excel(name = "采购方式", readConverterExp = "字=典")
    private Integer tenderMode;
    /**
     * 项目类别（字典）
     */
    @ApiModelProperty("项目类别（字典）")
    @Excel(name = "项目类别", readConverterExp = "字=典")
    private Integer projectType;

    /**
     * 子项
     */
    @TableField(exist = false)
    private List<ProcurementDocumentsItem> items;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    @ApiModelProperty("文件类型（1采购文件 2响应文件")
    private Integer fileType;

    @TableField(exist = false)
    private ProcurementDocumentsUinfo uInfo;
}
