package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购文件编制详细信息对象 procurement_documents_item
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("采购文件编制详细信息对象")
@TableName(resultMap = "com.ruoyi.procurement.mapper.ProcurementDocumentsItemMapper.ProcurementDocumentsItemResult")
public class ProcurementDocumentsItem extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 采购文件详情id
     */
    @ApiModelProperty("采购文件详情id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long projectFileItemId;
    /**
     * 采购文件id
     */
    @ApiModelProperty("采购文件id")
            @Excel(name = "采购文件id")
    private Long projectFileId;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
            @Excel(name = "项目名称")
    private String itemName;
    /**
     * 详情编号
     */
    @ApiModelProperty("详情编号")
            @Excel(name = "详情编号")
    private String itemCode;
    /**
     * 详情内容（json格式）
     */
    @ApiModelProperty("详情内容（json格式）")
            @Excel(name = "详情内容", readConverterExp = "j=son格式")
    private String itemContent;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

            @ApiModelProperty("详情顺序")
            private Integer itemSort;
        }
