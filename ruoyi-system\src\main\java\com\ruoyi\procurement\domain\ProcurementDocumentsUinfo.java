package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户编制采购文件信息保存对象 procurement_documents_uinfo
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("用户编制采购文件信息保存对象")
@TableName(resultMap = "com.ruoyi.procurement.mapper.ProcurementDocumentsUinfoMapper.ProcurementDocumentsUinfoResult")
public class ProcurementDocumentsUinfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户采购文件id
     */
    @ApiModelProperty("用户采购文件id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entFileId;
    /**
     * 选用采购文件id
     */
    @ApiModelProperty("选用采购文件id")
    @Excel(name = "选用采购文件id")
    private Long projectFileId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 文件路径
     */
    @ApiModelProperty("文件路径")
    @Excel(name = "文件路径")
    private String filePath;
    /**
     * 文件路径
     */
    @ApiModelProperty("pdf文件路径")
    @Excel(name = "pdf文件路径")
    private String pdfPath;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    @JsonProperty("uItems")
    private List<ProcurementDocumentsUitem> uItems;

    //0工程，1货物，2服务
    @TableField(exist = false)
    private Integer type;

}
