package com.ruoyi.procurement.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户编制采购文件信息保存对象 procurement_documents_uitem
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("用户编制采购文件信息保存对象")
@TableName(resultMap = "com.ruoyi.procurement.mapper.ProcurementDocumentsUitemMapper.ProcurementDocumentsUitemResult")
public class ProcurementDocumentsUitem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户采购文件id
     */
    @ApiModelProperty("用户采购文件id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entFileItemId;

    /**
     * 选用采购文件id
     */
    @ApiModelProperty("选用暂存的infoId")
    @Excel(name = "选用暂存的infoId")
    private Long entFileId;

    /**
     * 选用采购文件id
     */
    @ApiModelProperty("选用采购文件id")
    @Excel(name = "选用采购文件id")
    private Long projectFileId;
    /**
     * 选用采购文件id
     */
    @ApiModelProperty("选用采购文件itmeid")
    @Excel(name = "选用采购文件itmeid")
    private Long projectFileItemId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 文件路径
     */
    @ApiModelProperty("文件内容")
    @Excel(name = "文件内容")
    private String itemContent;

    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String itemName;

    /**
     * 文件路径
     */
    @ApiModelProperty("文件路径")
    @Excel(name = "文件路径")
    private String filePath;
    /**
     * 项目评分办法细则id
     */
    @ApiModelProperty("项目评分办法细则id")
    @Excel(name = "项目评分办法细则id")
    private String entMethodItemId;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
