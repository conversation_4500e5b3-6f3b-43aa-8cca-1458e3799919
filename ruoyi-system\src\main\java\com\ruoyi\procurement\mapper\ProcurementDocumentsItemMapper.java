package com.ruoyi.procurement.mapper;

import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import org.apache.ibatis.annotations.Param;

/**
 * 采购文件编制详细信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Mapper
public interface ProcurementDocumentsItemMapper extends BaseMapper<ProcurementDocumentsItem> {

    ProcurementDocumentsItem selectItemByInfoAndCode(@Param("tenderMode")Integer tenderMode, @Param("projectType")Integer projectType, @Param("code")String code);
}