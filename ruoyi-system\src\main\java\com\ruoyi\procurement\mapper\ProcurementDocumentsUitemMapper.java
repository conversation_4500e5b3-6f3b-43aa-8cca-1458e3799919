package com.ruoyi.procurement.mapper;

import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户编制采购文件信息保存Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Mapper
public interface ProcurementDocumentsUitemMapper extends BaseMapper<ProcurementDocumentsUitem> {

    ProcurementDocumentsUitem selectByProject(@Param("projectId")Long projectId, @Param("projectFileItemId")Long projectFileItemId);

    List<ProcurementDocumentsUitem> selectByProjectId(@Param("projectId")Long projectId);
}