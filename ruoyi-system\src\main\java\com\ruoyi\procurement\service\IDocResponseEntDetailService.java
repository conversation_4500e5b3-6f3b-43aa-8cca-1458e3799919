package com.ruoyi.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.scoring.domain.ScoringMethodInfo;

import java.util.List;
import java.util.Map;

/**
 * 用户编制响应文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDocResponseEntDetailService extends IService<DocResponseEntDetail> {

    List<DocResponseEntDetail> selectList(DocResponseEntDetail info);
    List<DocResponseEntDetail> selectTree(DocResponseEntDetail info);

    Map<String, List<DocResponseEntDetail>> selectMap(DocResponseEntDetail info);

    List<DocResponseEntDetail> selectBusinceeMap(ScoringMethodInfo scoringMethodInfo);

    DocResponseEntDetail selectById(Long id);

    DocResponseEntDetail saveDetail(DocResponseEntDetail detail);

    void deleteById(Long id);

    String removeZxqysmhDetail(Long detailId, String detailQyId);
}

