package com.ruoyi.procurement.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.vo.BidInformation;
import com.ruoyi.procurement.vo.BidOpenIngVo;

import java.util.List;
import java.util.Map;

/**
 * 用户编制响应文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDocResponseEntInfoService extends IService<DocResponseEntInfo> {

    List<BidOpenIngVo> getProcurementInfo(Long id);

    List<DocResponseEntInfo> entList(DocResponseEntInfo info);
    DocResponseEntInfo entInfo(Long projectId);

    List<DocResponseEntInfo> selectList(DocResponseEntInfo info);

    DocResponseEntInfo selectById(Long id);
    DocResponseEntInfo selectByProjectAndBidder(Long projectId, Long bidderId);

    Long saveInfo(DocResponseEntInfo info) throws Exception;

    AjaxResult createDocResponse(JSONObject data) throws Exception;

    AjaxResult createGoodsDocResponse(JSONObject data) throws Exception;

    AjaxResult createServiceDocResponse(JSONObject data) throws Exception;

    AjaxResult createSecurityDocResponse(Long id) throws Exception;

    AjaxResult createInquiryGoodsDocResponse(JSONObject data) throws Exception;

    /**
     * 根据项目获取所有供应商的响应文件页码
     * @param projectId
     * @return
     */
    JSONObject getPesponseDocPageByProject(Long projectId);

    List<DocResponseEntInfo> selectByProject(Long projectId, boolean haveDetail);

    AjaxResult createDocResponse1(JSONObject data) throws Exception;

    AjaxResult resDocReviewFactorsDecision(JSONObject data) throws Exception;
}

