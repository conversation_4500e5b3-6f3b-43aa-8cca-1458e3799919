package com.ruoyi.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.procurement.domain.DocResponseInfo;

import java.util.List;

/**
 * 用户编制采购文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDocResponseInfoService extends IService<DocResponseInfo> {

    public List<DocResponseInfo> selectList(DocResponseInfo info);

    DocResponseInfo getByProject(Long projectId) throws Exception;
    DocResponseInfo getByProject(Long projectId, boolean haveItems) throws Exception;

    DocResponseInfo selectById(Long infoId);
}

