package com.ruoyi.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.procurement.domain.DocResponseItem;

import java.util.List;
import java.util.Map;

/**
 * 用户编制采购文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDocResponseItemService extends IService<DocResponseItem> {

    public List<DocResponseItem> selectList(DocResponseItem info);

    List<DocResponseItem> selectTree(DocResponseItem info);

    List<DocResponseItem> selectTree(Long infoId);

    Map<String, Long> getItemMap(Long infoId);
}

