package com.ruoyi.procurement.service;

import java.util.List;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 采购文件编制基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IProcurementDocumentsInfoService extends IService<ProcurementDocumentsInfo> {
    /**
     * 查询采购文件编制基础信息列表
     *
     * @param procurementDocumentsInfo 采购文件编制基础信息
     * @return 采购文件编制基础信息集合
     */
    public List<ProcurementDocumentsInfo> selectList(ProcurementDocumentsInfo procurementDocumentsInfo);

    ProcurementDocumentsInfo infoByParams(ProcurementDocumentsInfo procurementDocumentsInfo);
}