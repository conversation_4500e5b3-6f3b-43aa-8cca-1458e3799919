package com.ruoyi.procurement.service;

import java.util.List;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 采购文件编制详细信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IProcurementDocumentsItemService extends IService<ProcurementDocumentsItem> {
    /**
     * 查询采购文件编制详细信息列表
     *
     * @param procurementDocumentsItem 采购文件编制详细信息
     * @return 采购文件编制详细信息集合
     */
    public List<ProcurementDocumentsItem> selectList(ProcurementDocumentsItem procurementDocumentsItem);

    List<ProcurementDocumentsItem> listByInfoId(Long projectFileId);
}