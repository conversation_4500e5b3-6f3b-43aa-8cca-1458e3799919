package com.ruoyi.procurement.service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.procurement.vo.CheckIsOverVo;
import com.ruoyi.procurement.vo.CheckisOverExpVo;
import com.ruoyi.procurement.vo.GenProjectResponseVo;
import com.ruoyi.procurement.vo.ProcurementDocumentVo;

/**
 * 用户编制采购文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IProcurementDocumentsUinfoService extends IService<ProcurementDocumentsUinfo> {
    /**
     * 查询用户编制采购文件信息保存列表
     *
     * @param procurementDocumentsUinfo 用户编制采购文件信息保存
     * @return 用户编制采购文件信息保存集合
     */
    public List<ProcurementDocumentsUinfo> selectList(ProcurementDocumentsUinfo procurementDocumentsUinfo);

    ProcurementDocumentsUinfo saveInfo(ProcurementDocumentsUinfo procurementDocumentsUinfo);

    ProcurementDocumentsUinfo getInfoById(Long entFileId);

    ProcurementDocumentsUinfo infoByParams(ProcurementDocumentsUinfo procurementDocumentsUinfo);

    CheckisOverExpVo checkIsOver(ProcurementDocumentsUinfo procurementDocumentsUinfo);

    AjaxResult generateProjectFileZip(ProcurementDocumentsUinfo queryUinfo) throws IOException;

    String generateResponseZip(GenProjectResponseVo genProjectResponseVo) throws Exception;
    //工程采购文件
    AjaxResult generateProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException;
    //货物
    AjaxResult generateGoodsProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException;

    AjaxResult generateTenderModeGoodsProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException;
    //货物
    AjaxResult generateServiceProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException;

    String prepareResponseDocuments(ProcurementDocumentVo procurementDocumentVo);

    String prepareGoodsResponseDocuments(ProcurementDocumentVo procurementDocumentVo);

    String prepareServiceResponseDocuments(ProcurementDocumentVo procurementDocumentVo);

    ProcurementDocumentsUinfo selectByProject(Long projectId);

    String prepareInquiryGoodsResponseDocuments(ProcurementDocumentVo procurementDocumentVo);

    String prepareResponseDocuments1(ProcurementDocumentVo vo, BusiTenderProject project, BaseEntInfo entInfo);
}

