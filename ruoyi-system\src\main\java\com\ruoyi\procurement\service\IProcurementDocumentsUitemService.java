package com.ruoyi.procurement.service;

import java.io.Serializable;
import java.util.List;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户编制采购文件信息保存Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IProcurementDocumentsUitemService extends IService<ProcurementDocumentsUitem> {
    /**
     * 查询用户编制采购文件信息保存列表
     *
     * @param procurementDocumentsUitem 用户编制采购文件信息保存
     * @return 用户编制采购文件信息保存集合
     */
    public List<ProcurementDocumentsUitem> selectList(ProcurementDocumentsUitem procurementDocumentsUitem);

    List<ProcurementDocumentsUitem> listByInfoId(Long entFileId);
}