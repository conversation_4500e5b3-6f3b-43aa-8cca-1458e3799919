package com.ruoyi.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.ProcurementDocumentsType;
import com.ruoyi.common.enums.ProjectType;
import com.ruoyi.common.enums.ScoreMethodType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.domain.DocResponseEntDetail;
import com.ruoyi.procurement.domain.DocResponseEntInfo;
import com.ruoyi.procurement.mapper.DocResponseEntDetailMapper;
import com.ruoyi.procurement.service.IDocResponseEntDetailService;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.mapper.ScoringMethodUitemMapper;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.IntStream;

//import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
//import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class DocResponseEntDetailServiceImpl extends ServiceImpl<DocResponseEntDetailMapper, DocResponseEntDetail> implements IDocResponseEntDetailService {

    @Autowired
    private ScoringMethodUitemMapper scoringMethodUitemMapper;
    @Autowired
    private DocResponseEntDetailMapper docResponseEntDetailMapper;

    @Override
    public List<DocResponseEntDetail> selectList(DocResponseEntDetail info) {
        QueryWrapper<DocResponseEntDetail> queryWrapper = getInfoQueryWrapper(info);
        return list(queryWrapper);
    }

    @Override
    public List<DocResponseEntDetail> selectTree(DocResponseEntDetail info) {
        return null;
    }

    @Override
    public Map<String, List<DocResponseEntDetail>> selectMap(DocResponseEntDetail info) {
        List<DocResponseEntDetail> detailList = selectList(info);
        Map<String, List<DocResponseEntDetail>> detailMap = new HashMap<>();
        for(DocResponseEntDetail detail : detailList){
            if (!detailMap.containsKey(detail.getDocResponseItemCode())) {
                detailMap.put(detail.getDocResponseItemCode(), new ArrayList<>());
            }
            detailMap.get(detail.getDocResponseItemCode()).add(detail);
        }
        return detailMap;
    }

    @Override
    public List<DocResponseEntDetail> selectBusinceeMap(ScoringMethodInfo scoringMethodInfo) {
        scoringMethodInfo.getUInfo().setItemCode(ScoreMethodType.ITEM_CODE_SWBPS.getCode());
        scoringMethodInfo.getUInfo().setScoringMethodId(scoringMethodInfo.getScoringMethodId());
        List<ScoringMethodUitem> uitemList = scoringMethodUitemMapper.selectBusinessUitemIds(scoringMethodInfo.getUInfo());

        List<DocResponseEntDetail> businessPart = new ArrayList<>();
        for(ScoringMethodUitem uitem : uitemList){
//            Map<String, String> businessMap = new HashMap<>();
            DocResponseEntDetail detail = new DocResponseEntDetail();
            detail.setDetailName(uitem.getItemName());
            detail.setDetailCode(uitem.getEntMethodItemId().toString());
            detail.setEntMethodItemId(uitem.getEntMethodItemId());
            businessPart.add(detail);
        }
        return businessPart;
    }

    @Override
    public DocResponseEntDetail selectById(Long id) {
        DocResponseEntDetail detail = getById(id);
        return detail;
    }

    @Override
    public DocResponseEntDetail saveDetail(DocResponseEntDetail detail) {
        // 中小企业声明函
        String qyId = UUID.randomUUID().toString().replaceAll("-","");
        if (ProcurementDocumentsType.ITEM_ZXQYSMH.getCode().equals(detail.getDocResponseItemCode())
            && ProjectType.GOODS.getCode().equals(detail.getDetailType())) {
            DocResponseEntDetail old = docResponseEntDetailMapper.selectById(detail.getId());
            if (detail.getId() == null || old == null) { // 第一次新增企业
                JSONObject newDetailContent = JSONObject.parseObject(detail.getDetailContent());
                newDetailContent.put("qyId",qyId);
                JSONArray array = new JSONArray();
                array.add(newDetailContent);
                detail.setDetailContent(array.toJSONString());
            } else { // 修改
                JSONObject newZxqy = JSONObject.parseObject(detail.getDetailContent());
                String newZxqyQyId = newZxqy.getString("qyId");
                JSONArray oldZxqyArr = JSON.parseArray(old.getDetailContent());
                if (StringUtils.isEmpty(newZxqyQyId)) { // 追加
                    newZxqy.put("qyId",qyId);
                    oldZxqyArr.add(newZxqy);
                } else { // 更新
                    for (int i=0; i<oldZxqyArr.size(); i++) {
                        JSONObject obj = oldZxqyArr.getJSONObject(i);
                        if (newZxqyQyId.equals(obj.getString("qyId"))) { // 替换对应的信息
                            oldZxqyArr.set(i, newZxqy);
                            break;
                        }
                    };
                }
                detail.setDetailContent(oldZxqyArr.toJSONString());
            }
        }

        Map<String, String> detailFileMap = detail.getDetailMap();
        if (detailFileMap == null || detailFileMap.isEmpty()) {
            saveOrUpdate(detail);
        }else{
            for (String key : detailFileMap.keySet()) {
                detail.setDetailName(key);
                detail.setFilePath(detailFileMap.get(key));
                saveOrUpdate(detail);
            }
        }
        return detail;
    }



    @Override
    public void deleteById(Long id) {
//        baseMapper.deleteById()
    }

    private void deleteDetailByItem(DocResponseEntDetail query){
        QueryWrapper<DocResponseEntDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doc_response_ent_id", query.getDocResponseEntId());
        queryWrapper.eq("doc_response_item_id", query.getDocResponseItemId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDetailName()), "detail_name", query.getDetailName());
        remove(queryWrapper);
    }

    private QueryWrapper<DocResponseEntDetail> getInfoQueryWrapper(DocResponseEntDetail query) {
        QueryWrapper<DocResponseEntDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getId()), "id", query.getId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDocResponseEntId()), "doc_response_ent_id", query.getDocResponseEntId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDocResponseItemId()), "doc_response_item_id", query.getDocResponseItemId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDetailCode()), "detail_code", query.getDetailCode());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDetailName()), "detail_name", query.getDetailName());
        queryWrapper.orderByAsc("detail_sort");

        return queryWrapper;
    }

    public String removeZxqysmhDetail(Long detailId, String detailQyId){
        if (detailId==null || StringUtils.isEmpty(detailQyId)) {
            throw new RuntimeException("未获取到参数值");
        }
        DocResponseEntDetail detail = docResponseEntDetailMapper.selectById(detailId);
        JSONArray jsonArray = JSON.parseArray(detail.getDetailContent());
        if (jsonArray.size()==1) { // 只有一条时，直接删除
            this.removeById(detailId);
            return "";
        } else {
            for (int i=0; i<jsonArray.size(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                if (detailQyId.equals(obj.getString("qyId"))) { // 删除对应的信息
                    jsonArray.remove(i);
                    break;
                }
            }
            detail.setDetailContent(jsonArray.toJSONString());
            saveOrUpdate(detail);
            return jsonArray.toJSONString();
        }
    }
}

