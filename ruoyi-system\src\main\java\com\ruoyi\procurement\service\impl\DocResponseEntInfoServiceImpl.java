package com.ruoyi.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.XmlConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.ProcurementDocumentsType;
import com.ruoyi.common.enums.ProjectType;
import com.ruoyi.common.enums.ResponseDocEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.procurement.designs.factory.DocResponseFactory;
import com.ruoyi.procurement.designs.template.AbstractDocResponseStrategy;
import com.ruoyi.procurement.domain.*;
import com.ruoyi.procurement.mapper.DocResponseEntInfoMapper;
import com.ruoyi.procurement.mapper.ProcurementDocumentsItemMapper;
import com.ruoyi.procurement.mapper.ProcurementDocumentsUitemMapper;
import com.ruoyi.procurement.service.*;
import com.ruoyi.procurement.vo.*;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.mapper.ScoringMethodInfoMapper;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.ExcelToPdfConverter;
import com.ruoyi.utils.PdfUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

//import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
//import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Log4j2
@Service
public class DocResponseEntInfoServiceImpl extends ServiceImpl<DocResponseEntInfoMapper, DocResponseEntInfo> implements IDocResponseEntInfoService {

    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IDocResponseInfoService docResponseInfoService;
    @Autowired
    private IDocResponseEntDetailService docResponseEntDetailService;
    @Autowired
    private IDocResponseItemService docResponseItemService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    private ScoringMethodInfoMapper scoringMethodInfoMapper;
    @Autowired
    private IProcurementDocumentsUinfoService procurementDocumentsUinfoService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;
    @Autowired
    private ProcurementDocumentsItemMapper procurementDocumentsItemMapper;
    @Autowired
    private ProcurementDocumentsUitemMapper procurementDocumentsUitemMapper;
    @Autowired
    private IBaseEntInfoService iBaseEntInfoService;
    @Autowired
    DocResponseFactory docResponseFactory;
    @Autowired
    DocResponseEntInfoMapper docResponseEntInfoMapper;

    @Override
    public List<BidOpenIngVo> getProcurementInfo(Long id){
        if (id==null) {
            throw new RuntimeException("必填参数不能为空");
        }
        DocResponseEntInfo dinfo = getById(id);
        if (dinfo==null) {
            throw new RuntimeException("未查询到项目信息");
        }
        BusiTenderProject project = busiTenderProjectService.getById(dinfo.getProjectId());
        // 区分项目类型
        String kbylbCode = "";
        if (ProjectType.PROJECT.getCode().equals(project.getProjectType())) { // 工程
            kbylbCode = ProcurementDocumentsType.ITEM_GCKBYLB.getCode();
        } else {
            kbylbCode = ProcurementDocumentsType.ITEM_KBYLB.getCode();
        }
        ProcurementDocumentsItem pdItem = procurementDocumentsItemMapper.selectItemByInfoAndCode(Integer.parseInt(project.getTenderMode()), Integer.parseInt(project.getProjectType()), kbylbCode);
        ProcurementDocumentsUitem pdUitem = procurementDocumentsUitemMapper.selectByProject(dinfo.getProjectId(), pdItem.getProjectFileItemId());
        return JSON.parseArray(pdUitem.getItemContent(), BidOpenIngVo.class);
    }

    @Override
    public List<DocResponseEntInfo> entList(DocResponseEntInfo info) {
        return baseMapper.entList(info);
    }

    @Override
    public DocResponseEntInfo entInfo(Long projectId) {
        BusiTenderProject project = busiTenderProjectService.getById(projectId);
        BusiTenderNotice notice = busiTenderNoticeService.getTenderNoticeByProjectId(projectId);
        BusiVenueOccupy occupyQuery = new BusiVenueOccupy();
        occupyQuery.setNoticeId(notice.getNoticeId());
        occupyQuery.setVenueType(1);
        List<BusiVenueOccupy> occupyList = busiVenueOccupyService.selectList(occupyQuery);
        DocResponseEntInfo entInfo = new DocResponseEntInfo();
        entInfo.setProjectName(project.getProjectName());
        entInfo.setProjectType(project.getProjectType());
        entInfo.setBidOpeningTime(notice.getBidOpeningTime());
        entInfo.setProjectType(project.getProjectType());
        entInfo.setTenderMode(project.getTenderMode());
        if(notice.getVenueType()==1){
            if(notice.getBidOpeningMode().equals("1")){
                entInfo.setBidOpeningPlace("鹤壁市京东大厦13楼 "+occupyList.get(0).getVenueName());
            }else{
                entInfo.setBidOpeningPlace("鹤壁市政府采购限额以下交易平台远程开标大厅");
            }
        }else {
            entInfo.setBidOpeningPlace(occupyList.get(0).getVenueName());
        }
        return entInfo;
    }

    @Override
    public List<DocResponseEntInfo> selectList(DocResponseEntInfo info) {
        QueryWrapper<DocResponseEntInfo> queryWrapper = getInfoQueryWrapper(info);
        return list(queryWrapper);
    }

    @Override
    public DocResponseEntInfo selectById(Long id) {
        DocResponseEntInfo info = getById(id);
        if (info==null) {
            throw new RuntimeException("未查询到数据");
        }
        // 添加法人信息
        BaseEntInfo entInfo = iBaseEntInfoService.getById(info.getEntId());
        if (entInfo!=null) {
            info.setEntLegalPerson(entInfo.getEntLegalPerson());
        }
        // 判断该项目类型是哪种（工程、服务、货物）
        ScoringMethodInfo scoringMethodInfo = scoringMethodInfoMapper.selectByProjectId(info.getProjectId());
        DocResponseEntDetail query = new DocResponseEntDetail();
        query.setDocResponseEntId(id);
        BusiTenderProject project = busiTenderProjectService.getById(info.getProjectId());
        //project.getTenderMode().equals("0")||project.getTenderMode().equals("1")||project.getTenderMode().equals("4")
       if (project.getTenderMode().equals("1")) {
           // 找出该项目对应的商务部分评审信息
           if (ProjectType.GOODS.getCode().equals(scoringMethodInfo.getProjectType().toString())
                   || ProjectType.SERVICE.getCode().equals(scoringMethodInfo.getProjectType().toString())) {
               List<DocResponseEntDetail> businessPart = docResponseEntDetailService.selectBusinceeMap(scoringMethodInfo);
               info.setBusinessPart(businessPart);
           }
        }
        Map<String, List<DocResponseEntDetail>> map = docResponseEntDetailService.selectMap(query);
        info.setDetailMap(map);
        return info;
    }

    @Override
    public DocResponseEntInfo selectByProjectAndBidder(Long projectId, Long bidderId) {
        DocResponseEntInfo infoQuery = new DocResponseEntInfo();
        infoQuery.setProjectId(projectId);
        infoQuery.setEntId(bidderId);
        List<DocResponseEntInfo> entInfoList = selectList(infoQuery);
        if (entInfoList == null || entInfoList.isEmpty()) {
            return null;
        }
        if (entInfoList.size()>1) {
            throw new RuntimeException("查询信息为多条");
        }
        DocResponseEntInfo entInfo = entInfoList.get(0);
        DocResponseEntDetail query = new DocResponseEntDetail();
        query.setDocResponseEntId(entInfo.getId());
        Map<String, List<DocResponseEntDetail>> map = docResponseEntDetailService.selectMap(query);
        entInfo.setDetailMap(map);
        return entInfo;
    }

    @Override
    public List<DocResponseEntInfo> selectByProject(Long projectId, boolean haveDetail) {
        DocResponseEntInfo infoQuery = new DocResponseEntInfo();
        infoQuery.setProjectId(projectId);
        List<DocResponseEntInfo> entInfoList = selectList(infoQuery);
        if (entInfoList == null || entInfoList.isEmpty()) {
            return new ArrayList<>();
        }
        if(haveDetail) {
            for (DocResponseEntInfo info : entInfoList) {
                DocResponseEntDetail query = new DocResponseEntDetail();
                query.setDocResponseEntId(info.getId());
                Map<String, List<DocResponseEntDetail>> map = docResponseEntDetailService.selectMap(query);
                info.setDetailMap(map);
            }
        }
        return entInfoList;
    }

    @Override
    public Long saveInfo(DocResponseEntInfo info) throws Exception {
        DocResponseEntInfo have = selectByProjectAndBidder(info.getProjectId(), info.getEntId());
        if (have != null) {
            return have.getId();
        }
        DocResponseInfo docResponseInfo = docResponseInfoService.getByProject(info.getProjectId(), false);
        info.setDocResponseId(docResponseInfo.getId());
        baseMapper.insert(info);
        //在保存企业响应文件信息的时候，同步初始化部分详情，用于后续操作方便
        Map<String, List<String>> initDetails = initDetails(info.getProjectId());
        DocResponseItem query = new DocResponseItem();
        query.setDocResponseId(info.getDocResponseId());
        List<DocResponseItem> items = docResponseItemService.selectList(query);
        Map<String, Long> itemIdMap = new HashMap<>();
        for (DocResponseItem item : items) {
            itemIdMap.put(item.getItemCode(), item.getId());
        }
        // 初始化营业执照
        BaseEntInfo entInfo = iBaseEntInfoService.getById(info.getEntId());
        if (entInfo!=null) {
            DocResponseEntDetail yyzzDetail = new DocResponseEntDetail();
            yyzzDetail.setDocResponseItemCode(ResponseDocEnum.YYZZ.getCode());
            yyzzDetail.setDocResponseEntId(info.getId());
            yyzzDetail.setDetailCode(ResponseDocEnum.YYZZ.getCode());
            yyzzDetail.setDetailName(ResponseDocEnum.YYZZ.getInfo());
            yyzzDetail.setDetailType("2");
            yyzzDetail.setDetailSort(1);
            if (StringUtils.isNotEmpty(entInfo.getBusinessLicense()) && entInfo.getBusinessLicense().contains(".pdf")) {
                yyzzDetail.setFilePath(entInfo.getBusinessLicense());
            }
            yyzzDetail.setDelFlag(0);
            docResponseEntDetailService.saveDetail(yyzzDetail);


            DocResponseEntDetail fddbrsfzmDetail = new DocResponseEntDetail();
            fddbrsfzmDetail.setDocResponseItemCode(ResponseDocEnum.FDDBRSFZM.getCode());
            fddbrsfzmDetail.setDocResponseEntId(info.getId());
            fddbrsfzmDetail.setDetailCode(ResponseDocEnum.FDDBRSFZM.getCode());
            fddbrsfzmDetail.setDetailName(ResponseDocEnum.FDDBRSFZM.getInfo());
            fddbrsfzmDetail.setDetailType("2");
            fddbrsfzmDetail.setDetailSort(1);
            JSONObject data = new JSONObject();
            if (StringUtils.isNoneBlank(entInfo.getEntLegalPerson())) {
                data.put("fddbrxm", entInfo.getEntLegalPerson());
            }else{
                data.put("fddbrxm", "");
            }
            if (StringUtils.isNotEmpty(entInfo.getEntLegalPersonCardFile()) && entInfo.getEntLegalPersonCardFile().contains(".pdf")) {
                data.put("filePath", entInfo.getEntLegalPersonCardFile());
                fddbrsfzmDetail.setFilePath(entInfo.getEntLegalPersonCardFile());
            }else{
                data.put("filePath", "");
            }
            data.put("fddbrnl", "");
            data.put("fddbrzw", "");
            fddbrsfzmDetail.setDetailContent(data.toJSONString());
            fddbrsfzmDetail.setDelFlag(0);
            docResponseEntDetailService.saveDetail(fddbrsfzmDetail);
        }
//        for (String key : initDetails.keySet()) {
//            for (int i = 0; i < initDetails.get(key).size();i++) {
//                String itemCode = initDetails.get(key).get(i);
//                DocResponseEntDetail detail = new DocResponseEntDetail();
//                detail.setDocResponseEntId(info.getId());
//                detail.setDocResponseItemCode(key);
//                detail.setDocResponseItemId(itemIdMap.get(key));
//                detail.setDetailName(itemCode);
//                detail.setDetailSort(i+1);
//                detail.setDetailType("1");
//                docResponseEntDetailService.saveDetail(detail);
//            }
//        }

        return info.getId();
    }

    @Override
    public AjaxResult createDocResponse(JSONObject data) throws Exception {
        Long id = data.getLong("infoId");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        BaseEntInfo ent = sysUser.getEnt();
        DocResponseEntInfo info = selectById(id);
        ProcurementDocumentVo vo = new ProcurementDocumentVo();
        vo.setEntId(SecurityUtils.getLoginUser().getEntId());
        vo.setBidderName(ent.getEntName());
        vo.setProjectId(info.getProjectId());
        Map<String, List<DocResponseEntDetail>> detailMap = info.getDetailMap();
        //开始填充数据
        //1 开标一览表   {"tbbjdx":"壹佰元整","tbbjxx":"100","gq":"10","zbq":"20","zlbz":"合格","tbyxq":"90天"}
        if (detailMap.get("kbylb")==null || detailMap.get("kbylb").isEmpty()) {
            throw new ServiceException("开标一览表信息不能为空");
        }
        JSONObject kbylb = JSONObject.parseObject(detailMap.get(ResponseDocEnum.KBYLB.getCode()).get(0).getDetailContent());
        vo.setBidAmountCapitalization(kbylb.getString("tbbjdx"));
        vo.setBidAmount(kbylb.getInteger("bidPrice"));
        vo.setOverTimeLimit(kbylb.getInteger("overTimeLimit"));
        vo.setWarrantyPeriod(kbylb.getInteger("warrantyPeriod"));
        vo.setQualityDemand(kbylb.getString("qualityDemand"));
        vo.setBidValidPeriod(kbylb.getString("tbyxq"));

        // 根据传参中的frorsqs判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("frorsqs").equals("fr")){
            if (detailMap.get("fddbrsfzm") == null || detailMap.get("fddbrsfzm").isEmpty() ||
                    detailMap.get("fddbrsfzm").get(0).getFilePath()==null || detailMap.get("fddbrsfzm").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            //2 法定代表人身份证明   {"fddbrxm":"4","fddbrxb":"3","fddbrnl":"2","fddbrzw":"1","id":1200227728098309}
            DocResponseEntDetail fddbrsfzmDetail = detailMap.get("fddbrsfzm").get(0);
            JSONObject fddbrsfzm = JSONObject.parseObject(fddbrsfzmDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsfzm.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsfzm.getString("fddbrxb"));
            vo.setLegalPersonAge(fddbrsfzm.getInteger("fddbrnl"));
            vo.setLegalPersonPosition(fddbrsfzm.getString("fddbrzw"));
//            setAtts(vo, fddbrsfzmDetail.getFilePath(), "FDDBRJZSFZ");
            vo.getPdfFiles().put("法定代表人身份证复印件（正反面）", fddbrsfzmDetail.getFilePath());
            vo.setFddbrsfzm(true);
        }else{
            if (detailMap.get("fddbrsqs") == null || detailMap.get("fddbrsqs").isEmpty() ||
                   detailMap.get("fddbrsqs").get(0).getFilePath()==null || detailMap.get("fddbrsqs").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            //3 法定代表人授权书   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            DocResponseEntDetail fddbrsqsDetail = detailMap.get("fddbrsqs").get(0);
            JSONObject fddbrsqs = JSONObject.parseObject(fddbrsqsDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsqs.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsqs.getString("wtrxm"));
//            setAtts(vo, fddbrsqsDetail.getFilePath(), "FDDBRSQS");
            vo.getPdfFiles().put("法定代表人和授权代表身份证复印件(正反面)", fddbrsqsDetail.getFilePath());
            vo.setSqwts(true);
        }

        // 资格审查及评审资料
        // 1、落实政府采购政策需满足的资格条件 tsqylx:  zxqy中小企业声明函  cjrflxdw残疾人福利性单位  jyqy监狱企业
        if(data.getString("tsqylx").equals("zxqy")){
            if (detailMap.get("zxqysmh") == null || detailMap.get("zxqysmh").isEmpty()) {
                throw new ServiceException("中小企业声明函信息不能为空");
            }
            // 中小企业声明函   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            JSONObject zxqysmh = JSONObject.parseObject(detailMap.get("zxqysmh").get(0).getDetailContent());
            vo.setTrade(zxqysmh.getString("sshy"));
            vo.setPractitionerNum(zxqysmh.getString("cyryrs"));
            vo.setOperatingRevenue(zxqysmh.getBigDecimal("yysr"));
            vo.setTotalAssets(zxqysmh.getBigDecimal("zcze"));
            vo.setCompanyType(zxqysmh.getString("qylx"));
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            if (detailMap.get("jyqy") == null || detailMap.get("jyqy").isEmpty()) {
                throw new ServiceException("中小企业声明函信息不能为空");
            }
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            setAtts(vo, jyqyDetail.getFilePath(), "JKQY");
            vo.setJyqy(true);
            vo.getPdfFiles().put("：监狱企业", jyqyDetail.getFilePath());
        }
//        if (detailMap.get("lszfcgzcxmzdzgtj") != null) {
//            List<DocResponseEntDetail> lszfcgzcxmzdzgtjDetails = detailMap.get("lszfcgzcxmzdzgtj");
//            for (DocResponseEntDetail detail : lszfcgzcxmzdzgtjDetails) {
//                setAtts(vo, detail.getFilePath(), "QFGZZCGZFQCFTZTJZGDZTJ");
//            }
//        }
        // 特定资格要求
        // 营业执照
        if (detailMap.get("yyzz") == null || detailMap.get("yyzz").isEmpty() ||
                detailMap.get("yyzz").get(0).getFilePath()==null || detailMap.get("yyzz").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("营业执照不能为空");
        }
        DocResponseEntDetail yyzzDetail = detailMap.get("yyzz").get(0);
        vo.getPdfFiles().put("（1）营业执照", yyzzDetail.getFilePath());
        // 资质证明
        if (detailMap.get("zzzm") == null || detailMap.get("zzzm").isEmpty() ||
                detailMap.get("zzzm").get(0).getFilePath()==null || detailMap.get("zzzm").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("资质证明不能为空");
        }
        DocResponseEntDetail zzzmDetail = detailMap.get("zzzm").get(0);
        vo.getPdfFiles().put("（2）资质证明", zzzmDetail.getFilePath());

        // 信用查询
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }else if(data.getString("xylx").equals("jt")){
            if (detailMap.get("qgjzscjgggfwpt") == null || detailMap.get("qgjzscjgggfwpt").isEmpty() ||
                    detailMap.get("qgjzscjgggfwpt").get(0).getFilePath()==null || detailMap.get("qgjzscjgggfwpt").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("全国建筑市场监管公共服务平台信用查询截图不能为空");
            }
            DocResponseEntDetail qgjzscjgggfwptDetail = detailMap.get("qgjzscjgggfwpt").get(0);
            vo.getPdfFiles().put("全国建筑市场监管公共服务平台", qgjzscjgggfwptDetail.getFilePath());
            if (detailMap.get("xyzg") == null || detailMap.get("xyzg").isEmpty() ||
                    detailMap.get("xyzg").get(0).getFilePath()==null || detailMap.get("xyzg").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("信用中国信用查询截图不能为空");
            }
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            if (detailMap.get("zgzxxxgkw") == null || detailMap.get("zgzxxxgkw").isEmpty() ||
                    detailMap.get("zgzxxxgkw").get(0).getFilePath()==null || detailMap.get("zgzxxxgkw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国执行信息公开网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            if (detailMap.get("zgzfcgw") == null || detailMap.get("zgzfcgw").isEmpty() ||
                    detailMap.get("zgzfcgw").get(0).getFilePath()==null || detailMap.get("zgzfcgw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国政府采购网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }else {
            throw new ServiceException("信用查询状态异常");
        }

        // 其他资格证明文件
        if (detailMap.get("qtzgzmwj") != null) {
            List<DocResponseEntDetail> qtzgzmwjDetails = detailMap.get("qtzgzmwj");
            for (DocResponseEntDetail detail : qtzgzmwjDetails) {
                setAtts(vo, detail.getFilePath(), "QTZGZMWJ");
                vo.getPdfFiles().put("其他资格证明文件", detail.getFilePath());
            }
        }

        //符合性审查
        // 3、已标价工程量清单
        if (detailMap.get("ybjgclqd") == null || detailMap.get("ybjgclqd").isEmpty() ||
                detailMap.get("ybjgclqd").get(0).getFilePath()==null || detailMap.get("ybjgclqd").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("已标价工程量清单不能为空");
        }
        List<DocResponseEntDetail> ybjgclqdDetails = detailMap.get("ybjgclqd");
        for (DocResponseEntDetail detail : ybjgclqdDetails) {
            setAtts(vo, detail.getFilePath(), "JBGLGCQD");
            vo.getPdfFiles().put("响应人应按根据第五章“工程量清单”的要求", detail.getFilePath());
        }
        // 其他符合性因素
        if (detailMap.get("qtfhxys") != null) {
            List<DocResponseEntDetail> qtfhxysDetails = detailMap.get("qtfhxys");
            for (DocResponseEntDetail detail : qtfhxysDetails) {
                setAtts(vo, detail.getFilePath(), "QTSHXFYS");
            }
        }

        //技术部分
        // 施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划
        if (detailMap.get("sgfajjscs") == null || detailMap.get("sgfajjscs").isEmpty() ||
                detailMap.get("sgfajjscs").get(0).getFilePath()==null || detailMap.get("sgfajjscs").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("施工方案及技术措施不能为空");
        }
        DocResponseEntDetail sgfajjscsDetail = detailMap.get("sgfajjscs").get(0);
        vo.getContentMap().put("sgfajjscs", removeTags(sgfajjscsDetail.getDetailContent()));
        setAtts(vo, sgfajjscsDetail.getFilePath(), "SGFAJJSCS");
        vo.getPdfFiles().put("1、施工方案及技术措施", sgfajjscsDetail.getFilePath());

        // 2．工程进度计划与措施、施工进度或施工网络图、施工总平面布置图
        if (detailMap.get("gcjdjhycs") == null || detailMap.get("gcjdjhycs").isEmpty() ||
                detailMap.get("gcjdjhycs").get(0).getFilePath()==null || detailMap.get("gcjdjhycs").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("工程进度计划与措施不能为空");
        }
        DocResponseEntDetail gcjdjhycsDetail = detailMap.get("gcjdjhycs").get(0);
        vo.getContentMap().put("gcjdjhycs", removeTags(gcjdjhycsDetail.getDetailContent()));
        setAtts(vo, gcjdjhycsDetail.getFilePath(), "GCJDJHYCS");
        vo.getPdfFiles().put("2、工程进度计划与措施", gcjdjhycsDetail.getFilePath());

        // 节能减排（绿色施工、工艺创新）在本工程的具体应用措施
        if (detailMap.get("jnjp") == null || detailMap.get("jnjp").isEmpty() ||
                detailMap.get("jnjp").get(0).getFilePath()==null || detailMap.get("jnjp").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("节能减排不能为空");
        }
        DocResponseEntDetail jnjpDetail = detailMap.get("jnjp").get(0);
        vo.getContentMap().put("jnjp", removeTags(jnjpDetail.getDetailContent()));
        setAtts(vo, jnjpDetail.getFilePath(), "JNJP");
        vo.getPdfFiles().put("3、节能减排", jnjpDetail.getFilePath());

        // 新工艺（新技术、新设备、新材料）的采用程度
        if (detailMap.get("xgy") == null || detailMap.get("xgy").isEmpty() ||
                detailMap.get("xgy").get(0).getFilePath()==null || detailMap.get("xgy").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("新工艺不能为空");
        }
        DocResponseEntDetail xgyDetail = detailMap.get("xgy").get(0);
        vo.getContentMap().put("xgy", removeTags(xgyDetail.getDetailContent()));
        setAtts(vo, xgyDetail.getFilePath(), "XGY");
        vo.getPdfFiles().put("4、新工艺", xgyDetail.getFilePath());

        // 风险管理措施
        if (detailMap.get("fxglcs") == null || detailMap.get("fxglcs").isEmpty() ||
                detailMap.get("fxglcs").get(0).getFilePath()==null || detailMap.get("fxglcs").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("风险管理措施不能为空");
        }
        DocResponseEntDetail fxglcsDetail = detailMap.get("fxglcs").get(0);
        vo.getContentMap().put("fxglcs", removeTags(fxglcsDetail.getDetailContent()));
        setAtts(vo, fxglcsDetail.getFilePath(), "FXGLCS");
        vo.getPdfFiles().put("5、风险管理措施", fxglcsDetail.getFilePath());



//        // 3、（一）商务偏离表
//        if (detailMap.get("swplb") != null) {
//            DocResponseEntDetail swplbDetail = detailMap.get("swplb").get(0);
////            JSONObject swplb = JSONObject.parseObject(detailMap.get("swplb").get(0).getDetailContent());
//            setAtts(vo, swplbDetail.getFilePath(), "SWPDHB");
//        }
//        // 技术偏离表
//        if (detailMap.get("jsplb") != null) {
//            DocResponseEntDetail jsplbDetail = detailMap.get("jsplb").get(0);
////            JSONObject swplb = JSONObject.parseObject(detailMap.get("swplb").get(0).getDetailContent());
//            setAtts(vo, jsplbDetail.getFilePath(), "JSWPDHB");
//        }
        // 施工方案及技术措施
//        if (detailMap.get("fwfahjsfa") != null) {
//            DocResponseEntDetail jsplbDetail = detailMap.get("fwfahjsfa").get(0);
//            setAtts(vo, jsplbDetail.getFilePath(), "JBGLGCQD");
//        }
        // 体系认证
        if (detailMap.get("txrz") == null || detailMap.get("txrz").isEmpty() ||
                detailMap.get("txrz").get(0).getFilePath()==null || detailMap.get("txrz").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("体系认证不能为空");
        }
        DocResponseEntDetail txrzDetail = detailMap.get("txrz").get(0);
        vo.getContentMap().put("txrz", txrzDetail.getDetailContent());
//            setAtts(vo, txrzDetail.getFilePath(), "TXRZ");
        vo.getPdfFiles().put("1、体系认证", txrzDetail.getFilePath());

        // 拟派项目管理人员
        if (detailMap.get("npxmglry") == null || detailMap.get("npxmglry").isEmpty() ||
                detailMap.get("npxmglry").get(0).getFilePath()==null || detailMap.get("npxmglry").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("拟派项目管理人员不能为空");
        }
        DocResponseEntDetail npxmglryDetail = detailMap.get("npxmglry").get(0);
        vo.getContentMap().put("npxmglry", npxmglryDetail.getDetailContent());
//            setAtts(vo, npxmglryDetail.getFilePath(), "NPXMGLRY");
        vo.getPdfFiles().put("2、拟派项目管理人员", npxmglryDetail.getFilePath());

        // 项目技术负责人
        if (detailMap.get("xmjsfzr") == null || detailMap.get("xmjsfzr").isEmpty() ||
                detailMap.get("xmjsfzr").get(0).getFilePath()==null || detailMap.get("xmjsfzr").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("项目技术负责人不能为空");
        }
        DocResponseEntDetail xmjsfzrDetail = detailMap.get("xmjsfzr").get(0);
        vo.getContentMap().put("xmjsfzr", xmjsfzrDetail.getDetailContent());
//            setAtts(vo, npxmglryDetail.getFilePath(), "NPXMGLRY");
        vo.getPdfFiles().put("3、项目技术负责人", xmjsfzrDetail.getFilePath());

        // 类似业绩
        if (detailMap.get("lsyj") == null || detailMap.get("lsyj").isEmpty() ||
                detailMap.get("lsyj").get(0).getFilePath()==null || detailMap.get("lsyj").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("类似业绩不能为空");
        }
        DocResponseEntDetail lsyjDetail = detailMap.get("lsyj").get(0);
        vo.getContentMap().put("lsyj", lsyjDetail.getDetailContent());
        vo.getPdfFiles().put("4、类似业绩", lsyjDetail.getFilePath());

        // 其他资料
        // 1、投标人认为需要提供的其他资料
        if (detailMap.get("tbrrwxytgdqtzl") != null) {
            DocResponseEntDetail qtzlDetail = detailMap.get("tbrrwxytgdqtzl").get(0);
            vo.getContentMap().put("tbrrwxytgdqtzl", qtzlDetail.getDetailContent());
            setAtts(vo, qtzlDetail.getFilePath(), "TBRRWXYTGDQTZL");
            vo.getPdfFiles().put("注：响应人认为需要提供的其他资料", qtzlDetail.getFilePath());
        }

        try {
            String pdfPath = procurementDocumentsUinfoService.prepareResponseDocuments(vo);
            String url = AttachmentUtil.realToUrl(pdfPath);
            System.out.println(url);
            info.setPdfPath(url);
            info.setBasePdfPath(url);
            info.setRemark(PdfUtil.getPageNum(pdfPath, ScoreItemKeywordEnum.getKeywordList(1,0)));
            updateById(info);
            return AjaxResult.success("操作成功", url);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult createGoodsDocResponse(JSONObject data) throws Exception {
        Long id = data.getLong("infoId");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        BaseEntInfo ent = sysUser.getEnt();
        DocResponseEntInfo info = selectById(id);
        ProcurementDocumentVo vo = new ProcurementDocumentVo();
        vo.setEntId(SecurityUtils.getLoginUser().getEntId());
        vo.setBidderName(ent.getEntName());
        vo.setProjectId(info.getProjectId());
        Map<String, List<DocResponseEntDetail>> detailMap = info.getDetailMap();
        //开始填充数据
        //1 开标一览表   {"tbbjdx":"壹佰元整","tbbjxx":"100","gq":"10","zbq":"20","zlbz":"合格","tbyxq":"90天"}
        if (detailMap.get("kbylb")==null || detailMap.get("kbylb").isEmpty()) {
            throw new ServiceException("开标一览表信息不能为空");
        }
        JSONObject kbylb = JSONObject.parseObject(detailMap.get(ResponseDocEnum.KBYLB.getCode()).get(0).getDetailContent());
        vo.setBidAmountCapitalization(kbylb.getString("tbbjdx"));
        vo.setBidAmount(kbylb.getInteger("bidPrice"));
        vo.setOverTimeLimit(kbylb.getInteger("overTimeLimit"));
        vo.setWarrantyPeriod(kbylb.getInteger("warrantyPeriod"));
        vo.setQualityDemand(kbylb.getString("qualityDemand"));
        vo.setBidValidPeriod(kbylb.getString("tbyxq"));

        //2 明细报价表
        if (detailMap.get("mxbjb") == null || detailMap.get("mxbjb").isEmpty() ||
                detailMap.get("mxbjb").get(0).getFilePath()==null || detailMap.get("mxbjb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("明细报价表不能为空");
        }
        DocResponseEntDetail mxbjbDetails = detailMap.get("mxbjb").get(0);
        vo.getPdfFiles().put("明细报价表", mxbjbDetails.getFilePath());

        // 根据传参中的frorsqs判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("frorsqs").equals("fr")){
            if (detailMap.get("fddbrsfzm") == null || detailMap.get("fddbrsfzm").isEmpty() ||
                    detailMap.get("fddbrsfzm").get(0).getFilePath()==null || detailMap.get("fddbrsfzm").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            //2 法定代表人身份证明   {"fddbrxm":"4","fddbrxb":"3","fddbrnl":"2","fddbrzw":"1","id":1200227728098309}
            DocResponseEntDetail fddbrsfzmDetail = detailMap.get("fddbrsfzm").get(0);
            JSONObject fddbrsfzm = JSONObject.parseObject(fddbrsfzmDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsfzm.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsfzm.getString("fddbrxb"));
            vo.setLegalPersonAge(fddbrsfzm.getInteger("fddbrnl"));
            vo.setLegalPersonPosition(fddbrsfzm.getString("fddbrzw"));
//            setAtts(vo, fddbrsfzmDetail.getFilePath(), "FDDBRJZSFZ");
            vo.getPdfFiles().put("附：法定代表人身份证复印件（正反面）", fddbrsfzmDetail.getFilePath());
            vo.setFddbrsfzm(true);
        }else{
            if (detailMap.get("fddbrsqs") == null || detailMap.get("fddbrsqs").isEmpty() ||
                    detailMap.get("fddbrsqs").get(0).getFilePath()==null || detailMap.get("fddbrsqs").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            //3 法定代表人授权书   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            DocResponseEntDetail fddbrsqsDetail = detailMap.get("fddbrsqs").get(0);
            JSONObject fddbrsqs = JSONObject.parseObject(fddbrsqsDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsqs.getString("fddbrxm"));
            vo.setEntrustPersonName(fddbrsqs.getString("wtrxm"));
            vo.setEntrustDeadline(fddbrsqs.getString("wtqx"));
//            setAtts(vo, fddbrsqsDetail.getFilePath(), "FDDBRSQS");
            vo.getPdfFiles().put("附：法定代表人和授权代表身份证复印件(正反面)", fddbrsqsDetail.getFilePath());
            vo.setSqwts(true);
        }

        // 资格审查及评审资料
        // 1、落实政府采购政策需满足的资格条件 tsqylx:  zxqy中小企业声明函  cjrflxdw残疾人福利性单位  jyqy监狱企业
        if(data.getString("tsqylx").equals("zxqy")){
            if (detailMap.get("zxqysmh") == null || detailMap.get("zxqysmh").isEmpty()) {
                throw new ServiceException("中小企业声明函信息不能为空");
            }
            // 中小企业声明函   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            StringBuffer zxqysmhxx = new StringBuffer();
            for (int i=0; i<zxqysmhArry.size(); i++) {
                JSONObject obj = zxqysmhArry.getJSONObject(i);
                zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
            };
            vo.setZxqysmhxx(zxqysmhxx.toString());
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            if (detailMap.get("jyqy") == null || detailMap.get("jyqy").isEmpty() ||
                    detailMap.get("jyqy").get(0).getFilePath()==null || detailMap.get("jyqy").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("监狱企业证明文件不能为空");
            }
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            setAtts(vo, jyqyDetail.getFilePath(), "JKQY");
            vo.setJyqy(true);
            vo.getPdfFiles().put("：监狱企业", jyqyDetail.getFilePath());
        }

        // 特定资格要求
        // 营业执照
        if (detailMap.get("yyzz") == null || detailMap.get("yyzz").isEmpty() ||
                detailMap.get("yyzz").get(0).getFilePath()==null || detailMap.get("yyzz").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("营业执照不能为空");
        }
        DocResponseEntDetail yyzzDetail = detailMap.get("yyzz").get(0);
        vo.getPdfFiles().put("（1）营业执照", yyzzDetail.getFilePath());
        // 资质证明
        if (detailMap.get("zzzm") == null || detailMap.get("zzzm").isEmpty() ||
                detailMap.get("zzzm").get(0).getFilePath()==null || detailMap.get("zzzm").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("资质证明不能为空");
        }
        DocResponseEntDetail zzzmDetail = detailMap.get("zzzm").get(0);
        vo.getPdfFiles().put("（2）资质证明", zzzmDetail.getFilePath());

        // 信用查询
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }else if(data.getString("xylx").equals("jt")){
            if (detailMap.get("xyzg") == null || detailMap.get("xyzg").isEmpty() ||
                    detailMap.get("xyzg").get(0).getFilePath()==null || detailMap.get("xyzg").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("信用中国信用查询截图不能为空");
            }
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            if (detailMap.get("zgzxxxgkw") == null || detailMap.get("zgzxxxgkw").isEmpty() ||
                    detailMap.get("zgzxxxgkw").get(0).getFilePath()==null || detailMap.get("zgzxxxgkw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国执行信息公开网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            if (detailMap.get("zgzfcgw") == null || detailMap.get("zgzfcgw").isEmpty() ||
                    detailMap.get("zgzfcgw").get(0).getFilePath()==null || detailMap.get("zgzfcgw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国政府采购网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }else {
            throw new ServiceException("信用查询状态异常");
        }

        // 其他资格证明文件
        if (detailMap.get("qtzgzmwj") != null) {
            List<DocResponseEntDetail> qtzgzmwjDetails = detailMap.get("qtzgzmwj");
            for (DocResponseEntDetail detail : qtzgzmwjDetails) {
                setAtts(vo, detail.getFilePath(), "QTZGZMWJ");
                vo.getPdfFiles().put("其他资格证明文件", detail.getFilePath());
            }
        }

        //技术部分
        // 1.技术方案
        if (detailMap.get("jsfa") == null || detailMap.get("jsfa").isEmpty() ||
                detailMap.get("jsfa").get(0).getFilePath()==null || detailMap.get("jsfa").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("技术方案不能为空");
        }
        DocResponseEntDetail jsfaDetail = detailMap.get("jsfa").get(0);
        vo.getContentMap().put("jsfa", removeTags(jsfaDetail.getDetailContent()));
        setAtts(vo, jsfaDetail.getFilePath(), "JSFA");
        vo.getPdfFiles().put("1、技术方案", jsfaDetail.getFilePath());

        // 2．技术偏离表
        if (detailMap.get("jsplb") == null || detailMap.get("jsplb").isEmpty() ||
                detailMap.get("jsplb").get(0).getFilePath()==null || detailMap.get("jsplb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("技术偏离表不能为空");
        }
        DocResponseEntDetail jsplbDetail = detailMap.get("jsplb").get(0);
        vo.getContentMap().put("jsplb", removeTags(jsplbDetail.getDetailContent()));
        setAtts(vo, jsplbDetail.getFilePath(), "JSPLB");
        vo.getPdfFiles().put("2、技术偏离表", jsplbDetail.getFilePath());

        // 商务部分
        List<DocResponseEntDetail> businessPartList = info.getBusinessPart();
        if (businessPartList!=null && businessPartList.size()>0) {
            int i = 1;
            for (DocResponseEntDetail part:businessPartList) {
                if (detailMap.get(part.getDetailCode()) == null || detailMap.get(part.getDetailCode()).isEmpty() ||
                        detailMap.get(part.getDetailCode()).get(0).getFilePath()==null || detailMap.get(part.getDetailCode()).get(0).getFilePath().isEmpty()) {
                    throw new ServiceException(part.getDetailName() + "不能为空");
                }
                DocResponseEntDetail businessPartDetail = detailMap.get(part.getDetailCode()).get(0);
                vo.getContentMap().put(part.getDetailCode(), removeTags(businessPartDetail.getDetailContent()));
//                setAtts(vo, businessPartDetail.getFilePath(), part.getDetailCode().toUpperCase(Locale.ROOT));
                vo.getPdfFiles().put(i+"、"+part.getDetailName(), businessPartDetail.getFilePath());
                if (i==1) {
                    vo.setSwbf1(true);
                    vo.setSwbfName1(i+"、"+part.getDetailName());
                }
                if (i==2) {
                    vo.setSwbf2(true);
                    vo.setSwbfName2(i+"、"+part.getDetailName());
                }
                if (i==3) {
                    vo.setSwbf3(true);
                    vo.setSwbfName3(i+"、"+part.getDetailName());
                }
                if (i==4) {
                    vo.setSwbf4(true);
                    vo.setSwbfName4(i+"、"+part.getDetailName());
                }
                if (i==5) {
                    vo.setSwbf5(true);
                    vo.setSwbfName5(i+"、"+part.getDetailName());
                }
                if (i==6) {
                    vo.setSwbf6(true);
                    vo.setSwbfName6(i+"、"+part.getDetailName());
                }
                i++;
            }
        }

        // 其他资料
        // 1、投标人认为需要提供的其他资料
        if (detailMap.get("tbrrwxytgdqtzl") != null) {
            DocResponseEntDetail qtzlDetail = detailMap.get("tbrrwxytgdqtzl").get(0);
            vo.getContentMap().put("tbrrwxytgdqtzl", qtzlDetail.getDetailContent());
            setAtts(vo, qtzlDetail.getFilePath(), "TBRRWXYTGDQTZL");
            vo.getPdfFiles().put("1、投标人认为需要提供的其他资料", qtzlDetail.getFilePath());
        }
        try {
            String pdfPath = procurementDocumentsUinfoService.prepareGoodsResponseDocuments(vo);
            String url = AttachmentUtil.realToUrl(pdfPath);
            System.out.println(url);
            info.setPdfPath(url);
            info.setBasePdfPath(url);
            info.setRemark(PdfUtil.getPageNum(pdfPath, ScoreItemKeywordEnum.getKeywordList(1,2)));
            updateById(info);
            return AjaxResult.success("操作成功", url);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult createServiceDocResponse(JSONObject data) throws Exception {
        Long id = data.getLong("infoId");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        BaseEntInfo ent = sysUser.getEnt();
        DocResponseEntInfo info = selectById(id);
        ProcurementDocumentVo vo = new ProcurementDocumentVo();
        vo.setEntId(SecurityUtils.getLoginUser().getEntId());
        vo.setBidderName(ent.getEntName());
        vo.setProjectId(info.getProjectId());
        Map<String, List<DocResponseEntDetail>> detailMap = info.getDetailMap();
        //开始填充数据
        //1 开标一览表   {"tbbjdx":"壹佰元整","tbbjxx":"100","gq":"10","zbq":"20","zlbz":"合格","tbyxq":"90天"}
        if (detailMap.get("kbylb")==null || detailMap.get("kbylb").isEmpty()) {
            throw new ServiceException("开标一览表信息不能为空");
        }
        JSONObject kbylb = JSONObject.parseObject(detailMap.get(ResponseDocEnum.KBYLB.getCode()).get(0).getDetailContent());
        vo.setBidAmountCapitalization(kbylb.getString("tbbjdx"));
        vo.setBidAmount(kbylb.getInteger("bidPrice"));
        vo.setOverTimeLimit(kbylb.getInteger("overTimeLimit"));
        vo.setWarrantyPeriod(kbylb.getInteger("warrantyPeriod"));
        vo.setQualityDemand(kbylb.getString("qualityDemand"));
        vo.setBidValidPeriod(kbylb.getString("tbyxq"));

        //2 明细报价表
        if (detailMap.get("mxbjb") == null || detailMap.get("mxbjb").isEmpty() ||
                detailMap.get("mxbjb").get(0).getFilePath()==null || detailMap.get("mxbjb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("明细报价表不能为空");
        }
        DocResponseEntDetail mxbjbDetails = detailMap.get("mxbjb").get(0);
        vo.getPdfFiles().put("明细报价表", mxbjbDetails.getFilePath());

        // 根据传参中的frorsqs判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("frorsqs").equals("fr")){
            if (detailMap.get("fddbrsfzm") == null || detailMap.get("fddbrsfzm").isEmpty() ||
                    detailMap.get("fddbrsfzm").get(0).getFilePath()==null || detailMap.get("fddbrsfzm").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            //2 法定代表人身份证明   {"fddbrxm":"4","fddbrxb":"3","fddbrnl":"2","fddbrzw":"1","id":1200227728098309}
            DocResponseEntDetail fddbrsfzmDetail = detailMap.get("fddbrsfzm").get(0);
            if (fddbrsfzmDetail.getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            JSONObject fddbrsfzm = JSONObject.parseObject(fddbrsfzmDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsfzm.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsfzm.getString("fddbrxb"));
            vo.setLegalPersonAge(fddbrsfzm.getInteger("fddbrnl"));
            vo.setLegalPersonPosition(fddbrsfzm.getString("fddbrzw"));
            vo.getPdfFiles().put("法定代表人身份证复印件（正反面）", fddbrsfzmDetail.getFilePath());
            vo.setFddbrsfzm(true);
        }else{
            if (detailMap.get("fddbrsqs") == null || detailMap.get("fddbrsqs").isEmpty() ||
                    detailMap.get("fddbrsqs").get(0).getFilePath()==null || detailMap.get("fddbrsqs").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            //3 法定代表人授权书   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            DocResponseEntDetail fddbrsqsDetail = detailMap.get("fddbrsqs").get(0);
            if (fddbrsqsDetail.getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            JSONObject fddbrsqs = JSONObject.parseObject(fddbrsqsDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsqs.getString("fddbrxm"));
            vo.setEntrustPersonName(fddbrsqs.getString("wtrxm"));
            vo.setEntrustDeadline(fddbrsqs.getString("wtqx"));
//            setAtts(vo, fddbrsqsDetail.getFilePath(), "FDDBRSQS");
            vo.getPdfFiles().put("法定代表人和授权代表身份证复印件(正反面)", fddbrsqsDetail.getFilePath());
            vo.setSqwts(true);
        }

        // 资格审查及评审资料
        // 1、落实政府采购政策需满足的资格条件 tsqylx:  zxqy中小企业声明函  cjrflxdw残疾人福利性单位  jyqy监狱企业
        if(data.getString("tsqylx").equals("zxqy")){
            if (detailMap.get("zxqysmh") == null || detailMap.get("zxqysmh").isEmpty()) {
                throw new ServiceException("中小企业声明函信息不能为空");
            }
            JSONObject zxqysmh = JSONObject.parseObject(detailMap.get("zxqysmh").get(0).getDetailContent());
            vo.setTrade(zxqysmh.getString("sshy"));
            vo.setPractitionerNum(zxqysmh.getString("cyryrs"));
            vo.setOperatingRevenue(zxqysmh.getBigDecimal("yysr"));
            vo.setTotalAssets(zxqysmh.getBigDecimal("zcze"));
            vo.setCompanyType(zxqysmh.getString("qylx"));
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            if (detailMap.get("jyqy") == null || detailMap.get("jyqy").isEmpty()) {
                throw new ServiceException("监狱企业的证明文件不能为空");
            }
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            if (jyqyDetail.getFilePath().isEmpty()) {
                throw new ServiceException("监狱企业的证明文件不能为空");
            }
            vo.setJyqy(true);
            vo.getPdfFiles().put("：监狱企业", jyqyDetail.getFilePath());
        }

        // 特定资格要求
        // 营业执照
        if (detailMap.get("yyzz") == null || detailMap.get("yyzz").isEmpty() ||
                detailMap.get("yyzz").get(0).getFilePath()==null || detailMap.get("yyzz").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("营业执照不能为空");
        }
        DocResponseEntDetail yyzzDetail = detailMap.get("yyzz").get(0);
        vo.getPdfFiles().put("（1）营业执照", yyzzDetail.getFilePath());
        // 资质证明
        if (detailMap.get("zzzm") == null || detailMap.get("zzzm").isEmpty() ||
                detailMap.get("zzzm").get(0).getFilePath()==null || detailMap.get("zzzm").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("资质证明不能为空");
        }
        DocResponseEntDetail zzzmDetail = detailMap.get("zzzm").get(0);
        vo.getPdfFiles().put("（2）资质证明", zzzmDetail.getFilePath());


        // 信用查询
        // 根据传参中的xylx判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("xylx").equals("cnh")){
            vo.setXycnh(true);
        }else if(data.getString("xylx").equals("jt")){
            if (detailMap.get("xyzg") == null || detailMap.get("xyzg").isEmpty() ||
                    detailMap.get("xyzg").get(0).getFilePath()==null || detailMap.get("xyzg").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("信用中国信用查询截图不能为空");
            }
            DocResponseEntDetail xyzgDetail = detailMap.get("xyzg").get(0);
            vo.getPdfFiles().put("信用中国", xyzgDetail.getFilePath());
            if (detailMap.get("zgzxxxgkw") == null || detailMap.get("zgzxxxgkw").isEmpty() ||
                    detailMap.get("zgzxxxgkw").get(0).getFilePath()==null || detailMap.get("zgzxxxgkw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国执行信息公开网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzxxxgkwDetail = detailMap.get("zgzxxxgkw").get(0);
            vo.getPdfFiles().put("中国执行信息公开网", zgzxxxgkwDetail.getFilePath());
            if (detailMap.get("zgzfcgw") == null || detailMap.get("zgzfcgw").isEmpty() ||
                    detailMap.get("zgzfcgw").get(0).getFilePath()==null || detailMap.get("zgzfcgw").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("中国政府采购网信用查询截图不能为空");
            }
            DocResponseEntDetail zgzfcgwDetail = detailMap.get("zgzfcgw").get(0);
            vo.getPdfFiles().put("中国政府采购网", zgzfcgwDetail.getFilePath());
            vo.setXyjt(true);
        }else {
            throw new ServiceException("信用查询状态异常");
        }

        // 其他资格证明文件
        if (detailMap.get("qtzgzmwj") != null) {
            List<DocResponseEntDetail> qtzgzmwjDetails = detailMap.get("qtzgzmwj");
            for (DocResponseEntDetail detail : qtzgzmwjDetails) {
                if (detail.getFilePath().isEmpty()) {
                    throw new ServiceException("其他资格证明文件不能为空");
                }
                vo.getPdfFiles().put("4、其他资格证明文件", detail.getFilePath());
            }
        }


        //技术部分
        // 1.服务方案
        if (detailMap.get("fwfa") == null || detailMap.get("fwfa").isEmpty() ||
                detailMap.get("fwfa").get(0).getFilePath()==null || detailMap.get("fwfa").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("服务方案不能为空");
        }
        DocResponseEntDetail jsfaDetail = detailMap.get("fwfa").get(0);
        vo.getContentMap().put("fwfa", removeTags(jsfaDetail.getDetailContent()));
        setAtts(vo, jsfaDetail.getFilePath(), "FWFA");
        vo.getPdfFiles().put("1、服务方案", jsfaDetail.getFilePath());

        // 2．技术偏离表
        if (detailMap.get("jsplb") == null || detailMap.get("jsplb").isEmpty() ||
                detailMap.get("jsplb").get(0).getFilePath()==null || detailMap.get("jsplb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("技术偏离表不能为空");
        }
        DocResponseEntDetail jsplbDetail = detailMap.get("jsplb").get(0);
        vo.getContentMap().put("jsplb", removeTags(jsplbDetail.getDetailContent()));
        setAtts(vo, jsplbDetail.getFilePath(), "JSPLB");
        vo.getPdfFiles().put("2、技术偏离表", jsplbDetail.getFilePath());

        // 3．拟派项目组人员表
        if (detailMap.get("npxmzryb") == null || detailMap.get("npxmzryb").isEmpty() ||
                detailMap.get("npxmzryb").get(0).getFilePath()==null || detailMap.get("npxmzryb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("拟派项目组人员表不能为空");
        }
        DocResponseEntDetail npxmzrybDetail = detailMap.get("npxmzryb").get(0);
        vo.getContentMap().put("npxmzryb", removeTags(npxmzrybDetail.getDetailContent()));
        vo.getPdfFiles().put("3、拟派项目组人员表", npxmzrybDetail.getFilePath());

        // 商务部分
        List<DocResponseEntDetail> businessPartList = info.getBusinessPart();
        if (businessPartList!=null && businessPartList.size()>0) {
            int i = 1;
            for (DocResponseEntDetail part:businessPartList) {
                if (detailMap.get(part.getDetailCode()) == null || detailMap.get(part.getDetailCode()).isEmpty() ||
                        detailMap.get(part.getDetailCode()).get(0).getFilePath()==null || detailMap.get(part.getDetailCode()).get(0).getFilePath().isEmpty()) {
                    throw new ServiceException(part.getDetailName() + "不能为空");
                }
                DocResponseEntDetail businessPartDetail = detailMap.get(part.getDetailCode()).get(0);
                vo.getContentMap().put(part.getDetailCode(), removeTags(businessPartDetail.getDetailContent()));
                vo.getPdfFiles().put(i+"、"+part.getDetailName(), businessPartDetail.getFilePath());
                if (i==1) {
                    vo.setSwbf1(true);
                    vo.setSwbfName1(i+"、"+part.getDetailName());
                }
                if (i==2) {
                    vo.setSwbf2(true);
                    vo.setSwbfName2(i+"、"+part.getDetailName());
                }
                if (i==3) {
                    vo.setSwbf3(true);
                    vo.setSwbfName3(i+"、"+part.getDetailName());
                }
                if (i==4) {
                    vo.setSwbf4(true);
                    vo.setSwbfName4(i+"、"+part.getDetailName());
                }
                if (i==5) {
                    vo.setSwbf5(true);
                    vo.setSwbfName5(i+"、"+part.getDetailName());
                }
                if (i==6) {
                    vo.setSwbf6(true);
                    vo.setSwbfName6(i+"、"+part.getDetailName());
                }
                i++;
            }
        }

        // 其他资料
        // 1、投标人认为需要提供的其他资料
        if (detailMap.get("tbrrwxytgdqtzl") != null) {
            DocResponseEntDetail lsyjDetail = detailMap.get("tbrrwxytgdqtzl").get(0);
            vo.getContentMap().put("tbrrwxytgdqtzl", lsyjDetail.getDetailContent());
            setAtts(vo, lsyjDetail.getFilePath(), "TBRRWXYTGDQTZL");
            vo.getPdfFiles().put("1、投标人认为需要提供的其他资料", lsyjDetail.getFilePath());
        }
        try {
            String pdfPath = procurementDocumentsUinfoService.prepareServiceResponseDocuments(vo);
            String url = AttachmentUtil.realToUrl(pdfPath);
            System.out.println(url);
            info.setPdfPath(url);
            info.setBasePdfPath(url);
            info.setRemark(PdfUtil.getPageNum(pdfPath, ScoreItemKeywordEnum.getKeywordList(1,1)));
            updateById(info);
            return AjaxResult.success("操作成功", url);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }
    }

    /**
     * 生成询价响应文件
     * @param data
     * @return
     * @throws Exception
     */
    @Override
    public AjaxResult createInquiryGoodsDocResponse(JSONObject data) throws Exception {
        Long id = data.getLong("infoId");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        BaseEntInfo ent = sysUser.getEnt();
        DocResponseEntInfo info = selectById(id);
        ProcurementDocumentVo vo = new ProcurementDocumentVo();
        vo.setEntId(SecurityUtils.getLoginUser().getEntId());
        vo.setBidderName(ent.getEntName());
        vo.setProjectId(info.getProjectId());
        Map<String, List<DocResponseEntDetail>> detailMap = info.getDetailMap();
        //开始填充数据
        if (detailMap.get("kbylb")==null || detailMap.get("kbylb").isEmpty()) {
            throw new ServiceException("开标一览表信息不能为空");
        }
        JSONObject kbylb = JSONObject.parseObject(detailMap.get(ResponseDocEnum.KBYLB.getCode()).get(0).getDetailContent());
        vo.setBidAmountCapitalization(kbylb.getString("tbbjdx"));
        vo.setBidAmount(kbylb.getInteger("bidPrice"));
        vo.setOverTimeLimit(kbylb.getInteger("overTimeLimit"));
        vo.setWarrantyPeriod(kbylb.getInteger("warrantyPeriod"));
        vo.setQualityDemand(kbylb.getString("qualityDemand"));
        vo.setBidValidPeriod(kbylb.getString("tbyxq"));

        //2 明细报价表
        if (detailMap.get("mxbjb") == null || detailMap.get("mxbjb").isEmpty() ||
                detailMap.get("mxbjb").get(0).getFilePath()==null || detailMap.get("mxbjb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("明细报价表不能为空");
        }
        DocResponseEntDetail mxbjbDetails = detailMap.get("mxbjb").get(0);
        vo.getContentMap().put("mxbjb", removeTags(mxbjbDetails.getDetailContent()));
        String mxbjbFilePath = mxbjbDetails.getFilePath();
        // 判断是否有多个文件
        if (StringUtils.isNotEmpty(mxbjbFilePath)) {
            String[] mxbjbArr = mxbjbFilePath.split(",");
            for (String filePath:mxbjbArr) {
                String mxbjbPath = AttachmentUtil.urlToReal(filePath);
                vo.getPdfFiles().put("、明细报价表", mxbjbPath);
//                String mxbjbPdfPath = RuoYiConfig.getUploadPath() + "/" + UUID.randomUUID().toString().replaceAll("-","") + ".pdf";
//                if (filePath.contains("xlsx")) { // 是xlsx文件，需要转pdf
//                    try {
//                        ExcelToPdfConverter.convertExcelToPdf(mxbjbPath, mxbjbPdfPath);
//                    } catch (Exception e) {
//                        log.error("excel文件转pdf文件异常",e.getMessage());
//                        e.printStackTrace();
//                        throw new ServiceException("excel文件转pdf文件异常");
//                    }
//                    vo.getPdfFiles().put("、明细报价表", mxbjbPdfPath);
//                } else {
//                    vo.getPdfFiles().put("、明细报价表", mxbjbPath);
//                }
            }
        }

        // 根据传参中的frorsqs判断用户选择的是法定代表人身份证明还是法定代表人授权书
        if(data.getString("frorsqs").equals("fr")){
            if (detailMap.get("fddbrsfzm") == null || detailMap.get("fddbrsfzm").isEmpty() ||
                    detailMap.get("fddbrsfzm").get(0).getFilePath()==null || detailMap.get("fddbrsfzm").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人身份证明信息不能为空");
            }
            //2 法定代表人身份证明   {"fddbrxm":"4","fddbrxb":"3","fddbrnl":"2","fddbrzw":"1","id":1200227728098309}
            DocResponseEntDetail fddbrsfzmDetail = detailMap.get("fddbrsfzm").get(0);
            JSONObject fddbrsfzm = JSONObject.parseObject(fddbrsfzmDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsfzm.getString("fddbrxm"));
            vo.setLegalPersonSex(fddbrsfzm.getString("fddbrxb"));
            vo.setLegalPersonAge(fddbrsfzm.getInteger("fddbrnl"));
            vo.setLegalPersonPosition(fddbrsfzm.getString("fddbrzw"));
//            setAtts(vo, fddbrsfzmDetail.getFilePath(), "FDDBRJZSFZ");
            vo.getPdfFiles().put("附：法定代表人身份证复印件（正反面）", AttachmentUtil.urlToReal(fddbrsfzmDetail.getFilePath()));
            vo.setFddbrsfzm(true);
        }else{
            if (detailMap.get("fddbrsqs") == null || detailMap.get("fddbrsqs").isEmpty() ||
                    detailMap.get("fddbrsqs").get(0).getFilePath()==null || detailMap.get("fddbrsqs").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("法定代表人授权书信息不能为空");
            }
            //3 法定代表人授权书   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            DocResponseEntDetail fddbrsqsDetail = detailMap.get("fddbrsqs").get(0);
            JSONObject fddbrsqs = JSONObject.parseObject(fddbrsqsDetail.getDetailContent());
            vo.setLegalPersonName(fddbrsqs.getString("fddbrxm"));
            vo.setEntrustPersonName(fddbrsqs.getString("wtrxm"));
            vo.setEntrustDeadline(fddbrsqs.getString("wtqx"));
//            setAtts(vo, fddbrsqsDetail.getFilePath(), "FDDBRSQS");
            vo.getPdfFiles().put("附：法定代表人和授权代表身份证复印件(正反面)", fddbrsqsDetail.getFilePath());
            vo.setSqwts(true);
        }

        // 资格审查及评审资料
        // 1、落实政府采购政策需满足的资格条件 tsqylx:  zxqy中小企业声明函  cjrflxdw残疾人福利性单位  jyqy监狱企业
        if(data.getString("tsqylx").equals("zxqy")){
            if (detailMap.get("zxqysmh") == null || detailMap.get("zxqysmh").isEmpty()) {
                throw new ServiceException("中小企业声明函信息不能为空");
            }
            // 中小企业声明函   {"fddbrxm":"222","wtrxm":"111","id":1200228492239877}
            JSONArray zxqysmhArry = JSONArray.parseArray(detailMap.get("zxqysmh").get(0).getDetailContent());
            StringBuffer zxqysmhxx = new StringBuffer();
            for (int i=0; i<zxqysmhArry.size(); i++) {
                JSONObject obj = zxqysmhArry.getJSONObject(i);
                zxqysmhxx.append(i+1).append("."+obj.getString("hwmc")+"属于（"+obj.getString("sshy")+"）；制造商为"+obj.getString("zzsmc")+"，从业人员"+obj.getString("cyryrs")+"人，营业收入为"+obj.getString("yysr")+"万元，资产总额为"+obj.getString("zcze")+"万元，属于"+obj.getString("qylx")+"；\n");
            };
            vo.setZxqysmhxx(zxqysmhxx.toString());
            vo.setZxqysmh(true);
        }else if(data.getString("tsqylx").equals("cjrflxdw")){
            vo.setCjrfuxdwsmh(true);
        }else if(data.getString("tsqylx").equals("jyqy")){
            if (detailMap.get("jyqy") == null || detailMap.get("jyqy").isEmpty() ||
                    detailMap.get("jyqy").get(0).getFilePath()==null || detailMap.get("jyqy").get(0).getFilePath().isEmpty()) {
                throw new ServiceException("监狱企业证明文件不能为空");
            }
            DocResponseEntDetail jyqyDetail = detailMap.get("jyqy").get(0);
            setAtts(vo, jyqyDetail.getFilePath(), "JKQY");
            vo.setJyqy(true);
            vo.addPdfFile("：监狱企业", jyqyDetail.getFilePath());
        }

        // 特定资格要求
        // 营业执照
        if (detailMap.get("yyzz") == null || detailMap.get("yyzz").isEmpty() ||
                detailMap.get("yyzz").get(0).getFilePath()==null || detailMap.get("yyzz").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("营业执照不能为空");
        }
        DocResponseEntDetail yyzzDetail = detailMap.get("yyzz").get(0);
        vo.addPdfFile("（1）营业执照", AttachmentUtil.urlToReal(yyzzDetail.getFilePath()));

        // 资质证明
        if (detailMap.get("zzzm") == null || detailMap.get("zzzm").isEmpty()) {
            throw new ServiceException("资质证明不能为空");
        }
        List<DocResponseEntDetail> zzzms = detailMap.get("zzzm");
        for (DocResponseEntDetail zzzm : zzzms) {
            if (zzzm.getFilePath()==null || zzzm.getFilePath().isEmpty()) {
                throw new ServiceException("其他资格证明文件不能为空");
            }
            vo.addPdfFile("（2）资质证明",  AttachmentUtil.urlToReal(zzzm.getFilePath()));
        }

        // 技术部分
        // 1．技术偏离表
        if (detailMap.get("jsplb") == null || detailMap.get("jsplb").isEmpty() ||
                detailMap.get("jsplb").get(0).getFilePath()==null || detailMap.get("jsplb").get(0).getFilePath().isEmpty()) {
            throw new ServiceException("技术偏离表不能为空");
        }
        DocResponseEntDetail jsplbDetail = detailMap.get("jsplb").get(0);
        vo.getContentMap().put("jsplb", removeTags(jsplbDetail.getDetailContent()));
        String jsplbFilePath = jsplbDetail.getFilePath();
        // 判断是否有多个文件
        if (StringUtils.isNotEmpty(jsplbFilePath)) {
            String[] jsplbArr = jsplbFilePath.split(",");
            for (String filePath:jsplbArr) {
                String jsplbPath = AttachmentUtil.urlToReal(filePath);
                String jsplbPdfPath = RuoYiConfig.getUploadPath() + "/" + UUID.randomUUID().toString().replaceAll("-","") + ".pdf";
                if (filePath.contains("xlsx")) { // 是xlsx文件，需要转pdf
                    try {
                        ExcelToPdfConverter.convertExcelToPdf(jsplbPath, jsplbPdfPath);
                    } catch (Exception e) {
                        log.error("excel文件转pdf文件异常",e.getMessage());
                        e.printStackTrace();
                        throw new ServiceException("excel文件转pdf文件异常");
                    }
                    vo.getPdfFiles().put("、技术偏离表", jsplbPdfPath);
                } else {
                    vo.getPdfFiles().put("、技术偏离表", jsplbPath);
                }
            }
        }

        // 其他资料
        // 1、投标人认为需要提供的其他资料
        if (detailMap.get("tbrrwxytgdqtzl") != null) {
            DocResponseEntDetail lsyjDetail = detailMap.get("tbrrwxytgdqtzl").get(0);
            vo.getContentMap().put("tbrrwxytgdqtzl", lsyjDetail.getDetailContent());
            setAtts(vo, lsyjDetail.getFilePath(), "TBRRWXYTGDQTZL");
            vo.getPdfFiles().put("、其他资料", lsyjDetail.getFilePath());
        }
        try {
            String pdfPath = procurementDocumentsUinfoService.prepareInquiryGoodsResponseDocuments(vo);
            String url = AttachmentUtil.realToUrl(pdfPath);
            System.out.println(url);
            info.setPdfPath(url);
            info.setBasePdfPath(url);
            info.setRemark(PdfUtil.getPageNum(pdfPath, ScoreItemKeywordEnum.getKeywordList(3,2)));
            updateById(info);
            return AjaxResult.success("操作成功", url);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }
    }


    @Override
    public JSONObject getPesponseDocPageByProject(Long projectId){
        JSONObject jo = new JSONObject();
        List<DocResponseEntInfo> infoList = selectByProject(projectId, false);
        for (DocResponseEntInfo info : infoList) {
            jo.put(info.getEntId().toString(), StringUtils.isNotBlank(info.getRemark())?JSONObject.parseObject(info.getRemark()):new JSONObject());
        }
        return jo;
    }

    @Override
    public AjaxResult createSecurityDocResponse(Long id) throws Exception {
        DocResponseEntInfo docResponseEntInfo = selectById(id);
        BusiTenderProject project = busiTenderProjectService.getAllById(docResponseEntInfo.getProjectId()).getTenderProject();
        BusiTenderNotice notice = busiTenderNoticeService.getTenderNoticeByProjectId(project.getProjectId());
        GenProjectResponseVo vo = new GenProjectResponseVo();
        vo.setProjectCode(project.getProjectCode());
        vo.setProjectName(project.getProjectName());
        vo.setProjectId(project.getProjectId().toString());
        vo.setDeadLine(DateUtils.getChineseDateTime(notice.getNoticeEndTime()));
        vo.setTenderMode(Integer.parseInt(project.getTenderMode()));
        vo.setTenderModeStr(project.getTenderModeName());
        vo.setTendererName(project.getTendererName());
        vo.setTendererPhone(project.getTendererPhone());
        if(StringUtils.isNoneBlank(project.getAgencyName())){
            vo.setAgencyName(project.getAgencyName());
            vo.setAgencyPhone(project.getAgencyPhone());
        }
        vo.setBidText(docResponseEntInfo.getPdfPath());
        List<BidInformation> bids = new ArrayList<>();
        for (String key : XmlConstants.COLUMN_MAP_NAME2CODE.keySet()) {
            BidInformation bi = new BidInformation();
            bi.setOpenBid(key);
            bi.setCode(XmlConstants.COLUMN_MAP_NAME2CODE.get(key));
            bi.setValue(key+"1");
            bids.add(bi);
        }
        vo.setBidInformation(bids);
        String zbwjPath = procurementDocumentsUinfoService.generateResponseZip(vo);
        String url = AttachmentUtil.realToUrl(zbwjPath);
        docResponseEntInfo.setFilePath(url);
        updateById(docResponseEntInfo);
        return AjaxResult.success("操作成功", url);

    }

    private void setAtts(ProcurementDocumentVo vo, String filePath, String itemType){
        if (StringUtils.isNoneBlank(filePath)) {
            String[] paths = filePath.split(",");
            for (String path : paths) {
                BusiAttachment a = new BusiAttachment();
                a.setFileType(itemType);
                a.setFilePath(AttachmentUtil.urlToReal(path));
                vo.getAttachments().add(a);
            }
        }
    }

    private QueryWrapper<DocResponseEntInfo> getInfoQueryWrapper(DocResponseEntInfo info) {
        QueryWrapper<DocResponseEntInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(info.getId()), "id", info.getId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(info.getDocResponseId()), "tender_mode", info.getDocResponseId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(info.getEntId()), "ent_id", info.getEntId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(info.getProjectId()), "project_id", info.getProjectId());

        return queryWrapper;
    }
    private
    Map<String, List<String>> initDetails(Long projectId){
        Map<String, List<String>> map = new HashMap<>();

        List<String> xycx = new ArrayList<>();
        xycx.add("中国政府采购网");
        xycx.add("中国执行信息公开网");
        xycx.add("信用中国");
        xycx.add("全国建筑市场监管公共服务平台");
        map.put("xycx", xycx);

        ScoringMethodUinfo uinfoQuery = new ScoringMethodUinfo();
        uinfoQuery.setProjectId(projectId);
        ScoringMethodUinfo uinfo = scoringMethodUinfoService.infoByParams(uinfoQuery);

        List<String> jsbf = new ArrayList<>();
        jsbf.add("施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划");
        jsbf.add("工程进度计划与措施、施工进度或施工网络图、施工总平面布置图");
        jsbf.add("节能减排（绿色施工、工艺创新）在本工程的具体应用措施");
        jsbf.add("新工艺（新技术、新设备、新材料）的采用程度");
        jsbf.add("风险管理措施");
        map.put("jsbf", jsbf);
        List<String> swbf = new ArrayList<>();
        swbf.add("以往业绩");
        swbf.add("体系认证");
        swbf.add("拟派项目管理人员");
        map.put("swbf", swbf);
        log.info(uinfo);
        return map;
    }

    private String removeTags(String html){
        if(StringUtils.isNoneBlank(html)) {
            org.jsoup.nodes.Document d = Jsoup.parse(html);
            org.jsoup.select.Elements paragraphs = d.select("p");
            StringBuilder textWithNewLines = new StringBuilder();
            for (Element p : paragraphs) {
                textWithNewLines.append(p.wholeText());
                textWithNewLines.append(System.lineSeparator()); // 添加换行
            }

            return textWithNewLines.toString();
        }
        return html;
    }

    public static void main(String[] args) {
        String html = "<p>范德萨范德萨范德萨</p><p>范德萨发达</p><p><br></p><p>rewrewrew</p><p><br></p><p><br></p><p>qwwwwee</p><p><br></p><p>vcxzvc</p>";
        org.jsoup.nodes.Document d = Jsoup.parse(html);
        Elements paragraphs = d.select("p");
        StringBuilder textWithNewLines = new StringBuilder();
        for (Element p : paragraphs) {
            textWithNewLines.append(p.wholeText());
            textWithNewLines.append(System.lineSeparator()); // 添加换行
        }

        System.out.println(textWithNewLines.toString());
    }



    @Override
    public AjaxResult createDocResponse1(JSONObject data) throws Exception {
        Long infoId = data.getLong("infoId");
        if (infoId==null) {
            throw new IllegalAccessException("无效的参数");
        }
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        BaseEntInfo baseEntInfo = sysUser.getEnt();
        DocResponseEntInfo docEntInfo = selectById(infoId);
        // 项目信息
        BusiTenderProject project = busiTenderProjectService.getById(docEntInfo.getProjectId());
        // 采购文件信息
        List<ProcurementDocumentsUitem> pUitemList = procurementDocumentsUitemMapper.selectByProjectId(docEntInfo.getProjectId());
        // 获取响应文件策略
        AbstractDocResponseStrategy docResponseStrategy = docResponseFactory.getDocResponse(docEntInfo.getDocResponseId());

        if ("1".equals(data.getString("isCheck"))) { // 系统校验
            Map<String, String> resultMsg = docResponseStrategy.checkAndDecision(data, docEntInfo, project, pUitemList);
            return AjaxResult.success("操作成功", resultMsg.get(ResponseDocEnum.RES_DOC_DECISION.getCode()));
        }
        if ("0".equals(data.getString("isCheck"))) { // 生成响应文件
            String url = docResponseStrategy.createDocResponse(data, baseEntInfo, docEntInfo, project);
            return AjaxResult.success("操作成功", url);
        }
        return AjaxResult.error("数据异常");
    }

    @Override
    public AjaxResult resDocReviewFactorsDecision(JSONObject data) throws Exception {
        Long projectId = data.getLong("projectId");
        Long entId = data.getLong("entId");
        if (projectId==null || entId==null) {
            throw new IllegalAccessException("无效的参数");
        }
        Map<String, String> map = new HashMap<>();
        // 项目信息
        BusiTenderProject project = busiTenderProjectService.getById(projectId);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq(true, "project_id", projectId);
        wrapper.eq(true, "ent_id", entId);
        DocResponseEntInfo docEntInfo = docResponseEntInfoMapper.selectOne(wrapper);
        DocResponseEntDetail query = new DocResponseEntDetail();
        query.setDocResponseEntId(docEntInfo.getId());
        Map<String, List<DocResponseEntDetail>> detailMap = docResponseEntDetailService.selectMap(query);
        docEntInfo.setDetailMap(detailMap);

        List<ProcurementDocumentsUitem> pUitemList = procurementDocumentsUitemMapper.selectByProjectId(docEntInfo.getProjectId());
        // 获取响应文件策略
        AbstractDocResponseStrategy docResponseStrategy = docResponseFactory.getDocResponse(docEntInfo.getDocResponseId());
        Map<String, String> resultMsg = docResponseStrategy.logicalDecision(project, pUitemList, docEntInfo);

        return AjaxResult.success(resultMsg);
    }






}

