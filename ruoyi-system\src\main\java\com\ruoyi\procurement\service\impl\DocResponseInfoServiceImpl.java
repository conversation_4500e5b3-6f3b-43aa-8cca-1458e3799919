package com.ruoyi.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.domain.DocResponseInfo;
import com.ruoyi.procurement.domain.DocResponseItem;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.mapper.DocResponseInfoMapper;
import com.ruoyi.procurement.service.IDocResponseInfoService;
import com.ruoyi.procurement.service.IDocResponseItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

//import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
//import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class DocResponseInfoServiceImpl extends ServiceImpl<DocResponseInfoMapper, DocResponseInfo> implements IDocResponseInfoService {

    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IDocResponseItemService docResponseItemService;

    @Override
    public List<DocResponseInfo> selectList(DocResponseInfo info) {
        QueryWrapper<DocResponseInfo> infoQueryWrapper = getInfoQueryWrapper(info);
        return list(infoQueryWrapper);
    }

    DocResponseInfo getByModeAndType(String tenderMode, String projectType) throws Exception{
        DocResponseInfo info = new DocResponseInfo();
        info.setTenderMode(tenderMode);
        info.setProjectType(projectType);
        List<DocResponseInfo> infoList = selectList(info);

        if (infoList == null || infoList.isEmpty()) {
            throw new RuntimeException("响应文件基础信息不存在");
        }
        if (infoList.size()>1) {
            throw new RuntimeException("响应文件基础信息数量异常");
        }
        return infoList.get(0);
    }

    @Override
    public DocResponseInfo getByProject(Long projectId) throws Exception {
        return getByProject(projectId, true);
    }

    @Override
    public DocResponseInfo getByProject(Long projectId, boolean haveItems) throws Exception {
        DocResponseInfo info = null;
        BusiTenderProject project = busiTenderProjectService.getById(projectId);
        if (project == null) {
            throw new RuntimeException("采购项目不存在");
        }
        info = getByModeAndType(project.getTenderMode(), project.getProjectType().toString());
        if(haveItems) {
            info.setDocResponseItemList(docResponseItemService.selectTree(info.getId()));
        }
        return info;
    }

    @Override
    public DocResponseInfo selectById(Long infoId) {
        DocResponseInfo info = getById(infoId);
        info.setDocResponseItemList(docResponseItemService.selectTree(infoId));
        return info;
    }

    private QueryWrapper<DocResponseInfo> getInfoQueryWrapper(DocResponseInfo info) {
        QueryWrapper<DocResponseInfo> infoQueryWrapper = new QueryWrapper<>();
        infoQueryWrapper.eq(ObjectUtil.isNotEmpty(info.getId()), "id", info.getId());
        infoQueryWrapper.eq(ObjectUtil.isNotEmpty(info.getTenderMode()), "tender_mode", info.getTenderMode());
        infoQueryWrapper.eq(ObjectUtil.isNotEmpty(info.getProjectType()), "project_type", info.getProjectType());

        return infoQueryWrapper;
    }
}

