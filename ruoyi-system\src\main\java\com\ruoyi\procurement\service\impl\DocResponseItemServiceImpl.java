package com.ruoyi.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.procurement.domain.DocResponseInfo;
import com.ruoyi.procurement.domain.DocResponseItem;
import com.ruoyi.procurement.mapper.DocResponseItemMapper;
import com.ruoyi.procurement.service.IDocResponseItemService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
//import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class DocResponseItemServiceImpl extends ServiceImpl<DocResponseItemMapper, DocResponseItem> implements IDocResponseItemService {


    @Override
    public List<DocResponseItem> selectList(DocResponseItem info) {
        QueryWrapper<DocResponseItem> queryWrapper = getQueryWrapper(info);
        return list(queryWrapper);
    }

    @Override
    public List<DocResponseItem> selectTree(DocResponseItem info) {
        List<DocResponseItem> itemList = selectList(info);
        Map<Long, DocResponseItem> itemMap = new HashMap<>();
        List<DocResponseItem> treeList = new ArrayList<>();
        for (DocResponseItem item : itemList) {
            itemMap.put(item.getId(), item);
            if (item.getPId() == null) {
                treeList.add(item);
            }
        }
        for (DocResponseItem item : itemList) {
            if (item.getPId() != null) {
                DocResponseItem i = itemMap.get(item.getPId());
                i.putChild(item);
            }
        }
        return treeList;
    }

    @Override
    public List<DocResponseItem> selectTree(Long infoId) {
        DocResponseItem info = new DocResponseItem();
        info.setDocResponseId(infoId);
        return selectTree(info);
    }

    @Override
    public Map<String, Long> getItemMap(Long infoId) {
        DocResponseItem query = new DocResponseItem();
        query.setDocResponseId(infoId);
        List<DocResponseItem> items = selectList(query);
        Map<String, Long> itemIdMap = new HashMap<>();
        for (DocResponseItem item : items) {
            itemIdMap.put(item.getItemCode(), item.getId());
        }
        return itemIdMap;
    }

    private QueryWrapper<DocResponseItem> getQueryWrapper(DocResponseItem query) {
        QueryWrapper<DocResponseItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getId()), "id", query.getId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getPId()), "p_id", query.getPId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getDocResponseId()), "doc_response_id", query.getDocResponseId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getItemCode()), "item_code", query.getItemCode());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getItemName()), "item_name", query.getItemName());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getItemLevel()), "item_level", query.getItemLevel());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getIsLeaf()), "is_leaf", query.getIsLeaf());
        if(ObjectUtil.isNotEmpty(query.getItemType())) {
            if(query.getItemType().equals("-1")){
                queryWrapper.ne("item_type", "0");
            }else{
                queryWrapper.eq("item_type", query.getItemType());
            }
        }
        queryWrapper.orderByAsc("item_level");
        return queryWrapper;
    }

}

