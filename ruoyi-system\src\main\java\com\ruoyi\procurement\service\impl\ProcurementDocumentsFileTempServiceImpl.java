package com.ruoyi.procurement.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.procurement.mapper.ProcurementDocumentsFileTempMapper;
import com.ruoyi.procurement.domain.ProcurementDocumentsFileTemp;
import com.ruoyi.procurement.service.IProcurementDocumentsFileTempService;

/**
 * 采购/响应文件模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Service
public class ProcurementDocumentsFileTempServiceImpl extends ServiceImpl<ProcurementDocumentsFileTempMapper, ProcurementDocumentsFileTemp> implements IProcurementDocumentsFileTempService {
    /**
     * 查询采购/响应文件模板列表
     *
     * @param procurementDocumentsFileTemp 采购/响应文件模板
     * @return 采购/响应文件模板
     */
    @Override
    public List<ProcurementDocumentsFileTemp> selectList(ProcurementDocumentsFileTemp procurementDocumentsFileTemp) {
        QueryWrapper<ProcurementDocumentsFileTemp> procurementDocumentsFileTempQueryWrapper = new QueryWrapper<>();
                        procurementDocumentsFileTempQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getTenderMode()),"tender_mode",procurementDocumentsFileTemp.getTenderMode());
                        procurementDocumentsFileTempQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getFileUrl()),"file_url",procurementDocumentsFileTemp.getFileUrl());
                        procurementDocumentsFileTempQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getProjectType()),"project_type",procurementDocumentsFileTemp.getProjectType());
                        procurementDocumentsFileTempQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getType()),"type",procurementDocumentsFileTemp.getType());
                        procurementDocumentsFileTempQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getDirPath()),"dir_path",procurementDocumentsFileTemp.getDirPath());
            procurementDocumentsFileTempQueryWrapper.apply(
                ObjectUtil.isNotEmpty(procurementDocumentsFileTemp.getParams().get("dataScope")),
        procurementDocumentsFileTemp.getParams().get("dataScope")+""
        );
        return list(procurementDocumentsFileTempQueryWrapper);
    }
}
