package com.ruoyi.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.procurement.mapper.ProcurementDocumentsInfoMapper;
import com.ruoyi.procurement.service.IProcurementDocumentsInfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsItemService;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采购文件编制基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ProcurementDocumentsInfoServiceImpl extends ServiceImpl<ProcurementDocumentsInfoMapper, ProcurementDocumentsInfo> implements IProcurementDocumentsInfoService {

    @Autowired
    IProcurementDocumentsItemService iProcurementDocumentsItemService;
    @Autowired
    IProcurementDocumentsUinfoService iProcurementDocumentsUinfoService;
    @Autowired
    IProcurementDocumentsUitemService iProcurementDocumentsUitemService;

    /**
     * 查询采购文件编制基础信息列表
     *
     * @param procurementDocumentsInfo 采购文件编制基础信息
     * @return 采购文件编制基础信息
     */
    @Override
    public List<ProcurementDocumentsInfo> selectList(ProcurementDocumentsInfo procurementDocumentsInfo) {
        QueryWrapper<ProcurementDocumentsInfo> procurementDocumentsInfoQueryWrapper = getProcurementDocumentsInfoQueryWrapper(procurementDocumentsInfo);
        return list(procurementDocumentsInfoQueryWrapper);
    }

    private QueryWrapper<ProcurementDocumentsInfo> getProcurementDocumentsInfoQueryWrapper(ProcurementDocumentsInfo procurementDocumentsInfo) {
        QueryWrapper<ProcurementDocumentsInfo> procurementDocumentsInfoQueryWrapper = new QueryWrapper<>();
        procurementDocumentsInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsInfo.getTenderMode()), "tender_mode", procurementDocumentsInfo.getTenderMode());
        procurementDocumentsInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsInfo.getProjectType()), "project_type", procurementDocumentsInfo.getProjectType());
        procurementDocumentsInfoQueryWrapper.eq("file_type", 1);
        String orderByDesc = (String) procurementDocumentsInfo.getParams().get("orderByDesc");
        if (StringUtils.isNotBlank(orderByDesc)) {
            procurementDocumentsInfoQueryWrapper.orderByDesc(orderByDesc);
        }
        procurementDocumentsInfoQueryWrapper.apply(
                ObjectUtil.isNotEmpty(procurementDocumentsInfo.getParams().get("dataScope")),
                procurementDocumentsInfo.getParams().get("dataScope") + ""
        );
        return procurementDocumentsInfoQueryWrapper;
    }

    @Override
    public ProcurementDocumentsInfo infoByParams(ProcurementDocumentsInfo procurementDocumentsInfo) {
        List<ProcurementDocumentsInfo> list = list(getProcurementDocumentsInfoQueryWrapper(procurementDocumentsInfo));
        if (list.size() > 0) {
            ProcurementDocumentsInfo data = list.get(0);
            String returnUinfo = (String) procurementDocumentsInfo.getParams().getOrDefault("returnUinfo","false");
            if(StringUtils.isNotBlank(returnUinfo) && returnUinfo.equals("true")){
                ProcurementDocumentsUinfo queryUinfo = new ProcurementDocumentsUinfo();
                Long entId = SecurityUtils.getLoginUser().getEntId();
                queryUinfo.setEntId(entId);
                String projectId = (String)procurementDocumentsInfo.getParams().get("projectId");
                queryUinfo.setProjectId(Long.parseLong(projectId));
                queryUinfo.setProjectFileId(data.getProjectFileId());
                ProcurementDocumentsUinfo uInfo = iProcurementDocumentsUinfoService.infoByParams(queryUinfo);
                if(ObjectUtil.isNotEmpty(uInfo)){
                    data.setUInfo(uInfo);
                }
            }
            data.setItems(iProcurementDocumentsItemService.listByInfoId(data.getProjectFileId()));
            return data;
        }
        return null;
    }
}
