package com.ruoyi.procurement.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.procurement.mapper.ProcurementDocumentsItemMapper;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import com.ruoyi.procurement.service.IProcurementDocumentsItemService;

/**
 * 采购文件编制详细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ProcurementDocumentsItemServiceImpl extends ServiceImpl<ProcurementDocumentsItemMapper, ProcurementDocumentsItem> implements IProcurementDocumentsItemService {
    /**
     * 查询采购文件编制详细信息列表
     *
     * @param procurementDocumentsItem 采购文件编制详细信息
     * @return 采购文件编制详细信息
     */
    @Override
    public List<ProcurementDocumentsItem> selectList(ProcurementDocumentsItem procurementDocumentsItem) {
        QueryWrapper<ProcurementDocumentsItem> procurementDocumentsItemQueryWrapper = new QueryWrapper<>();
                        procurementDocumentsItemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsItem.getProjectFileId()),"project_file_id",procurementDocumentsItem.getProjectFileId());
                        procurementDocumentsItemQueryWrapper.like(ObjectUtil.isNotEmpty(procurementDocumentsItem.getItemName()),"item_name",procurementDocumentsItem.getItemName());
                        procurementDocumentsItemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsItem.getItemCode()),"item_code",procurementDocumentsItem.getItemCode());
                        procurementDocumentsItemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsItem.getItemContent()),"item_content",procurementDocumentsItem.getItemContent());
            procurementDocumentsItemQueryWrapper.apply(
                ObjectUtil.isNotEmpty(procurementDocumentsItem.getParams().get("dataScope")),
        procurementDocumentsItem.getParams().get("dataScope")+""
        );
        return list(procurementDocumentsItemQueryWrapper);
    }

    @Override
    public List<ProcurementDocumentsItem> listByInfoId(Long projectFileId) {
        return list(new QueryWrapper<ProcurementDocumentsItem>().eq("project_file_id",projectFileId));
    }

    @Override
    public boolean save(ProcurementDocumentsItem entity) {
        super.save(entity);
        entity.setItemCode(generateItemCode(entity));
        return super.updateById(entity);
    }

    public String generateItemCode(ProcurementDocumentsItem entity) {
        StringBuffer sb = new StringBuffer();
        sb.append(entity.getProjectFileId());
        sb.append(UUID.randomUUID());
        sb.append(entity.getProjectFileItemId());
        sb.append(new Date().getTime());
        return Md5Utils.hash(sb.toString());
    }

    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("项目信息：");
        list.add("项目编号：");
        list.add("响应截止时间：");
        list.add("采购方式：");
        list.add("采购人：");
        list.add("采购人联系方式： @<el-input v-model=\"projectInfo.tendererPhone\" placeholder=\"请输入采购人联系方式\" ></el-input>");
        list.add("代理机构：");
        list.add("代理机构联系方式： @<el-input v-model=\"projectInfo.agencyPhone\" placeholder=\"请输入代理机构联系方式\" ></el-input>");
        System.out.println(JSONArray.from(list).toJSONString());
    }
}
