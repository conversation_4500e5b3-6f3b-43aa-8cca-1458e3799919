package com.ruoyi.procurement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.deepoove.poi.data.style.CellStyle;
import com.deepoove.poi.policy.AttachmentRenderPolicy;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.vo.XmlToEntityVo;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.busi.service.IBusiBiddingRecordService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.XmlConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.ImageUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.kaifangqian.service.image.PdfConvertImageService;
import com.ruoyi.kaifangqian.service.pojo.ConvertImage;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsItem;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.mapper.ProcurementDocumentsInfoMapper;
import com.ruoyi.procurement.mapper.ProcurementDocumentsItemMapper;
import com.ruoyi.procurement.mapper.ProcurementDocumentsUinfoMapper;
import com.ruoyi.procurement.service.IProcurementDocumentsInfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.procurement.service.IProcurementDocumentsUitemService;
import com.ruoyi.procurement.vo.*;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.mapper.ScoringMethodInfoMapper;
import com.ruoyi.scoring.mapper.ScoringMethodItemMapper;
import com.ruoyi.scoring.mapper.ScoringMethodUinfoMapper;
import com.ruoyi.scoring.mapper.ScoringMethodUitemMapper;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.utils.*;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.ss.usermodel.*;
import java.io.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

//import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
//import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Log4j2
@Service
public class ProcurementDocumentsUinfoServiceImpl extends ServiceImpl<ProcurementDocumentsUinfoMapper, ProcurementDocumentsUinfo> implements IProcurementDocumentsUinfoService {
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;
    @Autowired
    ProcurementDocumentsInfoMapper procurementDocumentsInfoMapper;
    @Autowired
    ProcurementDocumentsItemMapper procurementDocumentsItemMapper;
    @Autowired
    ScoringMethodInfoMapper scoringMethodInfoMapper;
    @Autowired
    ScoringMethodItemMapper scoringMethodItemMapper;
    @Autowired
    ScoringMethodUinfoMapper scoringMethodUinfoMapper;
    @Autowired
    ScoringMethodUitemMapper scoringMethodUitemMapper;

    @Autowired
    IProcurementDocumentsUitemService iProcurementDocumentsUitemService;
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    IBaseEntInfoService iBaseEntInfoService;
    @Autowired
    IProcurementDocumentsInfoService procurementDocumentsInfoService;
    @Autowired
    IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    ISysConfigService iSysConfigService;
    @Autowired
    PdfConvertImageService pdfConvertImageService;

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 查询用户编制采购文件信息保存列表
     *
     * @param procurementDocumentsUinfo 用户编制采购文件信息保存
     * @return 用户编制采购文件信息保存
     */
    @Override
    public List<ProcurementDocumentsUinfo> selectList(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        QueryWrapper<ProcurementDocumentsUinfo> procurementDocumentsUinfoQueryWrapper = getProcurementDocumentsUinfoQueryWrapper(procurementDocumentsUinfo);
        return list(procurementDocumentsUinfoQueryWrapper);
    }

    private QueryWrapper<ProcurementDocumentsUinfo> getProcurementDocumentsUinfoQueryWrapper(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        QueryWrapper<ProcurementDocumentsUinfo> procurementDocumentsUinfoQueryWrapper = new QueryWrapper<>();
        procurementDocumentsUinfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUinfo.getProjectFileId()), "project_file_id", procurementDocumentsUinfo.getProjectFileId());
        procurementDocumentsUinfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUinfo.getEntId()), "ent_id", procurementDocumentsUinfo.getEntId());
        procurementDocumentsUinfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUinfo.getProjectId()), "project_id", procurementDocumentsUinfo.getProjectId());
        procurementDocumentsUinfoQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUinfo.getFilePath()), "file_path", procurementDocumentsUinfo.getFilePath());
        procurementDocumentsUinfoQueryWrapper.apply(ObjectUtil.isNotEmpty(procurementDocumentsUinfo.getParams().get("dataScope")), procurementDocumentsUinfo.getParams().get("dataScope") + "");
        return procurementDocumentsUinfoQueryWrapper;
    }

    @Override
    public ProcurementDocumentsUinfo selectByProject(Long projectId) {
        ProcurementDocumentsUinfo procurementDocumentsUinfo = new ProcurementDocumentsUinfo();
        procurementDocumentsUinfo.setProjectId(projectId);
        List<ProcurementDocumentsUinfo> uinfoList = selectList(procurementDocumentsUinfo);
        if (uinfoList != null && uinfoList.size() == 1) {
            return uinfoList.get(0);
        }
        return null;
    }

    @Transactional
    @Override
    public ProcurementDocumentsUinfo saveInfo(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        //竞争性谈判(无评分方法) 0  竞争性磋商 1  询价（只有货物） 3  单一来源（暂时不做） 4
        Long entId = SecurityUtils.getLoginUser().getEntId();
        procurementDocumentsUinfo.setEntId(entId);
        ProcurementDocumentsUinfo uinfo = selectByProject(procurementDocumentsUinfo.getProjectId());
        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentsUinfo.getProjectId());
        if (uinfo == null) {
            ProcurementDocumentsInfo queryInfo = new ProcurementDocumentsInfo();
            queryInfo.setTenderMode(Integer.valueOf(project.getTenderMode()));
            queryInfo.setProjectType(Integer.valueOf(project.getProjectType()));
            List<ProcurementDocumentsInfo> infos = procurementDocumentsInfoService.selectList(queryInfo);
            if (infos != null && infos.size() == 1) {
                ProcurementDocumentsInfo info = infos.get(0);
                procurementDocumentsUinfo.setProjectFileId(info.getProjectFileId());
            }
            saveOrUpdate(procurementDocumentsUinfo);
            if (!project.getTenderMode().equals("0")){
                scoringMethodUitemService.initNewProjectUitem(procurementDocumentsUinfo.getProjectId());
            }
        }
        List<ProcurementDocumentsUitem> uItems = procurementDocumentsUinfo.getUItems();
        if (ObjectUtil.isNotEmpty(uItems)) {
            uItems.forEach(item -> {
                item.setEntFileId(procurementDocumentsUinfo.getEntFileId());
                item.setEntId(entId);
            });
            iProcurementDocumentsUitemService.saveOrUpdateBatch(uItems);
            if ("工程量清单及图纸".equals(uItems.get(0).getItemName())) {
                BusiTenderNotice notice = iBusiTenderNoticeService.selectByProject(procurementDocumentsUinfo.getProjectId());
                boolean b = iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "0");
                BusiAttachment attachment = new BusiAttachment();
                attachment.setBusiId(notice.getNoticeId());
                attachment.setFileName("工程量清单及图纸");
                attachment.setFileType("0");
                attachment.setFilePath(uItems.get(0).getItemContent());
                attachment.setFileSuffix(FileUploadUtils.getExtension(attachment.getFilePath()));
                iBusiAttachmentService.save(attachment);
            }
            if ("采购需求".equals(uItems.get(0).getItemName())) {
                BusiTenderNotice notice = iBusiTenderNoticeService.selectByProject(procurementDocumentsUinfo.getProjectId());
                boolean b = iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "0");

                JSONObject jo = JSONObject.parse(uItems.get(0).getItemContent());
                if (project.getTenderMode().equals("1")){
                    if(jo!=null && jo.get("quantityParamReq")!=null && jo.getJSONObject("quantityParamReq").get("wordUrl")!=null){
                        String wordUrl = jo.getJSONObject("quantityParamReq").getString("wordUrl");
                        if(StringUtils.isNoneBlank(wordUrl)) {
                            String[] urls = wordUrl.split(",");
                            for (String url : urls) {
                                BusiAttachment attachment = new BusiAttachment();
                                attachment.setBusiId(notice.getNoticeId());
                                attachment.setFileName("采购需求模板");
                                attachment.setFileType("0");
                                attachment.setFilePath(url);
                                //  String modifiedString = modifyFileName(url, oldFileName, newFileName);
                                attachment.setFileSuffix(FileUploadUtils.getExtension(attachment.getFilePath()));
                                iBusiAttachmentService.save(attachment);
                            }
                        }
                    }
                }
                if (project.getTenderMode().equals("3")){
                    if(jo!=null && jo.get("paramReq")!=null && jo.getJSONObject("paramReq").get("requirement")!=null){
                        String excelUrl = jo.getJSONObject("paramReq").getString("requirement");
                        if(StringUtils.isNoneBlank(excelUrl)) {
//                            BusiTenderNotice notice = iBusiTenderNoticeService.selectByProject(procurementDocumentsUinfo.getProjectId());
//                            boolean b = iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "0");
                            String[] urls = excelUrl.split(",");
                            for (String url : urls) {
                                String excelFilePath = AttachmentUtil.urlToReal(url);
                                String newFilePath = FileReNameUtil.renameAndSaveFile(excelFilePath, project.getProjectName() + "_偏离表");
                                BusiAttachment attachment = new BusiAttachment();
                                attachment.setBusiId(notice.getNoticeId());
                                attachment.setFileName(project.getProjectName() + "_偏离表");
                                attachment.setFileType("0");
                                attachment.setFilePath(AttachmentUtil.realToUrl(newFilePath));
                                attachment.setFileSuffix(FileUploadUtils.getExtension(attachment.getFilePath()));
                                iBusiAttachmentService.save(attachment);
                            }
                        }
                    }
                    if(jo!=null && jo.get("substantiveReq")!=null && jo.getJSONObject("substantiveReq").get("requirement")!=null){
                        String excelUrl = jo.getJSONObject("substantiveReq").getString("requirement");
                        if(StringUtils.isNoneBlank(excelUrl)) {
//                            BusiTenderNotice notice = iBusiTenderNoticeService.selectByProject(procurementDocumentsUinfo.getProjectId());
//                            boolean b = iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "0");
                            String[] urls = excelUrl.split(",");
                            for (String url : urls) {
                                String excelFilePath = AttachmentUtil.urlToReal(url);
                                String newFilePath = FileReNameUtil.renameAndSaveFile(excelFilePath, project.getProjectName() + "_采购需求");
                                BusiAttachment attachment = new BusiAttachment();
                                attachment.setBusiId(notice.getNoticeId());
                                attachment.setFileName(project.getProjectName()+"_采购需求");
                                attachment.setFileType("0");
                                attachment.setFilePath(AttachmentUtil.realToUrl(newFilePath));
                                attachment.setFileSuffix(FileUploadUtils.getExtension(attachment.getFilePath()));
                                iBusiAttachmentService.save(attachment);
                            }
                        }
                    }
                    //明细报价表
                    if(jo!=null && jo.get("quantityParamReq")!=null && jo.getJSONObject("quantityParamReq").get("requirement")!=null){
                        String wordUrl = jo.getJSONObject("quantityParamReq").getString("requirement");
                        if(StringUtils.isNoneBlank(wordUrl)) {
                            String[] urls = wordUrl.split(",");
                            for (String url : urls) {
                                String excelFilePath = AttachmentUtil.urlToReal(url);
                                String newFilePath = FileReNameUtil.renameAndSaveFile(excelFilePath, project.getProjectName() + "_明细报价表");
                                BusiAttachment attachment = new BusiAttachment();
                                attachment.setBusiId(notice.getNoticeId());
                                attachment.setFileName(project.getProjectName() + "_明细报价表");
                                attachment.setFileType("0");
                                attachment.setFilePath(AttachmentUtil.realToUrl(newFilePath));
                                //  String modifiedString = modifyFileName(url, oldFileName, newFileName);
                                attachment.setFileSuffix(FileUploadUtils.getExtension(attachment.getFilePath()));
                                iBusiAttachmentService.save(attachment);
                            }
                        }
                    }

                }
            }
        }
        return procurementDocumentsUinfo;
    }

    @Override
    public ProcurementDocumentsUinfo getInfoById(Long entFileId) {
        ProcurementDocumentsUinfo uInfo = getById(entFileId);
        uInfo.setUItems(iProcurementDocumentsUitemService.listByInfoId(entFileId));
        return uInfo;
    }

    @Override
    public ProcurementDocumentsUinfo infoByParams(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        List<ProcurementDocumentsUinfo> list = list(getProcurementDocumentsUinfoQueryWrapper(procurementDocumentsUinfo));
        if (list.size() > 0) {
            ProcurementDocumentsUinfo data = list.get(0);
            data.setUItems(iProcurementDocumentsUitemService.listByInfoId(data.getEntFileId()));
            return data;
        }
        return null;
    }

    public static Boolean checkJson(String itemContent) {
        if (itemContent == null || itemContent.isEmpty()) {
            System.out.println("itemContent 是空字符串或者null");
            return false;
        } else if (itemContent.equals("[]")) {
            System.out.println("itemContent 是空数组 []");
            return false;
        } else if (itemContent.equals("{}")) {
            System.out.println("itemContent 是空对象 {}");
            return false;
        } else if (itemContent.equals("[{}]")) {
            System.out.println("itemContent 是一个包含空对象的数组 [{}]");
            return false;
        } else {
            try {
                JSONObject jsonObject = JSONObject.parseObject(itemContent);
                if (jsonObject.isEmpty()) {
                    System.out.println("itemContent 是一个空对象 {}");
                    return false;
                }
            } catch (Exception e) {
                try {
                    JSONArray jsonArray = JSONArray.from(itemContent);
                    if (jsonArray.isEmpty()) {
                        System.out.println("itemContent 是一个空数组 []");
                        return false;
                    }
                } catch (Exception e2) {
                    System.out.println("itemContent 不是有效的JSON");
                    if (StringUtils.isBlank(itemContent)) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public CheckisOverExpVo checkIsOver(ProcurementDocumentsUinfo procurementDocumentsUinfo) {
        List<CheckIsOverVo> result = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //1.查询模板
        ProcurementDocumentsInfo info = procurementDocumentsInfoMapper.selectById(procurementDocumentsUinfo.getProjectFileId());
        if (ObjectUtil.isEmpty(info)) {
            throw new ServiceException("模板信息错误，请重新进入该页面");
        }
        //2.查询模板item
        List<ProcurementDocumentsItem> items = procurementDocumentsItemMapper.selectList(new QueryWrapper<ProcurementDocumentsItem>().eq("project_file_id", info.getProjectFileId()));
        if (ObjectUtil.isEmpty(items)) {
            throw new ServiceException("模板信息错误，请重新进入该页面");
        }
        Map<Long, ProcurementDocumentsItem> itemsMap = items.stream().collect(Collectors.toMap(ProcurementDocumentsItem::getProjectFileItemId, item -> item, (existing, replacement) -> existing)); // 在键冲突时保留现有的元素

        //3.查询自己info
        ProcurementDocumentsUinfo uInfo = getOne(new QueryWrapper<ProcurementDocumentsUinfo>().eq("project_id", procurementDocumentsUinfo.getProjectId()).eq("project_file_id", info.getProjectFileId()).eq("ent_id", loginUser.getEntId()));
        if (ObjectUtil.isEmpty(uInfo)) {
            result.add(new CheckIsOverVo().setResult(1).setFunctionPoint("唱标内容").setContent("未创建基本信息"));
            throw new ServiceException("未查询到基本信息");
        }
        //4.查询自己item
        List<ProcurementDocumentsUitem> uItems = iProcurementDocumentsUitemService.listByInfoId(uInfo.getEntFileId());
        if (ObjectUtil.isEmpty(uItems)) {
            result.add(new CheckIsOverVo().setResult(1).setFunctionPoint("唱标内容").setContent("未创建基本信息"));
        }
        //5.查出未维护的
        List<ProcurementDocumentsItem> noMatchList = items.stream().filter(item -> uItems.stream().noneMatch(uItem -> uItem.getProjectFileItemId().equals(item.getProjectFileItemId()))).collect(Collectors.toList());
        if (items.size() == uItems.size()) {
            //查询数据是否维护
            uItems.forEach(item -> {
                String itemContent = item.getItemContent();
                if (!checkJson(itemContent)) {
                    ProcurementDocumentsItem procurementDocumentsItem = itemsMap.get(item.getProjectFileItemId());
                    if (procurementDocumentsItem.getItemName().equals("采购文件正文")) {
                        result.add(new CheckIsOverVo().setSort(3).setResult(1).setFunctionPoint("采购文件正文").setContent(procurementDocumentsItem.getItemName() + "需求未完善"));
                    } else {
                        result.add(new CheckIsOverVo().setSort(1).setResult(1).setFunctionPoint("唱标内容").setContent(procurementDocumentsItem.getItemName() + "需求未完善"));
                    }
                }
            });
        }
        if (items.size() != uItems.size()) {
            noMatchList.forEach(item -> {
                if (item.getItemName().equals("采购文件正文")) {
                    result.add(new CheckIsOverVo().setSort(3).setResult(1).setFunctionPoint("采购文件正文").setContent(item.getItemName() + "需求未完善"));
                } else {
                    result.add(new CheckIsOverVo().setSort(1).setResult(1).setFunctionPoint("唱标内容").setContent(item.getItemName() + "需求未完善"));
                }
            });
        }
        //查询评分是否完善
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(procurementDocumentsUinfo.getProjectId());
        if (!tenderProject.getTenderMode().equals("3")){
            // 遍历数组，寻找itemName等于"评分办法"的对象
            ProcurementDocumentsUitem foundItem = null;
            for (ProcurementDocumentsUitem item : uItems) {
                ProcurementDocumentsItem procurementDocumentsItem = itemsMap.get(item.getProjectFileItemId());
                if ("评分办法".equals(procurementDocumentsItem.getItemName())) {
                    foundItem = item;
                    break; // 找到后退出循环
                }
            }

            // 打印结果
            if (foundItem != null) {
                //查询出scoring
                String itemContent = foundItem.getItemContent();
                if (!checkJson(itemContent)) {
                    result.add(new CheckIsOverVo().setResult(2).setFunctionPoint("评分办法").setContent("未完善"));
                }
                ScoringMethodInfo scoringInfo = scoringMethodInfoMapper.selectById(itemContent);
                if (ObjectUtil.isEmpty(scoringInfo)) {
                    throw new ServiceException("评分办法模板信息错误，请重新进入该页面");
                }
                //查询scoringitem
                List<ScoringMethodItem> scoringItems = scoringMethodItemMapper.selectList(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", itemContent));
                if (ObjectUtil.isEmpty(scoringItems)) {
                    throw new ServiceException("评分办法模板信息错误，请重新进入该页面");
                }
                //查询uscoring
                ScoringMethodUinfo scoringUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>()
                        .eq("project_id", procurementDocumentsUinfo.getProjectId()).eq("scoring_method_id", scoringInfo.getScoringMethodId()).eq("ent_id", loginUser.getEntId()));
                if (ObjectUtil.isEmpty(scoringUinfo)) {
                    result.add(new CheckIsOverVo().setSort(2).setResult(1).setFunctionPoint("评分办法").setContent("需求未完善"));
                }
                scoringItems.forEach(item -> {
                    //查询出scoringitem
                    List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemMapper.selectList(new QueryWrapper<ScoringMethodUitem>().eq("scoring_method_item_id", item.getScoringMethodItemId()).eq("ent_id", loginUser.getEntId()));
                    if (ObjectUtil.isEmpty(scoringMethodUitems)) {
                        result.add(new CheckIsOverVo().setSort(2).setResult(1).setFunctionPoint("评分办法").setContent(item.getItemName() + "需求未完善"));
                    }
                });
            } else {
                throw new ServiceException("评分办法模板信息错误，请重新进入该页面");
            }

            boolean pfbf = result.stream().anyMatch(f -> "评分办法".equals(f.getFunctionPoint()));
            if (!pfbf) {
                result.add(new CheckIsOverVo().setSort(2).setResult(0).setFunctionPoint("评分办法").setContent("正常"));
            }
        }

        boolean cbnr = result.stream().anyMatch(f -> "唱标内容".equals(f.getFunctionPoint()));
        if (!cbnr) {
            result.add(new CheckIsOverVo().setSort(1).setResult(0).setFunctionPoint("唱标内容").setContent("正常"));
        }
        boolean zbwjnr = result.stream().anyMatch(f -> "采购文件正文".equals(f.getFunctionPoint()));
        if (!zbwjnr) {
            result.add(new CheckIsOverVo().setSort(3).setResult(0).setFunctionPoint("采购文件正文").setContent("正常"));
        }
        CheckisOverExpVo vo = new CheckisOverExpVo();
        //原始
        vo.setOriginal(result);
        //存储使用
        // 使用流来保留具有相同name属性值的最后一个元素
        List<CheckIsOverVo> uniqueData = result.stream()
                // 使用Collectors.toMap来创建一个Map，其中键是name，值是CheckIsOverVo对象
                // 如果键相同，则使用第二个参数来决定保留哪个值（在这个例子中，我们保留第二个，即最后一个）
                .collect(Collectors.toMap(CheckIsOverVo::getFunctionPoint, item -> item, (existing, replacement) -> replacement))
                // 将Map的值转换回List
                .values().stream().sorted(Comparator.comparingInt(item -> item.getSort())).collect(Collectors.toList());
        vo.setUniqueData(uniqueData);
        return vo;
    }

    @Override
    public AjaxResult generateProjectFileZip(ProcurementDocumentsUinfo queryUinfo) throws IOException {
        String[] filePathList = new String[3];
        AjaxResult success = AjaxResult.success();
        // 上传文件路径
        String filePath = RuoYiConfig.getUploadPath();
        JSONObject projectInfoJson = new JSONObject();
        // 查询  projectInfo  项目基础信息  评分办法
        ProcurementDocumentsUinfo uInfo = getById(queryUinfo.getEntFileId());
        //查询项目
        BusiTenderProject projectInfo = iBusiTenderProjectService.getById(uInfo.getProjectId());
        projectInfoJson.put("projectInfo", BeanUtil.beanToMap(projectInfo, false, false));
        // 查询 uItem
        String downItemListName = (String) queryUinfo.getParams().get("downItemListName");
        if (StringUtils.isBlank(downItemListName)) {
            throw new ServiceException("请传入需要生成xml文件的子项名称");
        }
        String[] itemNameList = downItemListName.split(",");
        //查询item
        List<ProcurementDocumentsItem> items = procurementDocumentsItemMapper.selectList(new QueryWrapper<ProcurementDocumentsItem>().in("item_name", itemNameList).eq("project_file_id", uInfo.getProjectFileId()));
        List<Long> projectFileItemIds = items.stream().map(item -> {
            return item.getProjectFileItemId();
        }).collect(Collectors.toList());
        Map<Long, ProcurementDocumentsItem> itemMap = items.stream().collect(Collectors.toMap(ProcurementDocumentsItem::getProjectFileItemId,        // keyMapper
                item -> item,                       // valueMapper
                (existing, replacement) -> existing  // mergeFunction
        ));
        List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.list(new QueryWrapper<ProcurementDocumentsUitem>().eq("ent_file_id", uInfo.getEntFileId()).in("project_file_item_id", projectFileItemIds));
        uitems.forEach(item -> {
            ProcurementDocumentsItem procurementDocumentsItem = itemMap.get(item.getProjectFileItemId());
            if (procurementDocumentsItem.getItemName().equals("项目基本信息")) {
                projectInfoJson.put("basicInfo", BeanUtil.beanToMap(item, false, false));
            } else if (procurementDocumentsItem.getItemName().equals("开标一览表")) {
                projectInfoJson.put("bidInformation", BeanUtil.beanToMap(item, false, false));
            } else if (procurementDocumentsItem.getItemName().equals("评分办法")) {

            } else if (procurementDocumentsItem.getItemName().equals("采购文件正文")) {

            }
        });
        //生成文件
        String projectXmlPath = FileUploadUtils.extractFilename(filePath, "project.xml", projectInfo.getProjectId().toString(), "", false);
        try {
            XmlUtil.json2XmlFile(projectXmlPath, projectInfoJson);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("生成项目信息.xml失败");
        }
        List<ProcurementDocumentsUitem> noMatchScoring = uitems.stream().filter(item -> {
            ProcurementDocumentsItem procurementDocumentsItem = itemMap.get(item.getProjectFileItemId());
            return !procurementDocumentsItem.getItemName().equals("评分办法") && !procurementDocumentsItem.getItemName().equals("采购文件正文");
        }).map(item -> {
            item.setFilePath(projectXmlPath);
            return item;
        }).collect(Collectors.toList());
        iProcurementDocumentsUitemService.updateBatchById(noMatchScoring);
        filePathList[0] = projectXmlPath;
        //评分办法
        ProcurementDocumentsUitem uItemScoring = uitems.stream().filter(item -> {
            ProcurementDocumentsItem procurementDocumentsItem = itemMap.get(item.getProjectFileItemId());
            return procurementDocumentsItem.getItemName().equals("评分办法");
        }).findFirst().orElse(null);
        if (uItemScoring == null) {
            throw new ServiceException("评分办法不存在");
        }
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectInfo.getProjectId())
                .eq("scoring_method_id", uItemScoring.getItemContent()));
        //查询item项
        List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemMapper.selectList(new QueryWrapper<ScoringMethodUitem>().eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));
        if (ObjectUtil.isEmpty(scoringMethodUitems)) {
            throw new ServiceException("评分办法项不存在");
        }
        projectInfoJson.put("scoringUitems", scoringMethodUitems);
        String scoringXmlFliePath = FileUploadUtils.extractFilename(filePath, "scoringInfo.xml", projectInfo.getProjectId().toString(), "", false);
        //生成文件
        try {
            XmlUtil.json2XmlFile(scoringXmlFliePath, projectInfoJson);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("生成项目信息.xml失败");
        }
        uItemScoring.setFilePath(scoringXmlFliePath);
        iProcurementDocumentsUitemService.updateById(uItemScoring);
        filePathList[1] = scoringXmlFliePath;
        // 拿到附件
//        ProcurementDocumentsUitem bidTxt = uitems.stream().filter(item -> {
//            ProcurementDocumentsItem procurementDocumentsItem = itemMap.get(item.getProjectFileItemId());
//            return procurementDocumentsItem.getItemName().equals("采购文件正文");
//        }).findFirst().orElse(null);
//        if (bidTxt == null) {
//            throw new ServiceException("采购文件正文不存在");
//        }
//        String bidTxtPath = bidTxt.getItemContent();
//
//        String[] split = bidTxtPath.split("upload/");
//
//        // 使用最后一个"/"作为分隔符
//        String[] parts = split[1].split("/");
//
//        String bidTxtDate = "";
//        if (parts.length > 1) {
//            // 剩余部分组成日期
//            bidTxtDate = String.join("/", Arrays.copyOfRange(parts, 0, parts.length - 1));
//        } else {
//            throw new ServiceException("无法拆分采购文件正文路径。");
//        }
//        String part = parts[parts.length - 1];
//        String pdfPath = FileUploadUtils.extractFilename(filePath, part, "", bidTxtDate, false);
//        String[] split1 = part.split("\\.");
//        String s1 = pdfPath.replaceAll("\\\\", "/");
//        String[] split3 = s1.split("/");
//        split3[split3.length - 1] = projectInfo.getProjectId() + "/bidText." + split1[split1.length - 1];
//        String newPdfPath = StrUtil.join("/", split3);
//        FileUtil.copy(pdfPath, newPdfPath, true);
        BusiTenderNotice notice = iBusiTenderNoticeService.getTenderNoticeByProjectId(uInfo.getProjectId());
        List<BusiAttachment> pdfPaths = iBusiAttachmentService.selectByBusiId(notice.getNoticeId(), "5");
        if (pdfPaths != null && pdfPaths.size() == 1) {
            filePathList[2] = AttachmentUtil.urlToReal(pdfPaths.get(0).getFilePath());
        }
        String projectInfoZipPath = FileUploadUtils.extractFilename(filePath, "project.zbwj", projectInfo.getProjectId().toString(), "", false);
        try {
            ZipUtil.createZip(projectInfoZipPath, filePathList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //成功后修改uinfo和uitem的filePath
        uInfo.setFilePath(projectInfoZipPath);
        updateById(uInfo);
        //保存附件

        String noticeId = (String) queryUinfo.getParams().get("noticeId");
        BusiTenderNotice busiTenderNotice = iBusiTenderNoticeService.getById(Long.parseLong(noticeId));
        BusiAttachment item = new BusiAttachment();
        item.setBusiId(busiTenderNotice.getNoticeId());
        item.setDelFlag(DelFlagStatus.OK.getCode());
        item.setCreateBy(busiTenderNotice.getCreateBy());
        item.setUpdateBy(busiTenderNotice.getUpdateBy());
        item.setUpdateTime(busiTenderNotice.getUpdateTime());
        item.setCreateTime(busiTenderNotice.getCreateTime());

        String name = FileUtils.getName(projectInfoZipPath);
        item.setFileName(name);
        item.setFileType("0");
        String[] split2 = name.split("\\.");
        item.setFileSuffix(split2[split2.length - 1]);
        // 原始文件路径

        int upload = projectInfoZipPath.lastIndexOf("upload");
        String filePath1 = "/profile/upload" + projectInfoZipPath.substring(upload + 6);
        String s = filePath1.replaceAll("\\\\", "/");
        item.setFilePath(s);
        BusiAttachment one = iBusiAttachmentService.getOne(new QueryWrapper<BusiAttachment>()
                .eq("busi_id", busiTenderNotice.getNoticeId())
                .eq("file_name", name)
                .eq("file_type", 0)
                .eq("file_suffix", item.getFileSuffix()));
        if (one != null) {
            item.setAttachmentId(one.getAttachmentId());
        }
        iBusiAttachmentService.saveOrUpdate(item);


        return success.put("path", projectInfoZipPath).put("data", item);
    }

    @Override
    public String generateResponseZip(GenProjectResponseVo genProjectResponseVo) throws Exception {
        XmlToEntityVo vo = new XmlToEntityVo();
        List<XmlToEntityVo.ProjectInfoBean> projectInfoBeans = new ArrayList<>();
        List<XmlToEntityVo.BidAnnounceBean> bidAnnounceBeans = new ArrayList<>();
        List<XmlToEntityVo.BidInfoBean> bidInfoBeans = new ArrayList<>();
        BaseEntInfo ent = SecurityUtils.getLoginUser().getUser().getEnt();
        bidAnnounceBeans.add(new XmlToEntityVo.BidAnnounceBean().setCode("bidder").setName("投标人").setValue(ent.getEntName()).setRemark("投标企业名称"));
        bidInfoBeans.add(new XmlToEntityVo.BidInfoBean().setCode("bidder").setName("投标人").setValue(ent.getEntName()).setRemark("投标企业名称"));
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(genProjectResponseVo, false, false);
        stringObjectMap.forEach((key, value) -> {
            if (key.equals("projectCode") || key.equals("projectName")) {
                projectInfoBeans.add(new XmlToEntityVo.ProjectInfoBean().setName(XmlConstants.COLUMN_MAP_CODE2NAME.get(key)).setCode(key).setValue(value.toString()).setRemark(""));
            }
            if (key.equals("bidInformation")) {
                List<BidInformation> bidInformations = (List<BidInformation>) value;
                bidInformations.forEach(item -> {
                    if (XmlConstants.COLUMN_MAP_NAME2CODE.containsKey(item.getOpenBid())) {
                        bidAnnounceBeans.add(new XmlToEntityVo.BidAnnounceBean().setCode(XmlConstants.COLUMN_MAP_NAME2CODE.get(item.getOpenBid())).setName(item.getOpenBid()).setValue(item.getValue()).setRemark(item.getRemark()));
                        bidInfoBeans.add(new XmlToEntityVo.BidInfoBean().setCode(XmlConstants.COLUMN_MAP_NAME2CODE.get(item.getOpenBid())).setName(item.getOpenBid()).setValue(item.getValue()).setRemark(item.getRemark()));
                    } else {
                        bidAnnounceBeans.add(new XmlToEntityVo.BidAnnounceBean().setCode("").setName(item.getOpenBid()).setValue(item.getValue()).setRemark(item.getRemark()));
                        bidInfoBeans.add(new XmlToEntityVo.BidInfoBean().setCode("").setName(item.getOpenBid()).setValue(item.getValue()).setRemark(item.getRemark()));
                    }
                });
            }
        });

        vo.setVersion("1");
        vo.setProjectInfos(projectInfoBeans);
        vo.setInfoBeans(bidInfoBeans);
        vo.setBidAnnounceBeans(bidAnnounceBeans);

        String[] filePathList = new String[2];
        String filePath = RuoYiConfig.getUploadPath();
        String projectInfoXmlPath = FileUploadUtils.extractFilename(filePath, "responseProject.xml", genProjectResponseVo.getProjectId(), "", false);
        JSONObject from = JSONObject.from(vo);
        XmlUtil.json2XmlFile(projectInfoXmlPath, from);
        filePathList[0] = projectInfoXmlPath;
        String bidTxtPath = genProjectResponseVo.getBidText();
        String[] split = bidTxtPath.split("upload/");
        // 使用最后一个"/"作为分隔符
        String[] parts = split[1].split("/");
        String bidTxtDate = "";
        if (parts.length > 1) {
            // 剩余部分组成日期
            bidTxtDate = String.join("\\/", Arrays.copyOfRange(parts, 0, parts.length - 1));
        } else {
            throw new ServiceException("无法拆分采购文件正文路径。");
        }

        String part = parts[parts.length - 1];
        String pdfPath = FileUploadUtils.extractFilename(filePath, part, "", bidTxtDate, false);
        String[] split1 = part.split("\\.");
        parts[parts.length - 1] = genProjectResponseVo.getProjectId() + "/responseBidTxt." + split1[split1.length - 1];
        String newPdfPath = StrUtil.join("/", parts);
        String s = pdfPath.replaceAll("\\\\", "/");
        String[] split2 = s.split("upload/");
        split2[split2.length - 1] = newPdfPath;
        String join = StrUtil.join("upload/", split2);
        FileUtil.copy(pdfPath, join, true);
        filePathList[1] = join;
        String projectInfoZipPath = FileUploadUtils.extractFilename(filePath, genProjectResponseVo.getProjectName()+"-响应文件.zip", genProjectResponseVo.getProjectId(), "", false);
        try {
            ZipUtil.createZip(projectInfoZipPath, filePathList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String s1 = projectInfoZipPath.replaceAll(".zip", ".tbwj");
        BaseEntInfo ents = iBaseEntInfoService.getById(SecurityUtils.getLoginUser().getEntId());
        String secretKey = ents.getSecretKey();
        if (StringUtils.isBlank(secretKey)) {
            throw new ServiceException("请维护二级密码");
        }
        ZipUtil.encrypt(projectInfoZipPath, s1, Md5Utils.hash(ents.getEntCode()).substring(0, 16));
        FileUtils.deleteFile(projectInfoZipPath);
        return s1;
    }

    //采购文件编制--工程0
    @Override
    public AjaxResult generateProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException {
        //查询project  notice  dument uinfo  dument uitem soring uinfo and uitem
        Map<String, Object> dataMap = new HashMap<>();
        BusiTenderNotice notice = iBusiTenderNoticeService.getById(Long.parseLong(queryUinfo.getParams().get("noticeId") + ""));
        BusiTenderProject project = iBusiTenderProjectService.getById(Long.parseLong(queryUinfo.getParams().get("projectId") + ""));
        //查询评分方法
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", project.getProjectId())
        );
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemMapper.selectList(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId()));
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
            List<ScoringMethodUitem> list = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
                    .eq("scoring_method_id", scoringMethodItem.getScoringMethodId())
            );

            if (scoringMethodItem.getItemCode().equals("jsbps")) {
                int sum = 0;
                for (ScoringMethodUitem scoringMethodUitem : list) {
                    sum += scoringMethodUitem.getScore();
                    if (scoringMethodUitem.getUitemSort() == 1) {
                        dataMap.put("jsbfzItem1", scoringMethodUitem.getScore());
                        dataMap.put("jsbfzItem1Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 2) {
                        dataMap.put("jsbfzItem2", scoringMethodUitem.getScore());
                        dataMap.put("jsbfzItem2Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 3) {
                        dataMap.put("jsbfzItem3", scoringMethodUitem.getScore());
                        dataMap.put("jsbfzItem3Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 4) {
                        dataMap.put("jsbfzItem4", scoringMethodUitem.getScore());
                        dataMap.put("jsbfzItem4Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 5) {
                        dataMap.put("jsbfzItem5", scoringMethodUitem.getScore());
                        dataMap.put("jsbfzItem5Content", scoringMethodUitem.getItemRemark());
                    }
                }
                dataMap.put("jsbfz", sum);
            } else if (scoringMethodItem.getItemCode().equals("swbps")) {
                int sum = 0;
                for (ScoringMethodUitem scoringMethodUitem : list) {
                    sum += scoringMethodUitem.getScore();
                    if (scoringMethodUitem.getUitemSort() == 1) {
                        dataMap.put("swbfzItem1", scoringMethodUitem.getScore());
                        dataMap.put("swbfzItem1Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 2) {
                        dataMap.put("swbfzItem2", scoringMethodUitem.getScore());
                        dataMap.put("swbfzItem2Content", scoringMethodUitem.getItemRemark());

                    }
                    if (scoringMethodUitem.getUitemSort() == 3) {
                        dataMap.put("swbfzItem3", scoringMethodUitem.getScore());
                        dataMap.put("swbfzItem3Content", scoringMethodUitem.getItemRemark());
                    }
                    if (scoringMethodUitem.getUitemSort() == 4) {
                        dataMap.put("swbfzItem4", scoringMethodUitem.getScore());
                        dataMap.put("swbfzItem4Content", scoringMethodUitem.getItemRemark());
                    }
                }
                dataMap.put("swbfz", sum);


            } else if (scoringMethodItem.getItemCode().equals("tbbjdf")) {
                //计算总和
                int totalScore = list.stream()
                        .mapToInt(ScoringMethodUitem::getScore) // 获取每个ScoringMethodUitem对象的score并转换为int流
                        .sum();
                dataMap.put("tbbjfz", totalScore);
            }
        }


        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        BaseEntInfo ent = user.getEnt();
        Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
        String bidderQualification = project.getBidderQualification();
        projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
        projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
        projectMap.put("toSmeName", project.getToSmeName());
        projectMap.put("creditName", project.getCreditName());
        dataMap.put("project", projectMap);
        Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
        noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");

        // 格式化时间并添加到map中
        noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
        noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
        noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
        noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
        if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
            noticeMap.put("subcontractingAllowed1", "☑不允许");
            noticeMap.put("subcontractingAllowed2", "□允许");
        } else {
            noticeMap.put("subcontractingAllowed1", "□不允许");
            noticeMap.put("subcontractingAllowed2", "☑允许");
        }
        if (noticeMap.containsKey("allowCoalition") && "1".
                equals(noticeMap.get("allowCoalition") + "")) {
            noticeMap.put("allowCoalition", "允许");
        } else {
            noticeMap.put("allowCoalition", "不允许");
        }
        dataMap.put("notice", noticeMap);
        dataMap.put("procurement", ent);
        if (project.getAgencyId() != null) {
            BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
            dataMap.put("agency", agency);
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
        dataMap.put("prodYearMonth", df.format(new Date()));
        //查询uitem 质保期
        List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
        uitems.forEach(item -> {
            if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                JSONObject bidOpening = new JSONObject();
                bidOpenIngVos.forEach(vo -> {
                    bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                });
                dataMap.put("bidOpening", bidOpening);
            } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());
                if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                    baseInfo.put("lxr", project.getTendererContactPerson());
                    baseInfo.put("lxdh", project.getTendererPhone());
                } else {
                    baseInfo.put("lxr", project.getAgencyContactPerson());
                    baseInfo.put("lxdh", project.getAgencyPhone());
                }
                baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));
                dataMap.put("baseInfo", baseInfo);
            }
//            else if ("工程量清单及图纸".equals(item.getItemName())) {
//                String[] split = item.getItemContent().split(",");
//                List<Map<String, PictureRenderData>> list = new ArrayList<>();
//                String filePath = RuoYiConfig.getUploadPath();
//                for (int i = 0; i < split.length; i++) {
//                    String a = split[i].replaceAll("/profile/upload", filePath);
//                    Map<String, PictureRenderData> pic = new HashMap<>();
//                    pic.put("quantityListAndDrawing", Pictures.ofLocal(a)
//                            .size(500, ImageUtils.getAutoHeightByWidth(a, 500)).create());
//                    list.add(pic);
//                }
//                dataMap.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(item.getItemName()), list);
//            }
        });
        String templatePath = RuoYiConfig.getProfile() + "/templates/工程磋商采购文件.docx";
        System.out.println("templatePath:" + templatePath);
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/工程磋商采购文件.docx";
        String pdfPath = s1 + "/工程磋商采购文件.pdf";
        FileUploadUtils.checkDirExists(s1);
        XWPFTemplate template = XWPFTemplate.compile(templatePath).render(
                dataMap);
        template.writeAndClose(new FileOutputStream(originPath));
        //转PDF
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }
            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + pdfPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(pdfPath, saveOptions);
            String urlPath = AttachmentUtil.realToUrl(pdfPath);
            iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
            BusiAttachment noticePdf = new BusiAttachment();
            noticePdf.setBusiId(notice.getNoticeId());
            /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
            noticePdf.setFilePath(urlPath);
            noticePdf.setRemark(urlPath);
            noticePdf.setFileName("工程磋商采购文件.pdf");
            noticePdf.setFileType("5");
            noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
            iBusiAttachmentService.save(noticePdf);

            queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
            updateById(queryUinfo);

// 保存Word的BusiAttachment信息
            String urlPathWord = AttachmentUtil.realToUrl(originPath);
            //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
            BusiAttachment noticeWord = new BusiAttachment();
            noticeWord.setBusiId(notice.getNoticeId());
            noticeWord.setFilePath(urlPathWord);
            noticeWord.setRemark(urlPathWord);
            noticeWord.setFileName("工程磋商采购文件.docx");
            noticeWord.setFileType("5");
            noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
            iBusiAttachmentService.save(noticeWord);


        } catch (Exception e) {
            e.printStackTrace();
        }

        return AjaxResult.success(dataMap);
    }

    //采购文件编制--货物2
    @Transactional
    @Override
    public AjaxResult generateGoodsProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException {
        //查询project  notice  dument uinfo  dument uitem soring uinfo and uitem
        Map<String, Object> dataMap = new HashMap<>();
        BusiTenderNotice notice = iBusiTenderNoticeService.getById(Long.parseLong(queryUinfo.getParams().get("noticeId") + ""));
        BusiTenderProject project = iBusiTenderProjectService.getById(Long.parseLong(queryUinfo.getParams().get("projectId") + ""));
        //磋商有评分，其他没有评分
        if (project.getTenderMode().equals("1")) {
            //查询评分方法
            ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>()
                    .eq("project_id", project.getProjectId())
            );
            List<ScoringMethodItem> scoringMethodItems = scoringMethodItemMapper.selectList(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId()));
            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                List<ScoringMethodUitem> uitemList = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                        .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                        .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
                        .eq("scoring_method_id", scoringMethodItem.getScoringMethodId())
                );

                if (scoringMethodItem.getItemCode().equals("jsbps")) {
                    //判断list不是null，且数量大于1
                    if ("jsbps".equals(scoringMethodItem.getItemCode()) && CollectionUtils.isNotEmpty(uitemList) && uitemList.size() > 0) {
                        //技术标总分jsbfz
                        int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                        dataMap.put("jsbfz", totalScore);
                        // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                        String[][] tableData = new String[uitemList.size()][];
                        // 填充二维数组
                        for (int i = 0; i < uitemList.size(); i++) {
                            ScoringMethodUitem item = uitemList.get(i);
                            tableData[i] = new String[]{
                                    item.getItemName() + "（" + item.getScore() + "分）",
                                    item.getItemRemark()
                            };
                        }
                        // 添加备注行
//                    String str = "注：1.可量化参数的条款须提供证明资料，并在“技术偏差表”-“说明”栏里标注证明资料的页码位置，方便评审小组核实，否则，由此带来的后果由投标人自己承担；2.证明资料指产品检测报告或加盖厂家公章的技术资料等具有说服力的原件扫描件；3.未提供证明资料的，不得分。";
//                    tableData[uitemList.size()] = new String[]{" 备注", str};

                        TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                        com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                        tableStyle.setWidth("100%");
                        // 定义列数
                        int columnCount = 2;
                        // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                        int firstColumnWidthRatio = 20;
                        int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                        // 计算每列的宽度比例
                        int[] columnWidths = new int[columnCount];
                        columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                        for (int i = 1; i < columnCount; i++) {
                            columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                        }
                        // 设置表格的列宽
                        tableStyle.setColWidths(columnWidths);
                        tableStyle.setTopBorder(BorderStyle.DEFAULT);
                        tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                        tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                        tableStyle.setRightBorder(BorderStyle.DEFAULT);
                        tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                        tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                        tableRenderData.setTableStyle(tableStyle);


                        CellStyle cellStyle=new CellStyle();
                        cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                        for (RowRenderData row : tableRenderData.getRows()) {
                            for (CellRenderData cell : row.getCells()) {
                                cell.setCellStyle(cellStyle);
                            }
                        }
                        dataMap.put("jsbTable",tableRenderData );
                    }
                }
                else if (scoringMethodItem.getItemCode().equals("swbps")) {
                    int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                    dataMap.put("swbfz", totalScore);
                    // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                    String[][] tableData = new String[uitemList.size()][];
                    // 填充二维数组
                    for (int i = 0; i < uitemList.size(); i++) {
                        ScoringMethodUitem item = uitemList.get(i);
                        tableData[i] = new String[]{
                                item.getItemName() + "（" + item.getScore() + "分）",
                                item.getItemRemark()
                        };
                    }
                    TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                    com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                    tableStyle.setWidth("100%");
                    // 定义列数
                    int columnCount = 2;
                    // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                    int firstColumnWidthRatio = 20;
                    int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                    // 计算每列的宽度比例
                    int[] columnWidths = new int[columnCount];
                    columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                    for (int i = 1; i < columnCount; i++) {
                        columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                    }
                    // 设置表格的列宽
                    tableStyle.setColWidths(columnWidths);
                    tableStyle.setTopBorder(BorderStyle.DEFAULT);
                    tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                    tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                    tableStyle.setRightBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                    tableRenderData.setTableStyle(tableStyle);
                    CellStyle cellStyle=new CellStyle();
                    cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                    for (RowRenderData row : tableRenderData.getRows()) {
                        for (CellRenderData cell : row.getCells()) {
                            cell.setCellStyle(cellStyle);
                        }
                    }
                    dataMap.put("swbTable",tableRenderData );
                }
                else if (scoringMethodItem.getItemCode().equals("tbbjdf")) {
                    //计算总和
                    int totalScore = uitemList.stream()
                            .mapToInt(ScoringMethodUitem::getScore) // 获取每个ScoringMethodUitem对象的score并转换为int流
                            .sum();
                    dataMap.put("tbbjfz", totalScore);
                }
            }

            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            BaseEntInfo ent = user.getEnt();
            Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
            String bidderQualification = project.getBidderQualification();
            projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
            projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            projectMap.put("toSmeName", project.getToSmeName());
            projectMap.put("creditName", project.getCreditName());
            dataMap.put("project", projectMap);
            Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
            noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");

            // 格式化时间并添加到map中
            noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
            noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
            noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
            noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
            if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
                noticeMap.put("subcontractingAllowed1", "☑不允许");
                noticeMap.put("subcontractingAllowed2", "□允许");
            } else {
                noticeMap.put("subcontractingAllowed1", "□不允许");
                noticeMap.put("subcontractingAllowed2", "☑允许");
            }
            if (noticeMap.containsKey("allowCoalition") && "1".
                    equals(noticeMap.get("allowCoalition") + "")) {
                noticeMap.put("allowCoalition", "允许");
            } else {
                noticeMap.put("allowCoalition", "不允许");
            }
            dataMap.put("notice", noticeMap);
            dataMap.put("procurement", ent);
            if (project.getAgencyId() != null) {
                BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
                dataMap.put("agency", agency);
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
            dataMap.put("prodYearMonth", df.format(new Date()));
            //查询uitem 质保期
            List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
            uitems.forEach(item -> {
                if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                    List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                    JSONObject bidOpening = new JSONObject();
                    bidOpenIngVos.forEach(vo -> {
                        bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                    });
                    dataMap.put("bidOpening", bidOpening);
                } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                    JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());

                    if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                        baseInfo.put("lxr", project.getTendererContactPerson());
                        baseInfo.put("lxdh", project.getTendererPhone());
                    } else {
                        baseInfo.put("lxr", project.getAgencyContactPerson());
                        baseInfo.put("lxdh", project.getAgencyPhone());
                    }
                    baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));
                    dataMap.put("baseInfo", baseInfo);
                } else if ("采购需求".equals(item.getItemName())) {
                    ProcurementDemandVo procurementDemandVo = JSONObject.parseObject(item.getItemContent(), ProcurementDemandVo.class);
                    // dataMap.put("procurementDemandVo", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("procurementDemandVo", procurementDemandVo);
                    dataMap.put("SubstantiveReqRequirement", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("ParamReqRequirement", procurementDemandVo.getParamReq().getRequirement());

                    //取附件  /profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf
                    // List<Map<String, String>> png = pdfToImage(procurementDemandVo.getQuantityParamReq().getPdfUrl(), "png");
                    //List<Map<String, String>> pngs = pdfToImage("D:\\ruoyi\\uploadPath\\templates\\新建文件夹\\关于--新建项目.pdf", "png");
                    List<ConvertImage> convertImages = pdfConvertImageService.convertImage(null, AttachmentUtil.urlToReal(procurementDemandVo.getQuantityParamReq().getPdfUrl()));

                    List<Map<String, PictureRenderData>> list = new ArrayList<>();
                    for (ConvertImage convertImage : convertImages) {
                        Map<String, PictureRenderData> map = new HashMap<>();
                        map.put("png",Pictures.ofLocal(convertImage.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(convertImage.getFilePath(), 500)).create());
                        list.add(map);
                    }
                    dataMap.put("pictures", list);
                }
            });

            // 从dataMap中获取tbbjfz的值，如果不存在则默认为0
            int tbbjfz = dataMap.get("tbbjfz")!= null? (int) dataMap.get("tbbjfz") : 0;
            // 从dataMap中获取jsbfz的值，如果不存在则默认为0
            int jsbfz = dataMap.get("jsbfz")!= null? (int) dataMap.get("jsbfz") : 0;
            // 从dataMap中获取swbfz的值，如果不存在则默认为0
            int swbfz = dataMap.get("swbfz")!= null? (int) dataMap.get("swbfz") : 0;
            // 计算三个值的总和
            int sum = tbbjfz + jsbfz + swbfz;
            // 判断总和是否等于100
            if (sum != 100){
                throw new RuntimeException("技术标、商务标、投标报价打分，累计分值不等于100分");
            }
            String templatePath = RuoYiConfig.getProfile() + "/templates/货物磋商采购文件.docx";
            System.out.println("templatePath:" + templatePath);
            String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
            String originPath = s1 + "/货物磋商采购文件.docx";
            String pdfPath = s1 + "/货物磋商采购文件.pdf";
            FileUploadUtils.checkDirExists(s1);
            Configure config = Configure.builder().bind("attachment", new AttachmentRenderPolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(dataMap);
            template.writeAndClose(new FileOutputStream(originPath));
            //转PDF
            try {
                String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                License license = new License();
                license.setLicense(is);
                if (active.equals("release")){
                    // 指定Linux系统上的中文字体目录
                    String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                    FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                }
                Document document = new Document(originPath);
                System.out.println("PDF文件地址：" + pdfPath);
                // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                PdfSaveOptions saveOptions = new PdfSaveOptions();
                saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(pdfPath, saveOptions);
                //测试内存
//            long usedMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used before saving: " + usedMemoryBefore);
//            long usedMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used after saving: " + usedMemoryAfter);
                document.save(pdfPath, saveOptions);
                String urlPath = AttachmentUtil.realToUrl(pdfPath);
                iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
                BusiAttachment noticePdf = new BusiAttachment();
                noticePdf.setBusiId(notice.getNoticeId());
                /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
                noticePdf.setFilePath(urlPath);
                noticePdf.setRemark(urlPath);
                noticePdf.setFileName("货物磋商采购文件.pdf");
                noticePdf.setFileType("5");
                noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
                iBusiAttachmentService.save(noticePdf);

                queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
                updateById(queryUinfo);

                // 保存Word的BusiAttachment信息
                String urlPathWord = AttachmentUtil.realToUrl(originPath);
                //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
                BusiAttachment noticeWord = new BusiAttachment();
                noticeWord.setBusiId(notice.getNoticeId());
                noticeWord.setFilePath(urlPathWord);
                noticeWord.setRemark(urlPathWord);
                noticeWord.setFileName("货物磋商采购文件.docx");
                noticeWord.setFileType("5");
                noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
                iBusiAttachmentService.save(noticeWord);
                return AjaxResult.success();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        else  if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            BaseEntInfo ent = user.getEnt();
            Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
            String bidderQualification = project.getBidderQualification();
            projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
            projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            projectMap.put("toSmeName", project.getToSmeName());
            projectMap.put("creditName", project.getCreditName());
            dataMap.put("project", projectMap);
            Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
            noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");

            // 格式化时间并添加到map中
            noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
            noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
            noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
            noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
            if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
                noticeMap.put("subcontractingAllowed1", "☑不允许");
                noticeMap.put("subcontractingAllowed2", "□允许");
            } else {
                noticeMap.put("subcontractingAllowed1", "□不允许");
                noticeMap.put("subcontractingAllowed2", "☑允许");
            }
            if (noticeMap.containsKey("allowCoalition") && "1".
                    equals(noticeMap.get("allowCoalition") + "")) {
                noticeMap.put("allowCoalition", "允许");
            } else {
                noticeMap.put("allowCoalition", "不允许");
            }
            dataMap.put("notice", noticeMap);
            dataMap.put("procurement", ent);
            if (project.getAgencyId() != null) {
                BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
                dataMap.put("agency", agency);
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
            dataMap.put("prodYearMonth", df.format(new Date()));
            //查询uitem 质保期
            List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
            uitems.forEach(item -> {
                if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                    List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                    JSONObject bidOpening = new JSONObject();
                    bidOpenIngVos.forEach(vo -> {
                        bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                    });
                    dataMap.put("bidOpening", bidOpening);
                } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                    JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());

                    if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                        baseInfo.put("lxr", project.getTendererContactPerson());
                        baseInfo.put("lxdh", project.getTendererPhone());
                    } else {
                        baseInfo.put("lxr", project.getAgencyContactPerson());
                        baseInfo.put("lxdh", project.getAgencyPhone());
                    }
                    baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));
                    dataMap.put("baseInfo", baseInfo);
                } else if ("采购需求".equals(item.getItemName())) {
                    ProcurementDemandVo procurementDemandVo = JSONObject.parseObject(item.getItemContent(), ProcurementDemandVo.class);
                    // dataMap.put("procurementDemandVo", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("procurementDemandVo", procurementDemandVo);
                    dataMap.put("SubstantiveReqRequirement", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("ParamReqRequirement", procurementDemandVo.getParamReq().getRequirement());

                    //取附件  /profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf
                    // List<Map<String, String>> png = pdfToImage(procurementDemandVo.getQuantityParamReq().getPdfUrl(), "png");
                    //List<Map<String, String>> pngs = pdfToImage("D:\\ruoyi\\uploadPath\\templates\\新建文件夹\\关于--新建项目.pdf", "png");
                    List<ConvertImage> convertImages = pdfConvertImageService.convertImage(null, AttachmentUtil.urlToReal(procurementDemandVo.getQuantityParamReq().getPdfUrl()));

                    List<Map<String, PictureRenderData>> list = new ArrayList<>();
                    for (ConvertImage convertImage : convertImages) {
                        Map<String, PictureRenderData> map = new HashMap<>();
                        map.put("png",Pictures.ofLocal(convertImage.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(convertImage.getFilePath(), 500)).create());
                        list.add(map);
                    }
                    dataMap.put("pictures", list);
                }
            });

           /* // 从dataMap中获取tbbjfz的值，如果不存在则默认为0
            int tbbjfz = dataMap.get("tbbjfz")!= null? (int) dataMap.get("tbbjfz") : 0;
            // 从dataMap中获取jsbfz的值，如果不存在则默认为0
            int jsbfz = dataMap.get("jsbfz")!= null? (int) dataMap.get("jsbfz") : 0;
            // 从dataMap中获取swbfz的值，如果不存在则默认为0
            int swbfz = dataMap.get("swbfz")!= null? (int) dataMap.get("swbfz") : 0;
            // 计算三个值的总和
            int sum = tbbjfz + jsbfz + swbfz;
            // 判断总和是否等于100
            if (sum != 100){
                throw new RuntimeException("技术标、商务标、投标报价打分，累计分值不等于100分");
            }*/
            String templatePath = RuoYiConfig.getProfile() + "/templates/货物询价采购文件.docx";
            System.out.println("templatePath:" + templatePath);
            String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
            String originPath = s1 + "/货物询价采购文件.docx";
            String pdfPath = s1 + "/货物询价采购文件.pdf";
            FileUploadUtils.checkDirExists(s1);
            Configure config = Configure.builder().bind("attachment", new AttachmentRenderPolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(dataMap);
            template.writeAndClose(new FileOutputStream(originPath));
            //转PDF
            try {
                String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                License license = new License();
                license.setLicense(is);
                if (active.equals("release")){
                    // 指定Linux系统上的中文字体目录
                    String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                    FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                }
                Document document = new Document(originPath);
                System.out.println("PDF文件地址：" + pdfPath);
                // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                PdfSaveOptions saveOptions = new PdfSaveOptions();
                saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(pdfPath, saveOptions);
                //测试内存
//            long usedMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used before saving: " + usedMemoryBefore);
//            long usedMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used after saving: " + usedMemoryAfter);
                document.save(pdfPath, saveOptions);
                String urlPath = AttachmentUtil.realToUrl(pdfPath);
                iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
                BusiAttachment noticePdf = new BusiAttachment();
                noticePdf.setBusiId(notice.getNoticeId());
                /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
                noticePdf.setFilePath(urlPath);
                noticePdf.setRemark(urlPath);
                noticePdf.setFileName("货物询价采购文件.pdf");
                noticePdf.setFileType("5");
                noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
                iBusiAttachmentService.save(noticePdf);

                queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
                updateById(queryUinfo);

                // 保存Word的BusiAttachment信息
                String urlPathWord = AttachmentUtil.realToUrl(originPath);
                //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
                BusiAttachment noticeWord = new BusiAttachment();
                noticeWord.setBusiId(notice.getNoticeId());
                noticeWord.setFilePath(urlPathWord);
                noticeWord.setRemark(urlPathWord);
                noticeWord.setFileName("货物询价采购文件.docx");
                noticeWord.setFileType("5");
                noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
                iBusiAttachmentService.save(noticeWord);
                return AjaxResult.success();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return AjaxResult.success(dataMap);


    }
    //采购文件编制--货物2
    @Transactional
    @Override
    public AjaxResult generateTenderModeGoodsProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException {
        //查询project  notice  dument uinfo  dument uitem soring uinfo and uitem
        Map<String, Object> dataMap = new HashMap<>();
        BusiTenderNotice notice = iBusiTenderNoticeService.getById(Long.parseLong(queryUinfo.getParams().get("noticeId") + ""));
        BusiTenderProject project = iBusiTenderProjectService.getById(Long.parseLong(queryUinfo.getParams().get("projectId") + ""));
        //磋商有评分，其他没有评分
        if (project.getTenderMode().equals("1")) {
            //查询评分方法
            ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>()
                    .eq("project_id", project.getProjectId())
            );
            List<ScoringMethodItem> scoringMethodItems = scoringMethodItemMapper.selectList(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId()));
            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                List<ScoringMethodUitem> uitemList = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                        .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                        .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
                        .eq("scoring_method_id", scoringMethodItem.getScoringMethodId())
                );

                if (scoringMethodItem.getItemCode().equals("jsbps")) {
                    //判断list不是null，且数量大于1
                    if ("jsbps".equals(scoringMethodItem.getItemCode()) && CollectionUtils.isNotEmpty(uitemList) && uitemList.size() > 0) {
                        //技术标总分jsbfz
                        int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                        dataMap.put("jsbfz", totalScore);
                        // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                        String[][] tableData = new String[uitemList.size()][];
                        // 填充二维数组
                        for (int i = 0; i < uitemList.size(); i++) {
                            ScoringMethodUitem item = uitemList.get(i);
                            tableData[i] = new String[]{
                                    item.getItemName() + "（" + item.getScore() + "分）",
                                    item.getItemRemark()
                            };
                        }
                        // 添加备注行
//                    String str = "注：1.可量化参数的条款须提供证明资料，并在“技术偏差表”-“说明”栏里标注证明资料的页码位置，方便评审小组核实，否则，由此带来的后果由投标人自己承担；2.证明资料指产品检测报告或加盖厂家公章的技术资料等具有说服力的原件扫描件；3.未提供证明资料的，不得分。";
//                    tableData[uitemList.size()] = new String[]{" 备注", str};

                        TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                        com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                        tableStyle.setWidth("100%");
                        // 定义列数
                        int columnCount = 2;
                        // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                        int firstColumnWidthRatio = 20;
                        int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                        // 计算每列的宽度比例
                        int[] columnWidths = new int[columnCount];
                        columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                        for (int i = 1; i < columnCount; i++) {
                            columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                        }
                        // 设置表格的列宽
                        tableStyle.setColWidths(columnWidths);
                        tableStyle.setTopBorder(BorderStyle.DEFAULT);
                        tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                        tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                        tableStyle.setRightBorder(BorderStyle.DEFAULT);
                        tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                        tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                        tableRenderData.setTableStyle(tableStyle);


                        CellStyle cellStyle=new CellStyle();
                        cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                        for (RowRenderData row : tableRenderData.getRows()) {
                            for (CellRenderData cell : row.getCells()) {
                                cell.setCellStyle(cellStyle);
                            }
                        }
                        dataMap.put("jsbTable",tableRenderData );
                    }
                }
                else if (scoringMethodItem.getItemCode().equals("swbps")) {
                    int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                    dataMap.put("swbfz", totalScore);
                    // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                    String[][] tableData = new String[uitemList.size()][];
                    // 填充二维数组
                    for (int i = 0; i < uitemList.size(); i++) {
                        ScoringMethodUitem item = uitemList.get(i);
                        tableData[i] = new String[]{
                                item.getItemName() + "（" + item.getScore() + "分）",
                                item.getItemRemark()
                        };
                    }
                    TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                    com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                    tableStyle.setWidth("100%");
                    // 定义列数
                    int columnCount = 2;
                    // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                    int firstColumnWidthRatio = 20;
                    int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                    // 计算每列的宽度比例
                    int[] columnWidths = new int[columnCount];
                    columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                    for (int i = 1; i < columnCount; i++) {
                        columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                    }
                    // 设置表格的列宽
                    tableStyle.setColWidths(columnWidths);
                    tableStyle.setTopBorder(BorderStyle.DEFAULT);
                    tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                    tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                    tableStyle.setRightBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                    tableRenderData.setTableStyle(tableStyle);
                    CellStyle cellStyle=new CellStyle();
                    cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                    for (RowRenderData row : tableRenderData.getRows()) {
                        for (CellRenderData cell : row.getCells()) {
                            cell.setCellStyle(cellStyle);
                        }
                    }
                    dataMap.put("swbTable",tableRenderData );
                }
                else if (scoringMethodItem.getItemCode().equals("tbbjdf")) {
                    //计算总和
                    int totalScore = uitemList.stream()
                            .mapToInt(ScoringMethodUitem::getScore) // 获取每个ScoringMethodUitem对象的score并转换为int流
                            .sum();
                    dataMap.put("tbbjfz", totalScore);
                }
            }

            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            BaseEntInfo ent = user.getEnt();
            Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
            String bidderQualification = project.getBidderQualification();
            projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
            projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            projectMap.put("toSmeName", project.getToSmeName());
            projectMap.put("creditName", project.getCreditName());
            dataMap.put("project", projectMap);
            Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
            noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");

            // 格式化时间并添加到map中
            noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
            noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
            noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
            noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
            if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
                noticeMap.put("subcontractingAllowed1", "☑不允许");
                noticeMap.put("subcontractingAllowed2", "□允许");
            } else {
                noticeMap.put("subcontractingAllowed1", "□不允许");
                noticeMap.put("subcontractingAllowed2", "☑允许");
            }
            if (noticeMap.containsKey("allowCoalition") && "1".
                    equals(noticeMap.get("allowCoalition") + "")) {
                noticeMap.put("allowCoalition", "允许");
            } else {
                noticeMap.put("allowCoalition", "不允许");
            }
            dataMap.put("notice", noticeMap);
            dataMap.put("procurement", ent);
            if (project.getAgencyId() != null) {
                BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
                dataMap.put("agency", agency);
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
            dataMap.put("prodYearMonth", df.format(new Date()));
            //查询uitem 质保期
            List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
            uitems.forEach(item -> {
                if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                    List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                    JSONObject bidOpening = new JSONObject();
                    bidOpenIngVos.forEach(vo -> {
                        bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                    });
                    dataMap.put("bidOpening", bidOpening);
                } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                    JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());

                    if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                        baseInfo.put("lxr", project.getTendererContactPerson());
                        baseInfo.put("lxdh", project.getTendererPhone());
                    } else {
                        baseInfo.put("lxr", project.getAgencyContactPerson());
                        baseInfo.put("lxdh", project.getAgencyPhone());
                    }
                    baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));
                    dataMap.put("baseInfo", baseInfo);
                } else if ("采购需求".equals(item.getItemName())) {
                    ProcurementDemandVo procurementDemandVo = JSONObject.parseObject(item.getItemContent(), ProcurementDemandVo.class);
                    // dataMap.put("procurementDemandVo", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("procurementDemandVo", procurementDemandVo);
                    dataMap.put("SubstantiveReqRequirement", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("ParamReqRequirement", procurementDemandVo.getParamReq().getRequirement());

                    //取附件  /profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf
                    // List<Map<String, String>> png = pdfToImage(procurementDemandVo.getQuantityParamReq().getPdfUrl(), "png");
                    //List<Map<String, String>> pngs = pdfToImage("D:\\ruoyi\\uploadPath\\templates\\新建文件夹\\关于--新建项目.pdf", "png");
                    List<ConvertImage> convertImages = pdfConvertImageService.convertImage(null, AttachmentUtil.urlToReal(procurementDemandVo.getQuantityParamReq().getPdfUrl()));

                    List<Map<String, PictureRenderData>> list = new ArrayList<>();
                    for (ConvertImage convertImage : convertImages) {
                        Map<String, PictureRenderData> map = new HashMap<>();
                        map.put("png",Pictures.ofLocal(convertImage.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(convertImage.getFilePath(), 500)).create());
                        list.add(map);
                    }
                    dataMap.put("pictures", list);
                }
            });

            // 从dataMap中获取tbbjfz的值，如果不存在则默认为0
            int tbbjfz = dataMap.get("tbbjfz")!= null? (int) dataMap.get("tbbjfz") : 0;
            // 从dataMap中获取jsbfz的值，如果不存在则默认为0
            int jsbfz = dataMap.get("jsbfz")!= null? (int) dataMap.get("jsbfz") : 0;
            // 从dataMap中获取swbfz的值，如果不存在则默认为0
            int swbfz = dataMap.get("swbfz")!= null? (int) dataMap.get("swbfz") : 0;
            // 计算三个值的总和
            int sum = tbbjfz + jsbfz + swbfz;
            // 判断总和是否等于100
            if (sum != 100){
                throw new RuntimeException("技术标、商务标、投标报价打分，累计分值不等于100分");
            }
            String templatePath = RuoYiConfig.getProfile() + "/templates/货物磋商采购文件.docx";
            System.out.println("templatePath:" + templatePath);
            String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
            String originPath = s1 + "/货物磋商采购文件.docx";
            String pdfPath = s1 + "/货物磋商采购文件.pdf";
            FileUploadUtils.checkDirExists(s1);
            Configure config = Configure.builder().bind("attachment", new AttachmentRenderPolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(dataMap);
            template.writeAndClose(new FileOutputStream(originPath));
            //转PDF
            try {
                String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                License license = new License();
                license.setLicense(is);
                if (active.equals("release")){
                    // 指定Linux系统上的中文字体目录
                    String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                    FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                }
                Document document = new Document(originPath);
                System.out.println("PDF文件地址：" + pdfPath);
                // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                PdfSaveOptions saveOptions = new PdfSaveOptions();
                saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(pdfPath, saveOptions);
                //测试内存
//            long usedMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used before saving: " + usedMemoryBefore);
//            long usedMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used after saving: " + usedMemoryAfter);
                document.save(pdfPath, saveOptions);
                String urlPath = AttachmentUtil.realToUrl(pdfPath);
                iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
                BusiAttachment noticePdf = new BusiAttachment();
                noticePdf.setBusiId(notice.getNoticeId());
                /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
                noticePdf.setFilePath(urlPath);
                noticePdf.setRemark(urlPath);
                noticePdf.setFileName("货物磋商采购文件.pdf");
                noticePdf.setFileType("5");
                noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
                iBusiAttachmentService.save(noticePdf);

                queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
                updateById(queryUinfo);

                // 保存Word的BusiAttachment信息
                String urlPathWord = AttachmentUtil.realToUrl(originPath);
                //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
                BusiAttachment noticeWord = new BusiAttachment();
                noticeWord.setBusiId(notice.getNoticeId());
                noticeWord.setFilePath(urlPathWord);
                noticeWord.setRemark(urlPathWord);
                noticeWord.setFileName("货物磋商采购文件.docx");
                noticeWord.setFileType("5");
                noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
                iBusiAttachmentService.save(noticeWord);
                return AjaxResult.success();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        else  if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            BaseEntInfo ent = user.getEnt();
            Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
            String bidderQualification = project.getBidderQualification();
            projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
            projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            projectMap.put("toSmeName", project.getToSmeName());
            projectMap.put("creditName", project.getCreditName());
            dataMap.put("project", projectMap);
            Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
            noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
            //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");

            // 格式化时间并添加到map中
            noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
            noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
            noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
            noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
            if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
                noticeMap.put("subcontractingAllowed1", "☑不允许");
                noticeMap.put("subcontractingAllowed2", "□允许");
            } else {
                noticeMap.put("subcontractingAllowed1", "□不允许");
                noticeMap.put("subcontractingAllowed2", "☑允许");
            }
            if (noticeMap.containsKey("allowCoalition") && "1".
                    equals(noticeMap.get("allowCoalition") + "")) {
                noticeMap.put("allowCoalition", "允许");
            } else {
                noticeMap.put("allowCoalition", "不允许");
            }
            dataMap.put("notice", noticeMap);
            dataMap.put("procurement", ent);
            if (project.getAgencyId() != null) {
                BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
                dataMap.put("agency", agency);
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
            dataMap.put("prodYearMonth", df.format(new Date()));
            //查询uitem 质保期
            List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
            uitems.forEach(item -> {
                if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                    List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                    JSONObject bidOpening = new JSONObject();
                    bidOpenIngVos.forEach(vo -> {
                        bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                    });
                    dataMap.put("bidOpening", bidOpening);
                } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                    JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());

                    if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                        baseInfo.put("lxr", project.getTendererContactPerson());
                        baseInfo.put("lxdh", project.getTendererPhone());
                    } else {
                        baseInfo.put("lxr", project.getAgencyContactPerson());
                        baseInfo.put("lxdh", project.getAgencyPhone());
                    }
                    baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));
                    dataMap.put("baseInfo", baseInfo);
                } else if ("采购需求".equals(item.getItemName())) {
                    ProcurementDemandVo procurementDemandVo = JSONObject.parseObject(item.getItemContent(), ProcurementDemandVo.class);
                    // dataMap.put("procurementDemandVo", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("procurementDemandVo", procurementDemandVo);
                    dataMap.put("SubstantiveReqRequirement", procurementDemandVo.getSubstantiveReq().getRequirement());
                    dataMap.put("ParamReqRequirement", procurementDemandVo.getParamReq().getRequirement());
                    dataMap.put("quantityParamReq", procurementDemandVo.getQuantityParamReq().getRequirement());

                    //取附件  /profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf
                    // List<Map<String, String>> png = pdfToImage(procurementDemandVo.getQuantityParamReq().getPdfUrl(), "png");
                    //List<Map<String, String>> pngs = pdfToImage("D:\\ruoyi\\uploadPath\\templates\\新建文件夹\\关于--新建项目.pdf", "png");
                    //ExcelToPdfConverter.convertExcelToPdf(AttachmentUtil.urlToReal(procurementDemandVo.getSubstantiveReq().getRequirement()),);
                    /* 采购需求 */
                    // 获取 Excel 文件路径
                    String excelFilePath = AttachmentUtil.urlToReal(procurementDemandVo.getSubstantiveReq().getRequirement());

                    try (InputStream inputStream = new FileInputStream(excelFilePath)) {
                        // 转换Excel为TableRenderData，使用第一个工作表，从第1行开始，包含所有数据，将第一行作为表头
                        TableRenderData tableData = ExcelToPoiTlConverter.convertToTableData(inputStream, null, 0, -1, true);
                        dataMap.put("pictures", tableData);

                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                   /* // 修改文件后缀名为 PDF
                    String pdfFilePath = excelFilePath.replace(".xlsx", ".pdf").replace(".xls", ".pdf");
                    // 输出修改后的文件路径
                    try {
                        ExcelToPdfConverter.convertExcelToPdf(excelFilePath,pdfFilePath);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    System.out.println("PDF 文件路径: " + pdfFilePath);

                    List<ConvertImage> convertImages = pdfConvertImageService.convertImage(null, pdfFilePath);
                    List<Map<String, PictureRenderData>> list = new ArrayList<>();
                    for (ConvertImage convertImage : convertImages) {
                        Map<String, PictureRenderData> map = new HashMap<>();
                        map.put("png",Pictures.ofLocal(convertImage.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(convertImage.getFilePath(), 500)).create());
                        list.add(map);
                    }*/
                }
            });

           /* // 从dataMap中获取tbbjfz的值，如果不存在则默认为0
            int tbbjfz = dataMap.get("tbbjfz")!= null? (int) dataMap.get("tbbjfz") : 0;
            // 从dataMap中获取jsbfz的值，如果不存在则默认为0
            int jsbfz = dataMap.get("jsbfz")!= null? (int) dataMap.get("jsbfz") : 0;
            // 从dataMap中获取swbfz的值，如果不存在则默认为0
            int swbfz = dataMap.get("swbfz")!= null? (int) dataMap.get("swbfz") : 0;
            // 计算三个值的总和
            int sum = tbbjfz + jsbfz + swbfz;
            // 判断总和是否等于100
            if (sum != 100){
                throw new RuntimeException("技术标、商务标、投标报价打分，累计分值不等于100分");
            }*/
            String templatePath = RuoYiConfig.getProfile() + "/templates/货物询价采购文件.docx";
            System.out.println("templatePath:" + templatePath);
            String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
            String originPath = s1 + "/货物询价采购文件.docx";
            String pdfPath = s1 + "/货物询价采购文件.pdf";
            FileUploadUtils.checkDirExists(s1);
            Configure config = Configure.builder().bind("attachment", new AttachmentRenderPolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(dataMap);
            template.writeAndClose(new FileOutputStream(originPath));
            //转PDF
            try {
                String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                License license = new License();
                license.setLicense(is);
                if (active.equals("release")){
                    // 指定Linux系统上的中文字体目录
                    String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                    FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                }
                Document document = new Document(originPath);
                System.out.println("PDF文件地址：" + pdfPath);
                // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                PdfSaveOptions saveOptions = new PdfSaveOptions();
                saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(pdfPath, saveOptions);
                //测试内存
//            long usedMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used before saving: " + usedMemoryBefore);
//            long usedMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
//            System.out.println("Memory used after saving: " + usedMemoryAfter);
                document.save(pdfPath, saveOptions);
                String urlPath = AttachmentUtil.realToUrl(pdfPath);
                iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
                BusiAttachment noticePdf = new BusiAttachment();
                noticePdf.setBusiId(notice.getNoticeId());
                /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
                noticePdf.setFilePath(urlPath);
                noticePdf.setRemark(urlPath);
                noticePdf.setFileName("货物询价采购文件.pdf");
                noticePdf.setFileType("5");
                noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
                iBusiAttachmentService.save(noticePdf);

                queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
                updateById(queryUinfo);

                // 保存Word的BusiAttachment信息
                String urlPathWord = AttachmentUtil.realToUrl(originPath);
                //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
                BusiAttachment noticeWord = new BusiAttachment();
                noticeWord.setBusiId(notice.getNoticeId());
                noticeWord.setFilePath(urlPathWord);
                noticeWord.setRemark(urlPathWord);
                noticeWord.setFileName("货物询价采购文件.docx");
                noticeWord.setFileType("5");
                noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
                iBusiAttachmentService.save(noticeWord);
                return AjaxResult.success();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return AjaxResult.success(dataMap);


    }


    //采购文件编制--服务1
    @Override
    public AjaxResult generateServiceProjectFile(ProcurementDocumentsUinfo queryUinfo) throws IOException {

        //查询project  notice  dument uinfo  dument uitem soring uinfo and uitem
        Map<String, Object> dataMap = new HashMap<>();
        BusiTenderNotice notice = iBusiTenderNoticeService.getById(Long.parseLong(queryUinfo.getParams().get("noticeId") + ""));
        BusiTenderProject project = iBusiTenderProjectService.getById(Long.parseLong(queryUinfo.getParams().get("projectId") + ""));
        //查询评分方法
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", project.getProjectId())
        );
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemMapper.selectList(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId()));
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
            List<ScoringMethodUitem> uitemList = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("scoring_method_item_id", scoringMethodItem.getScoringMethodItemId())
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId())
                    .eq("scoring_method_id", scoringMethodItem.getScoringMethodId())
            );

            if (scoringMethodItem.getItemCode().equals("jsbps")) {
                //判断list不是null，且数量大于1
                if ("jsbps".equals(scoringMethodItem.getItemCode()) && CollectionUtils.isNotEmpty(uitemList) && uitemList.size() > 0) {
                    //技术标总分jsbfz
                    int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                    dataMap.put("jsbfz", totalScore);
                    // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                    String[][] tableData = new String[uitemList.size()][];
                    // 填充二维数组
                    for (int i = 0; i < uitemList.size(); i++) {
                        ScoringMethodUitem item = uitemList.get(i);
                        tableData[i] = new String[]{
                                item.getItemName() + "（" + item.getScore() + "分）",
                                item.getItemRemark()
                        };
                    }
                    // 添加备注行
//                    String str = "注：1.可量化参数的条款须提供证明资料，并在“技术偏差表”-“说明”栏里标注证明资料的页码位置，方便评审小组核实，否则，由此带来的后果由投标人自己承担；2.证明资料指产品检测报告或加盖厂家公章的技术资料等具有说服力的原件扫描件；3.未提供证明资料的，不得分。";
//                    tableData[uitemList.size()] = new String[]{" 备注", str};
                    // 一个2行2列的表格
                    TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                    com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                    tableStyle.setWidth("100%");
                    // 定义列数
                    int columnCount = 2;
                    // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                    int firstColumnWidthRatio = 20;
                    int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                    // 计算每列的宽度比例
                    int[] columnWidths = new int[columnCount];
                    columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                    for (int i = 1; i < columnCount; i++) {
                        columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                    }
                    // 设置表格的列宽
                    tableStyle.setColWidths(columnWidths);
                    tableStyle.setTopBorder(BorderStyle.DEFAULT);
                    tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                    tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                    tableStyle.setRightBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                    tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                    tableRenderData.setTableStyle(tableStyle);
                    CellStyle cellStyle=new CellStyle();
                    cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                    for (RowRenderData row : tableRenderData.getRows()) {
                        for (CellRenderData cell : row.getCells()) {
                            cell.setCellStyle(cellStyle);
                        }
                    }

                    dataMap.put("jsbTable",tableRenderData );
                }
            } else if (scoringMethodItem.getItemCode().equals("swbps")) {
                int totalScore = uitemList.stream().mapToInt(ScoringMethodUitem::getScore).sum(); // 对所有score值求和
                dataMap.put("swbfz", totalScore);
                // 创建一个二维数组，大小为 uitemList.size() + 1（用于备注）
                String[][] tableData = new String[uitemList.size()][];
                // 填充二维数组
                for (int i = 0; i < uitemList.size(); i++) {
                    ScoringMethodUitem item = uitemList.get(i);
                    tableData[i] = new String[]{
                            item.getItemName() + "（" + item.getScore() + "分）",
                            item.getItemRemark()
                    };
                }
                TableRenderData tableRenderData = Tables.of(tableData).border(BorderStyle.DEFAULT).create();
                com.deepoove.poi.data.style.TableStyle tableStyle=new com.deepoove.poi.data.style.TableStyle();
                tableStyle.setWidth("100%");
                // 定义列数
                int columnCount = 2;
                // 定义列宽比例，例如：第一列占比10%，其他列占比剩余的90%并平均分配
                int firstColumnWidthRatio = 20;
                int otherColumnsWidthRatio = (100 - firstColumnWidthRatio) / (columnCount - 1);
                // 计算每列的宽度比例
                int[] columnWidths = new int[columnCount];
                columnWidths[0] = firstColumnWidthRatio; // 第一列宽度
                for (int i = 1; i < columnCount; i++) {
                    columnWidths[i] = otherColumnsWidthRatio; // 其他列宽度
                }
                // 设置表格的列宽
                tableStyle.setColWidths(columnWidths);
                tableStyle.setTopBorder(BorderStyle.DEFAULT);
                tableStyle.setBottomBorder(BorderStyle.DEFAULT);
                tableStyle.setLeftBorder(BorderStyle.DEFAULT);
                tableStyle.setRightBorder(BorderStyle.DEFAULT);
                tableStyle.setInsideHBorder(BorderStyle.DEFAULT);
                tableStyle.setInsideVBorder(BorderStyle.DEFAULT);
                tableRenderData.setTableStyle(tableStyle);
                CellStyle cellStyle=new CellStyle();
                cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
                for (RowRenderData row : tableRenderData.getRows()) {
                    for (CellRenderData cell : row.getCells()) {
                        cell.setCellStyle(cellStyle);
                    }
                }
                dataMap.put("swbTable", tableRenderData);
            } else if (scoringMethodItem.getItemCode().equals("tbbjdf")) {
                //计算总和
                int totalScore = uitemList.stream()
                        .mapToInt(ScoringMethodUitem::getScore) // 获取每个ScoringMethodUitem对象的score并转换为int流
                        .sum();
                dataMap.put("tbbjfz", totalScore);
            }
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        BaseEntInfo ent = user.getEnt();
        Map<String, Object> projectMap = BeanUtil.beanToMap(project, false, false);
        String bidderQualification = project.getBidderQualification();
        projectMap.put("bidderQualification", bidderQualification.replaceAll("<p>", "\n\t\t").replaceAll("</p>", ""));
        projectMap.put("tenderFundSource", dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
        projectMap.put("toSmeName", project.getToSmeName());
        projectMap.put("creditName", project.getCreditName());
        // 假设projectMap是一个已经存在的HashMap<String, String>
        if (null == projectMap.get("agencyName") ) {
            projectMap.put("agencyName", "");
        }
        if (null == projectMap.get("chargingStandard") ) {
            projectMap.put("chargingStandard", "");
        }
        dataMap.put("project", projectMap);
        Map<String, Object> noticeMap = BeanUtil.beanToMap(notice, false, false);
        noticeMap.put("announcementDuration", DateUtils.differentDaysByMillisecond(notice.getNoticeStartTime(), notice.getNoticeEndTime()));
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfNoTime = new SimpleDateFormat("yyyy年MM月dd日");
        // 格式化时间并添加到map中
        noticeMap.put("bidOpeningTime", DateUtils.formatTime(notice.getBidOpeningTime(), sdfNoTime));
        noticeMap.put("docResponseOverTime", DateUtils.formatTime(notice.getDocResponseOverTime(), sdfNoTime));
        noticeMap.put("docAcquisitionStartTime", DateUtils.formatTime(notice.getDocAcquisitionStartTime(), sdfNoTime));
        noticeMap.put("docAcquisitionEndTime", DateUtils.formatTime(notice.getDocAcquisitionEndTime(), sdfNoTime));
        if (notice.getSubcontractingAllowed() == null || notice.getSubcontractingAllowed() == 0) {
            noticeMap.put("subcontractingAllowed1", "☑不允许");
            noticeMap.put("subcontractingAllowed2", "□允许");
        } else {
            noticeMap.put("subcontractingAllowed1", "□不允许");
            noticeMap.put("subcontractingAllowed2", "☑允许");
        }
        if (noticeMap.containsKey("allowCoalition") && "1".
                equals(noticeMap.get("allowCoalition") + "")) {
            noticeMap.put("allowCoalition", "允许");
        } else {
            noticeMap.put("allowCoalition", "不允许");
        }
        dataMap.put("notice", noticeMap);
        dataMap.put("procurement", ent);
        if (project.getAgencyId() != null) {
            BaseEntInfo agency = iBaseEntInfoService.getById(project.getAgencyId());
            dataMap.put("agency", agency);
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月");
        dataMap.put("prodYearMonth", df.format(new Date()));
        //查询uitem 质保期
        List<ProcurementDocumentsUitem> uitems = iProcurementDocumentsUitemService.listByInfoId(queryUinfo.getEntFileId());
        uitems.forEach(item -> {
            if ("开标一览表".equals(item.getItemName())) {
//                [{"isFixed":true,"code":"bidManager","openBid":"项目负责人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidderLegal","openBid":"法定代表人或授权委托人","defaultValue":"","remark":""},{"isFixed":true,"code":"bidPrice","openBid":"投标报价（元）","defaultValue":"","remark":"只能输入金额数字，不要带单位"},{"isFixed":true,"code":"qualityDemand","openBid":"质量要求","defaultValue":"","remark":""},{"code":"warrantyPeriod","isFixed":true,"openBid":"质保期/保修期","defaultValue":"365天","remark":""},{"code":"overTimeLimit","isFixed":true,"openBid":"工期/供货期","defaultValue":"100天","remark":""}]
                List<BidOpenIngVo> bidOpenIngVos = JSONArray.parseArray(item.getItemContent(), BidOpenIngVo.class);
                JSONObject bidOpening = new JSONObject();
                bidOpenIngVos.forEach(vo -> {
                    bidOpening.put(XmlConstants.COLUMN_MAP_NAME2CODE.get(vo.getOpenBid()), vo.getDefaultValue());
                });
                dataMap.put("bidOpening", bidOpening);
            } else if ("项目基本信息".equals(item.getItemName())) {
//                {"projectName":"风车天路采购项目","projectCode":"4324,4325,4326-20240821114751","deadLine":"2024-08-31","tenderMode":"1","tendererName":"采购人三号","tendererPhone":"17603211111","agencyName":"","agencyPhone":""}
                JSONObject baseInfo = JSONObject.parseObject(item.getItemContent());
                if (StringUtils.isBlank(project.getAgencyContactPerson())) {
                    baseInfo.put("lxr", project.getTendererContactPerson());
                    baseInfo.put("lxdh", project.getTendererPhone());
                } else {
                    baseInfo.put("lxr", project.getAgencyContactPerson());
                    baseInfo.put("lxdh", project.getAgencyPhone());
                }
                baseInfo.put("totalReviewNum", baseInfo.getIntValue("expertNum") + baseInfo.getIntValue("purchaserNum"));

                dataMap.put("baseInfo", baseInfo);
            } else if ("采购需求".equals(item.getItemName())) {
                System.out.println("json："+JSONObject.toJSONString(item.getItemContent()));
                ServiceVo serviceVo = JSONObject.parseObject(item.getItemContent(), ServiceVo.class);
                // dataMap.put("procurementDemandVo", procurementDemandVo.getSubstantiveReq().getRequirement());
                dataMap.put("serviceVo", serviceVo);
                dataMap.put("projectOverview", serviceVo.getProjectOverview().getRequirement());
                dataMap.put("standardsAndReq", serviceVo.getStandardsAndReq().getRequirement());

            }
        });
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月");
        dataMap.put("prodYearMonth", simpleDateFormat.format(new Date()));
        // 从dataMap中获取tbbjfz的值，如果不存在则默认为0
        int tbbjfz = dataMap.get("tbbjfz")!= null? (int) dataMap.get("tbbjfz") : 0;
        // 从dataMap中获取jsbfz的值，如果不存在则默认为0
        int jsbfz = dataMap.get("jsbfz")!= null? (int) dataMap.get("jsbfz") : 0;
        // 从dataMap中获取swbfz的值，如果不存在则默认为0
        int swbfz = dataMap.get("swbfz")!= null? (int) dataMap.get("swbfz") : 0;
        // 计算三个值的总和
        int sum = tbbjfz + jsbfz + swbfz;
        // 判断总和是否等于100
        if (sum != 100){
            throw new RuntimeException("技术标、商务标、投标报价打分，累计分值不等于100分");
        }
        String templatePath = RuoYiConfig.getProfile() + "/templates/服务磋商采购文件.docx";
        System.out.println("templatePath:" + templatePath);
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/服务磋商采购文件.docx";
        String pdfPath = s1 + "/服务磋商采购文件.pdf";
        FileUploadUtils.checkDirExists(s1);
        Configure config = Configure.builder().bind("attachment", new AttachmentRenderPolicy()).build();
        XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(dataMap);
        template.writeAndClose(new FileOutputStream(originPath));
        //转PDF
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);

            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }

            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + pdfPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(pdfPath, saveOptions);
            //测试内存
            long usedMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            System.out.println("Memory used before saving: " + usedMemoryBefore);
            document.save(pdfPath, saveOptions);
            long usedMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            System.out.println("Memory used after saving: " + usedMemoryAfter);

            String urlPath = AttachmentUtil.realToUrl(pdfPath);
            iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");
            BusiAttachment noticePdf = new BusiAttachment();
            noticePdf.setBusiId(notice.getNoticeId());
            /// String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
            noticePdf.setFilePath(urlPath);
            noticePdf.setRemark(urlPath);
            noticePdf.setFileName("服务磋商采购文件.pdf");
            noticePdf.setFileType("5");
            noticePdf.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPath));
            iBusiAttachmentService.save(noticePdf);

            queryUinfo.setRemark(PdfUtil.getProcurementPageNum(pdfPath, ProcurementScoreItemKeywordEnum.getKeywordList(project.getTenderMode(),project.getProjectType())));
            updateById(queryUinfo);

            // 保存Word的BusiAttachment信息
            String urlPathWord = AttachmentUtil.realToUrl(originPath);
            //  iBusiAttachmentService.deleteByBusiIdAndType(notice.getNoticeId(), "5");  // 假设类型4代表Word，可根据实际情况调整
            BusiAttachment noticeWord = new BusiAttachment();
            noticeWord.setBusiId(notice.getNoticeId());
            noticeWord.setFilePath(urlPathWord);
            noticeWord.setRemark(urlPathWord);
            noticeWord.setFileName("服务磋商采购文件.docx");
            noticeWord.setFileType("5");
            noticeWord.setFileSuffix(AttachmentUtil.getAttachmentSuffix(urlPathWord));
            iBusiAttachmentService.save(noticeWord);
            return AjaxResult.success(dataMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(dataMap);
    }

    @Override
    public String prepareResponseDocuments(ProcurementDocumentVo procurementDocumentVo) {
        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentVo.getProjectId());
        BaseEntInfo entInfo = iBaseEntInfoService.getById(procurementDocumentVo.getEntId());
        //  BusiBiddingRecord busiBiddingRecord = busiBiddingRecordService.getOne(new QueryWrapper<BusiBiddingRecord>()
        //          .eq("project_id", project.getProjectId()).eq("bidder_id", entInfo.getEntId()));
        Map<String, Object> dataMap = new HashMap<>();

        if (StringUtils.isBlank(project.getAgencyName())){
        project.setLxr(project.getTendererName());
        project.setLxdh(project.getTendererPhone());
        }else{
            project.setLxr(project.getAgencyName());
            project.setLxdh(project.getAgencyPhone());
        }
        dataMap.put("vo", procurementDocumentVo);
        dataMap.put("project", project);
        dataMap.put("baseEntInfo", entInfo);
        dataMap.put("currentTimeow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("contentMap", procurementDocumentVo.getContentMap());
        List<BusiAttachment> attachments = procurementDocumentVo.getAttachments();
        if (Objects.nonNull(attachments) && attachments.size() > 0) {
            Map<String, List<BusiAttachment>> groupedByType = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getFileType));
            List<Map<String, PictureRenderData>> list = new ArrayList<>();
            for (String s : groupedByType.keySet()) {
                //单种类型附件的集合
                List<BusiAttachment> busiAttachments = groupedByType.get(s);
                Map<String, PictureRenderData> map = new HashMap<>();
                for (BusiAttachment attachment : busiAttachments) {
                    if (!"pdf".equals(AttachmentUtil.getAttachmentSuffix(attachment.getFilePath()))) {
                        map.put(attachment.getFileType(), Pictures.ofLocal(attachment.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(attachment.getFilePath(), 500)).create());
                        list.add(map);
                    }
                }
            }
            dataMap.put("pictures", list);
        }

        String templatePath = RuoYiConfig.getProfile() + "/templates/磋商工程响应文件.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/响应文件编制.docx";
        String genPath = s1 + "/响应文件编制2.pdf";
        FileUploadUtils.checkDirExists(s1);


        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }
            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            for (String key : procurementDocumentVo.getPdfFiles().keySet()) {
                genPath = PdfUtil.addPdf(genPath, procurementDocumentVo.getPdfFiles().get(key), key, s1);
            }
            genPath = PdfUtil.deleteBlankPageAndCreateFooter(genPath, s1);
            return genPath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String prepareGoodsResponseDocuments(ProcurementDocumentVo procurementDocumentVo) {
        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentVo.getProjectId());
        BaseEntInfo entInfo = iBaseEntInfoService.getById(procurementDocumentVo.getEntId());
        Map<String, Object> dataMap = new HashMap<>();

        project.setLxr(project.getTendererName());
        project.setLxdh(project.getTendererPhone());
        dataMap.put("vo", procurementDocumentVo); // 供应商响应信息
        dataMap.put("project", project); // 项目、采购人信息
        dataMap.put("baseEntInfo", entInfo);  // 供应商企业信息
        dataMap.put("currentTimeNow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("contentMap", procurementDocumentVo.getContentMap());
        List<BusiAttachment> attachments = procurementDocumentVo.getAttachments();
        if (Objects.nonNull(attachments) && attachments.size() > 0) {
            Map<String, List<BusiAttachment>> groupedByType = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getFileType));
            List<Map<String, PictureRenderData>> list = new ArrayList<>();
            for (String s : groupedByType.keySet()) {
                //单种类型附件的集合
                List<BusiAttachment> busiAttachments = groupedByType.get(s);
                Map<String, PictureRenderData> map = new HashMap<>();
                for (BusiAttachment attachment : busiAttachments) {
                    if (!"pdf".equals(AttachmentUtil.getAttachmentSuffix(attachment.getFilePath()))) {
                        map.put(attachment.getFileType(), Pictures.ofLocal(attachment.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(attachment.getFilePath(), 500)).create());
                        list.add(map);
                    }
                }
            }
            dataMap.put("pictures", list);
        }

        String templatePath = RuoYiConfig.getProfile() + "/templates/货物类响应文件模板.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/响应文件编制.docx";
        String genPath = s1 + "/响应文件编制2.pdf";
        FileUploadUtils.checkDirExists(s1);

        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }
            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            for (String key : procurementDocumentVo.getPdfFiles().keySet()) {
                genPath = PdfUtil.addPdf(genPath, procurementDocumentVo.getPdfFiles().get(key), key, s1);
            }
            genPath = PdfUtil.deleteBlankPageAndCreateFooter(genPath, s1);
            return genPath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String prepareServiceResponseDocuments(ProcurementDocumentVo procurementDocumentVo) {
        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentVo.getProjectId());
        BaseEntInfo entInfo = iBaseEntInfoService.getById(procurementDocumentVo.getEntId());
        Map<String, Object> dataMap = new HashMap<>();

        project.setLxr(project.getTendererName());
        project.setLxdh(project.getTendererPhone());
        dataMap.put("vo", procurementDocumentVo); // 供应商响应信息
        dataMap.put("project", project); // 项目、采购人信息
        dataMap.put("baseEntInfo", entInfo);  // 供应商企业信息
        dataMap.put("currentTimeNow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("contentMap", procurementDocumentVo.getContentMap());
        List<BusiAttachment> attachments = procurementDocumentVo.getAttachments();
        if (Objects.nonNull(attachments) && attachments.size() > 0) {
            Map<String, List<BusiAttachment>> groupedByType = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getFileType));
            List<Map<String, PictureRenderData>> list = new ArrayList<>();
            for (String s : groupedByType.keySet()) {
                //单种类型附件的集合
                List<BusiAttachment> busiAttachments = groupedByType.get(s);
                Map<String, PictureRenderData> map = new HashMap<>();
                for (BusiAttachment attachment : busiAttachments) {
                    if (!"pdf".equals(AttachmentUtil.getAttachmentSuffix(attachment.getFilePath()))) {
                        map.put(attachment.getFileType(), Pictures.ofLocal(attachment.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(attachment.getFilePath(), 500)).create());
                        list.add(map);
                    }
                }
            }
            dataMap.put("pictures", list);
        }

        String templatePath = RuoYiConfig.getProfile() + "/templates/服务类响应文件模板.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/响应文件编制.docx";
        String genPath = s1 + "/响应文件编制2.pdf";
        FileUploadUtils.checkDirExists(s1);

        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }

            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            for (String key : procurementDocumentVo.getPdfFiles().keySet()) {
                genPath = PdfUtil.addPdf(genPath, procurementDocumentVo.getPdfFiles().get(key), key, s1);
            }
            genPath = PdfUtil.deleteBlankPageAndCreateFooter(genPath, s1);
            return genPath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String prepareInquiryGoodsResponseDocuments(ProcurementDocumentVo procurementDocumentVo) {
        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentVo.getProjectId());
        BaseEntInfo entInfo = iBaseEntInfoService.getById(procurementDocumentVo.getEntId());
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, String> pdfFiles = procurementDocumentVo.getPdfFiles();

        String excelPath = pdfFiles.get("、明细报价表");
        try (InputStream inputStream = new FileInputStream(excelPath)) {
            // 转换Excel为TableRenderData，使用第一个工作表，从第1行开始，包含所有数据，将第一行作为表头
            TableRenderData tableData = ExcelToPoiTlConverter.convertToTableData(inputStream, null, 0, -1, true);
            dataMap.put("mxbjb", tableData);
            pdfFiles.remove("、明细报价表");
        } catch (IOException e) {
            e.printStackTrace();
        }

        project.setLxr(project.getTendererName());
        project.setLxdh(project.getTendererPhone());
        dataMap.put("vo", procurementDocumentVo); // 供应商响应信息
        dataMap.put("project", project); // 项目、采购人信息
        dataMap.put("baseEntInfo", entInfo);  // 供应商企业信息
        dataMap.put("currentTimeNow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("contentMap", procurementDocumentVo.getContentMap());
        List<BusiAttachment> attachments = procurementDocumentVo.getAttachments();
        if (Objects.nonNull(attachments) && attachments.size() > 0) {
            Map<String, List<BusiAttachment>> groupedByType = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getFileType));
            List<Map<String, PictureRenderData>> list = new ArrayList<>();
            for (String s : groupedByType.keySet()) {
                //单种类型附件的集合
                List<BusiAttachment> busiAttachments = groupedByType.get(s);
                Map<String, PictureRenderData> map = new HashMap<>();
                for (BusiAttachment attachment : busiAttachments) {
                    if (!"pdf".equals(AttachmentUtil.getAttachmentSuffix(attachment.getFilePath()))) {
                        map.put(attachment.getFileType(), Pictures.ofLocal(attachment.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(attachment.getFilePath(), 500)).create());
                        list.add(map);
                    }
                }
            }
            dataMap.put("pictures", list);
        }

        String templatePath = RuoYiConfig.getProfile() + "/templates/询价-货物类响应文件模板.docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/响应文件编制.docx";
        String genPath = s1 + "/响应文件编制2.pdf";
        FileUploadUtils.checkDirExists(s1);

        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }
            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            for (String key : procurementDocumentVo.getPdfFiles().keySet()) {
                genPath = PdfUtil.addPdf(genPath, procurementDocumentVo.getPdfFiles().get(key), key, s1);
            }
            genPath = PdfUtil.deleteBlankPageAndCreateFooter(genPath, s1);
            return genPath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String prepareResponseDocuments1(ProcurementDocumentVo vo, BusiTenderProject project, BaseEntInfo entInfo) {
//        BusiTenderProject project = iBusiTenderProjectService.getById(procurementDocumentVo.getProjectId());
//        BaseEntInfo entInfo = iBaseEntInfoService.getById(procurementDocumentVo.getEntId());
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, String> pdfFiles = vo.getPdfFiles();
        String excelPath = pdfFiles.get("）明细报价表-e");
        if (StringUtils.isNoneBlank(excelPath)) {
            try (InputStream inputStream = new FileInputStream(excelPath)) {
                // 转换Excel为TableRenderData，使用第一个工作表，从第1行开始，包含所有数据，将第一行作为表头
                TableRenderData tableData = ExcelToPoiTlConverter.convertToTableData(inputStream, null, 0, -1, true);
                dataMap.put("mxbjb", tableData);
                pdfFiles.remove("）明细报价表-e");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        project.setLxr(project.getTendererName());
        project.setLxdh(project.getTendererPhone());
        dataMap.put("vo", vo); // 供应商响应信息
        dataMap.put("project", project); // 项目、采购人信息
        dataMap.put("baseEntInfo", entInfo);  // 供应商企业信息
        dataMap.put("currentTimeNow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("contentMap", vo.getContentMap());
        List<BusiAttachment> attachments = vo.getAttachments();
        if (Objects.nonNull(attachments) && attachments.size() > 0) {
            Map<String, List<BusiAttachment>> groupedByType = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getFileType));
            List<Map<String, PictureRenderData>> list = new ArrayList<>();
            for (String s : groupedByType.keySet()) {
                //单种类型附件的集合
                List<BusiAttachment> busiAttachments = groupedByType.get(s);
                Map<String, PictureRenderData> map = new HashMap<>();
                for (BusiAttachment attachment : busiAttachments) {
                    if (!"pdf".equals(AttachmentUtil.getAttachmentSuffix(attachment.getFilePath()))) {
                        map.put(attachment.getFileType(), Pictures.ofLocal(attachment.getFilePath()).size(500, ImageUtils.getAutoHeightByWidth(attachment.getFilePath(), 500)).create());
                        list.add(map);
                    }
                }
            }
            dataMap.put("pictures", list);
        }

        String templatePath = RuoYiConfig.getProfile() + "/templates/"+vo.getTemplateName()+".docx";
        String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + project.getProjectId();
        String originPath = s1 + "/响应文件编制.docx";
        String genPath = s1 + "/响应文件编制2.pdf";
        FileUploadUtils.checkDirExists(s1);

        try {
            XWPFTemplate.compile(templatePath)
                    .render(dataMap)
                    .writeToFile(originPath);
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            License license = new License();
            license.setLicense(is);
            // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            if (active.equals("release")){
                // 指定Linux系统上的中文字体目录
                String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
            }
            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
            System.out.println("PDF文件地址：" + genPath);
            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
            document.save(genPath, saveOptions);

            for (String key : vo.getPdfFiles().keySet()) {
                genPath = PdfUtil.addPdf(genPath, vo.getPdfFiles().get(key), key, s1);
            }
            genPath = PdfUtil.deleteBlankPageAndCreateFooter(genPath, s1);
            return genPath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static void main(String[] args) throws Exception {
//        String originPath = "C://Users//a2685//Pictures//shuiyin//限额以下模板文件//响应文件编制模板111.docx";
//        String genPath = "C://Users//a2685//Pictures//shuiyin//限额以下模板文件//响应文件编制模板111.pdf";
//        //转PDF
//        try {
//            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
//            byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
//            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
//            License license = new License();
//            license.setLicense(is);
//            // 指定Linux系统上的中文字体目录
////            String chineseF0ontsPath = "/usr/share/fonts/chinese";
////            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
//
//            com.aspose.words.Document document = new com.aspose.words.Document(originPath);
//            System.out.println("PDF文件地址："+genPath);
//            // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
//            PdfSaveOptions saveOptions = new PdfSaveOptions();
//            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
//            document.save(genPath, saveOptions);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        byte[] bytes = Pictures.ofLocal("d:/111.jpg").size(500, ImageUtils.getAutoHeightByWidth("d:/111.jpg",500)).create().readPictureData();
//        byte[] bytes = Pictures.ofLocal("d:/111.jpg,d:/111222.jpg").size(100, 100).create().readPictureData();
//        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
//        BufferedImage image = ImageIO.read(bis);
//        File outputFile = new File("d:/11222.jpg");
//        ImageIO.write(image, "jpg", outputFile);
//        bis.close();
//        Document srcDoc = new Document("d:/111.docx");
//        Document dstDoc = new Document("d:/file.pdf");
//        dstDoc.appendDocument(srcDoc, ImportFormatMode.KEEP_DIFFERENT_STYLES);
//        dstDoc.save("d://333.docx");
    }
}

