package com.ruoyi.procurement.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.Serializable;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.procurement.mapper.ProcurementDocumentsUitemMapper;
import com.ruoyi.procurement.domain.ProcurementDocumentsUitem;
import com.ruoyi.procurement.service.IProcurementDocumentsUitemService;

/**
 * 用户编制采购文件信息保存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ProcurementDocumentsUitemServiceImpl extends ServiceImpl<ProcurementDocumentsUitemMapper, ProcurementDocumentsUitem> implements IProcurementDocumentsUitemService {
    /**
     * 查询用户编制采购文件信息保存列表
     *
     * @param procurementDocumentsUitem 用户编制采购文件信息保存
     * @return 用户编制采购文件信息保存
     */
    @Override
    public List<ProcurementDocumentsUitem> selectList(ProcurementDocumentsUitem procurementDocumentsUitem) {
        QueryWrapper<ProcurementDocumentsUitem> procurementDocumentsUitemQueryWrapper = new QueryWrapper<>();
                        procurementDocumentsUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUitem.getProjectFileId()),"project_file_id",procurementDocumentsUitem.getProjectFileId());
                        procurementDocumentsUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUitem.getEntId()),"ent_id",procurementDocumentsUitem.getEntId());
                        procurementDocumentsUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(procurementDocumentsUitem.getFilePath()),"file_path",procurementDocumentsUitem.getFilePath());
            procurementDocumentsUitemQueryWrapper.apply(
                ObjectUtil.isNotEmpty(procurementDocumentsUitem.getParams().get("dataScope")),
        procurementDocumentsUitem.getParams().get("dataScope")+""
        );
        return list(procurementDocumentsUitemQueryWrapper);
    }

    @Override
    public List<ProcurementDocumentsUitem> listByInfoId(Long entFileId) {
        return list(new QueryWrapper<ProcurementDocumentsUitem>().eq("ent_file_id",entFileId));
    }




}
