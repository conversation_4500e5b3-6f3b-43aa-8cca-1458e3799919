package com.ruoyi.procurement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class BidOpenIngVo {
    @JsonProperty("isFixed")
    private Boolean isFixed;
    @JsonProperty("code")
    private String code;
    @JsonProperty("openBid")
    private String openBid;
    @JsonProperty("defaultValue")
    private String defaultValue;
    @JsonProperty("remark")
    private String remark;
}
