package com.ruoyi.procurement.vo;

import lombok.Data;

@Data
public class ProcurementDemandVo {


    /**
     * substantiveReq : {"requirement":"测试实质性要求填写"}
     * quantityParamReq : {"pdfUrl":"/profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf"}
     * paramReq : {"requirement":"测试商务需求内容"}
     */

    private SubstantiveReqBean substantiveReq;
    private QuantityParamReqBean quantityParamReq;
    private ParamReqBean paramReq;

@Data
    public static class SubstantiveReqBean {
        /**
         * requirement : 测试实质性要求填写
         */

        private String requirement;

    }
    @Data
    public static class QuantityParamReqBean {
        /**
         * pdfUrl : /profile/upload/608b36ec6c7d45bda389f27e92cd14e6.pdf
         */
        private String pdfUrl;
        private String requirement;

    }
    @Data
    public static class ParamReqBean {
        /**
         * requirement : 测试商务需求内容
         */
        private String requirement;

    }
}
