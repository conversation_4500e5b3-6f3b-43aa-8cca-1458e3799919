package com.ruoyi.procurement.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.*;

@Data
public class ProcurementDocumentVo {
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 项目开始时间
     */
    @ApiModelProperty("成立时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date establishmentTime;


    @ApiModelProperty("经营期限")
    private Integer operatingPeriod;

    /**
     * 法人信息，姓名，性别，年龄，职务
     */
    @ApiModelProperty("法人姓名")
    private String legalPersonName;


    @ApiModelProperty("法人年龄")
    private Integer legalPersonAge;

    @ApiModelProperty("法人性别")
    private String legalPersonSex;

    @ApiModelProperty("法人职务")
    private String legalPersonPosition;

    /**
     * 委托人
     */
    @ApiModelProperty("委托人姓名")
    private String entrustPersonName;

    @ApiModelProperty("委托期限")
    private String entrustDeadline;

    @ApiModelProperty("所属行业")
    private String trade;

    @ApiModelProperty("从业人员（人数）")
    private String practitionerNum;

    @ApiModelProperty("营业收入（万元）")
    private BigDecimal operatingRevenue;

    @ApiModelProperty("资产总额")
    private BigDecimal totalAssets;

    @ApiModelProperty("公司类型")
    private String companyType;


    @ApiModelProperty("是否残疾人福利性企业")
    private Integer isWelfareCompany;

    @ApiModelProperty("工期")
    private Integer overTimeLimit;

    @ApiModelProperty("质保期")
    private Integer warrantyPeriod;

    @ApiModelProperty("质量标准")
    private String qualityDemand;
    @ApiModelProperty("投标人")
    private String bidderName;
    @ApiModelProperty("投标报价大写")
    private String bidAmountCapitalization;
    @ApiModelProperty("投标报价小写")
    private Integer bidAmount;
    @ApiModelProperty("投标有效期")
    private String bidValidPeriod;

    @ApiModelProperty("法定代表人身份证明")
    private boolean fddbrsfzm;
    @ApiModelProperty("授权委托书")
    private boolean sqwts;
    @ApiModelProperty("中小企业声明函")
    private boolean zxqysmh;
    @ApiModelProperty("中小企业声明函细项")
    private String zxqysmhxx;
    @ApiModelProperty("残疾人福利性单位声明函")
    private boolean cjrfuxdwsmh;
    @ApiModelProperty("监狱企业")
    private boolean jyqy;
    @ApiModelProperty("信用承诺函")
    private boolean xycnh;
    @ApiModelProperty("信用截图")
    private boolean xyjt;

    @ApiModelProperty("商务部分")
    private boolean swbf1;
    @ApiModelProperty("商务部分2")
    private boolean swbf2;
    @ApiModelProperty("商务部分3")
    private boolean swbf3;
    @ApiModelProperty("商务部分4")
    private boolean swbf4;
    @ApiModelProperty("商务部分5")
    private boolean swbf5;
    @ApiModelProperty("商务部分6")
    private boolean swbf6;
    @ApiModelProperty("商务部分名称")
    private String swbfName1;
    @ApiModelProperty("商务部分名称2")
    private String swbfName2;
    @ApiModelProperty("商务部分名称3")
    private String swbfName3;
    @ApiModelProperty("商务部分名称4")
    private String swbfName4;
    @ApiModelProperty("商务部分名称5")
    private String swbfName5;
    @ApiModelProperty("商务部分名称6")
    private String swbfName6;

    @ApiModelProperty("模板名称")
    private String templateName;


    private List<BidInformation> bidInformation;

    private Map<String, String> contentMap = new HashMap<>();
    /**
     * 附件
     */
    private List<BusiAttachment> attachments = new ArrayList<>();

    private Map<String, String> pdfFiles = new HashMap<>();

    public void addPdfFile(String key, String value){
        if(StringUtils.isNoneBlank(pdfFiles.get(key))){
            String v = pdfFiles.get(key);
            pdfFiles.put(key, v + "," + value);
        }else{
            pdfFiles.put(key, value);
        }
    }

    //key是因素名称，对应附件list
    private  Map<String,List<BusiAttachment>> scoringMethodUitemAttr=new HashMap<>();
}
