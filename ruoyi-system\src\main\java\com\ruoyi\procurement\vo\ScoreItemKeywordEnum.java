package com.ruoyi.procurement.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * 磋商工程的关键字对应关系
 */
public enum ScoreItemKeywordEnum {
    /*
    工程类评审因素
     */
    //资格性评审
    GC_ZG_FHESET(1, 0, "符合《中华人民共和国政府采购法》第二十二条规定", "、资格条件承诺函"),
    GC_ZG_TDZGYQ(1, 0, "特定资格要求", "、特定资格要求"),
    GC_ZG_XYCX(1, 0, "信用查询", "、信用查询"),
    //符合型评审
    GC_FH_XYRMC(1, 0, "响应人名称", "（二）开标一览表"),
    GC_FH_QZGZ(1, 0, "签字盖章", "（二）开标一览表"),
    GC_FH_YBJGCLQD(1, 0, "已标价工程量清单", "五、已标价工程量清单"),
    GC_FH_HTLXQX(1, 0, "合同履行期限", "（二）开标一览表"),
    GC_FH_ZBQ(1, 0, "质保期", "（二）开标一览表"),
    GC_FH_TBBJ(1, 0, "投标报价", "（二）开标一览表"),
    GC_FH_QTYQ(1, 0, "其他要求", "（二）开标一览表"),
    //技术标评审
    GC_JS_SGFA(1, 0, "施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划",
            "1、施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划"),
    GC_JS_GCJD(1, 0, "工程进度计划与措施、施工进度或施工网络图、施工总平面布置图", "、工程进度计划与措施、施工进度或施工网络图、施工总平面布置图"),
    GC_JS_JNJP(1, 0, "节能减排（绿色施工、工艺创新）在本工程的具体应用措施", "、节能减排（绿色施工、工艺创新）在本工程的具体应用措施"),
    GC_JS_XGY(1, 0, "新工艺（新技术、新设备、新材料）的采用程度", "、新工艺（新技术、新设备、新材料）的采用程度"),
    GC_JS_FXGLCS(1, 0, "风险管理措施", "、风险管理措施"),
    //商务标评审
    GC_SW_NPXMGLRY(1, 0, "拟派项目管理人员", "、拟派项目管理人员"),
    GC_SW_XMJSFZR(1, 0, "项目技术负责人", "、项目技术负责人"),
    GC_SW_TXRZ(1, 0, "体系认证", "、体系认证"),
    GC_SW_LSYJ(1, 0, "类似业绩", "、类似业绩"),

    /*
    服务类评审因素
     */
    //资格性评审
    FW_ZG_FHESET(1, 1, "符合《中华人民共和国政府采购法》第二十二条规定", "、资格条件承诺函"),
    FW_ZG_TDZGYQ(1, 1, "特定资格要求", "、特定资格要求"),
    FW_ZG_XYCX(1, 1, "信用查询", "、信用查询"),
    //符合型评审
    FW_FH_XYRMC(1, 1, "响应人名称", "（二）开标一览表"),
    FW_FH_XYWJGS(1, 1, "响应文件格式", "（二）开标一览表"),
    FW_FH_QZGZ(1, 1, "签字盖章", "（二）开标一览表"),
    FW_FH_FWQX(1, 1, "服务期限", "（二）开标一览表"),
    FW_FH_TBBJ(1, 1, "投标报价", "（二）开标一览表"),
    FW_FH_QTYQ(1, 1, "其他要求", "（二）开标一览表"),
    //技术标评审
    FW_JS_FWFA(1, 1, "服务方案","、服务方案"),
    //商务标评审
    FW_SW_XMZRY(1, 1, "项目组人员", "、项目组人员"),
    FW_SW_LSYJ(1, 1, "类似业绩", "、类似业绩"),
    FW_SW_TXRZ(1, 1, "体系认证", "、体系认证"),
    FW_SW_YQSB(1, 1, "仪器设备", "、仪器设备"),
    /*
    货物类评审因素
     */
    //资格性评审
    HW_ZG_FHESET(1, 2, "符合《中华人民共和国政府采购法》第二十二条规定", "、资格条件承诺函"),
    HW_ZG_TDZGYQ(1, 2, "特定资格要求", "、特定资格要求"),
    HW_ZG_XYCX(1, 2, "信用查询", "、信用查询"),
    //符合型评审
    HW_FH_XYRMC(1, 2, "响应人名称", "（二）开标一览表"),
    HW_FH_XYWJGS(1, 2, "响应文件格式", "（二）开标一览表"),
    HW_FH_QZGZ(1, 2, "签字盖章", "（二）开标一览表"),
    HW_FH_GHQ(1, 2, "供货期", "（二）开标一览表"),
    HW_FH_TBBJ(1, 2, "投标报价", "（二）开标一览表"),
    HW_FH_QTYQ(1, 2, "其他要求", "（二）开标一览表"),
    //技术标评审
    HW_JS_KLHJSCS(1, 2, "可量化技术参数", "、技术偏离表"),
    //商务标评审
    HW_SW_JNHB(1, 2, "节能环保", "、节能环保"),
    HW_SW_SHFW(1, 2, "售后服务", "、售后服务"),
    HW_SW_TXRZ(1, 2, "体系认证", "、体系认证"),
    HW_SW_ZSCQ(1, 2, "知识产权", "、知识产权"),
    HW_SW_LSYJ(1, 2, "类似业绩", "、类似业绩"),
    HW_SW_ZBQ(1, 2, "质保期加分项", "、质保期加分项"),
    /*
    询价货物类评审因素
     */
    //资格性评审
    XJ_HW_ZG_FHESET(3, 2, "符合《中华人民共和国政府采购法》第二十二条规定", "、资格条件承诺函"),
    XJ_HW_ZG_TDZGYQ(3, 2, "特定资格要求", "（2）资质证明"),
    XJ_HW_ZG_XYCX(3, 2, "信用查询", "、信用查询"),
    XJ_HW_ZG_BSLHT(3, 2, "不是联合体", "、资格条件承诺函"),
    //符合型评审
    XJ_HW_FH_XYRMC(3, 2, "响应人名称", "（1）营业执照"),
    XJ_HW_FH_XYNR(3, 2, "响应内容", "1、技术偏离表"),
    XJ_HW_FH_CGXQ(3, 2, "采购需求", "（二）开标一览表"),
    XJ_HW_FH_GHQX(3, 2, "供货期限", "（二）开标一览表"),
    XJ_HW_FH_TBBJ(3, 2, "投标报价", "（二）开标一览表"),;
    /*
    采购方式
     */
    private Integer tenderMode;
    /*
    项目类别
     */
    private Integer projectType;
    private String scoreItemName;
    private String keyword;

    ScoreItemKeywordEnum(Integer tenderMode, Integer projectType, String scoreItemName, String keyword) {
        this.tenderMode = tenderMode;
        this.projectType = projectType;
        this.scoreItemName = scoreItemName;
        this.keyword = keyword;
    }

    public Integer getTenderMode() {
        return tenderMode;
    }
    public Integer getProjectType() {
        return projectType;
    }

    public String getKeyword() {
        return keyword;
    }

    public String getScoreItemName() {
        return scoreItemName;
    }

    public static List<ScoreItemKeywordEnum> getKeywordList(String tenderMode, String projectType){
        return getKeywordList(Integer.valueOf(tenderMode), Integer.valueOf(projectType));
    }
    public static List<ScoreItemKeywordEnum> getKeywordList(Integer tenderMode, Integer projectType){
        ScoreItemKeywordEnum[] enums = ScoreItemKeywordEnum.values();
        List<ScoreItemKeywordEnum> keywords = new ArrayList<>();
        for (ScoreItemKeywordEnum e : enums) {
            if (e.getTenderMode().equals(tenderMode) && e.getProjectType().equals(projectType)) {
                keywords.add(e);
            }
        }
        return keywords;
    }
}
