package com.ruoyi.procurement.vo;

import lombok.Data;

@Data
public class ServiceVo {

    /**
     * projectOverview : {"requirement":"想啊想啊"}
     * standardsAndReq : {"requirement":"欣赏欣赏"}
     */

    private ProjectOverviewBean projectOverview;
    private StandardsAndReqBean standardsAndReq;

    public ProjectOverviewBean getProjectOverview() {
        return projectOverview;
    }

    public void setProjectOverview(ProjectOverviewBean projectOverview) {
        this.projectOverview = projectOverview;
    }

    public StandardsAndReqBean getStandardsAndReq() {
        return standardsAndReq;
    }

    public void setStandardsAndReq(StandardsAndReqBean standardsAndReq) {
        this.standardsAndReq = standardsAndReq;
    }

    public static class ProjectOverviewBean {
        /**
         * requirement : 想啊想啊
         */

        private String requirement;

        public String getRequirement() {
            return requirement;
        }

        public void setRequirement(String requirement) {
            this.requirement = requirement;
        }
    }

    public static class StandardsAndReqBean {
        /**
         * requirement : 欣赏欣赏
         */

        private String requirement;

        public String getRequirement() {
            return requirement;
        }

        public void setRequirement(String requirement) {
            this.requirement = requirement;
        }
    }
}
