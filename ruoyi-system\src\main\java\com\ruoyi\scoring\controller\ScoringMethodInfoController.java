package com.ruoyi.scoring.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.service.IScoringMethodInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 评分办法信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "评分办法信息管理")
@RestController
@RequestMapping("/method/info")
public class ScoringMethodInfoController extends BaseController {
    @Autowired
    private IScoringMethodInfoService scoringMethodInfoService;

/**
 * 查询评分办法信息列表
 */
@PreAuthorize("@ss.hasPermi('method:info:list')")
@ApiOperation(value = "查询评分办法信息列表")
@GetMapping("/list")
    public TableDataInfo list(ScoringMethodInfo scoringMethodInfo) {
        startPage();
        List<ScoringMethodInfo> list = scoringMethodInfoService.selectList(scoringMethodInfo);
        return getDataTable(list);
    }

    /**
     * 查询评分办法信息列表
     */
    @PreAuthorize("@ss.hasPermi('documents:info:list')")
    @ApiOperation(value = "查询采购文件编制基础信息列表")
    @GetMapping("/infoByParams")
    public AjaxResult infoByParams(ScoringMethodInfo scoringMethodInfo) {
        ScoringMethodInfo info = scoringMethodInfoService.infoByParams(scoringMethodInfo);
        return success(info);
    }

    /**
     * 导出评分办法信息列表
     */
    @PreAuthorize("@ss.hasPermi('method:info:export')")
    @Log(title = "评分办法信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出评分办法信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoringMethodInfo scoringMethodInfo) {
        List<ScoringMethodInfo> list = scoringMethodInfoService.selectList(scoringMethodInfo);
        ExcelUtil<ScoringMethodInfo> util = new ExcelUtil<ScoringMethodInfo>(ScoringMethodInfo. class);
        util.exportExcel(response, list, "评分办法信息数据");
    }

    /**
     * 获取评分办法信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:info:query')")
    @ApiOperation(value = "获取评分办法信息详细信息")
    @ApiImplicitParam(name = "scoringMethodId", value = "评分办法id", required = true, dataType = "Long")
    @GetMapping(value = "/{scoringMethodId}")
    public AjaxResult getInfo(@PathVariable("scoringMethodId")Long scoringMethodId) {
        return success(scoringMethodInfoService.getById(scoringMethodId));
    }

    /**
     * 新增评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:info:add')")
    @Log(title = "评分办法信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增评分办法信息")
    @PostMapping
    public AjaxResult add(@RequestBody ScoringMethodInfo scoringMethodInfo) {
        return toAjax(scoringMethodInfoService.save(scoringMethodInfo));
    }

    /**
     * 修改评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:info:edit')")
    @Log(title = "评分办法信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改评分办法信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ScoringMethodInfo scoringMethodInfo) {
        return toAjax(scoringMethodInfoService.updateById(scoringMethodInfo));
    }

    /**
     * 删除评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:info:remove')")
    @Log(title = "评分办法信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除评分办法信息")
    @DeleteMapping("/{scoringMethodIds}")
    public AjaxResult remove(@PathVariable Long[] scoringMethodIds) {
        return toAjax(scoringMethodInfoService.removeByIds(Arrays.asList(scoringMethodIds)));
    }
}
