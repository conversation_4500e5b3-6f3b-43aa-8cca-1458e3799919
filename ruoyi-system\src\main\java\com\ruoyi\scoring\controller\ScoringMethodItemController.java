package com.ruoyi.scoring.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 评分办法详细信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "评分办法详细信息管理")
@RestController
@RequestMapping("/method/item")
public class ScoringMethodItemController extends BaseController {
    @Autowired
    private IScoringMethodItemService scoringMethodItemService;

/**
 * 查询评分办法详细信息列表
 */
@PreAuthorize("@ss.hasPermi('method:item:list')")
@ApiOperation(value = "查询评分办法详细信息列表")
@GetMapping("/list")
    public TableDataInfo list(ScoringMethodItem scoringMethodItem) {
        startPage();
        List<ScoringMethodItem> list = scoringMethodItemService.selectList(scoringMethodItem);
        return getDataTable(list);
    }

    /**
     * 导出评分办法详细信息列表
     */
    @PreAuthorize("@ss.hasPermi('method:item:export')")
    @Log(title = "评分办法详细信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出评分办法详细信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoringMethodItem scoringMethodItem) {
        List<ScoringMethodItem> list = scoringMethodItemService.selectList(scoringMethodItem);
        ExcelUtil<ScoringMethodItem> util = new ExcelUtil<ScoringMethodItem>(ScoringMethodItem. class);
        util.exportExcel(response, list, "评分办法详细信息数据");
    }

    /**
     * 获取评分办法详细信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:item:query')")
    @ApiOperation(value = "获取评分办法详细信息详细信息")
    @ApiImplicitParam(name = "scoringMethodItemId", value = "评分办法id", required = true, dataType = "Long")
    @GetMapping(value = "/{scoringMethodItemId}")
    public AjaxResult getInfo(@PathVariable("scoringMethodItemId")Long scoringMethodItemId) {
        return success(scoringMethodItemService.getById(scoringMethodItemId));
    }

    /**
     * 新增评分办法详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:item:add')")
    @Log(title = "评分办法详细信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增评分办法详细信息")
    @PostMapping
    public AjaxResult add(@RequestBody ScoringMethodItem scoringMethodItem) {
        return toAjax(scoringMethodItemService.save(scoringMethodItem));
    }

    /**
     * 修改评分办法详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:item:edit')")
    @Log(title = "评分办法详细信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改评分办法详细信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ScoringMethodItem scoringMethodItem) {
        return toAjax(scoringMethodItemService.updateById(scoringMethodItem));
    }

    /**
     * 删除评分办法详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:item:remove')")
    @Log(title = "评分办法详细信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除评分办法详细信息")
    @DeleteMapping("/{scoringMethodItemIds}")
    public AjaxResult remove(@PathVariable Long[] scoringMethodItemIds) {
        return toAjax(scoringMethodItemService.removeByIds(Arrays.asList(scoringMethodItemIds)));
    }
}
