package com.ruoyi.scoring.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 用户评分办法信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "用户评分办法信息管理")
@RestController
@RequestMapping("/method/uinfo")
public class ScoringMethodUinfoController extends BaseController {
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;

/**
 * 查询用户评分办法信息列表
 */
@PreAuthorize("@ss.hasPermi('method:uinfo:list')")
@ApiOperation(value = "查询用户评分办法信息列表")
@GetMapping("/list")
    public TableDataInfo list(ScoringMethodUinfo scoringMethodUinfo) {
        startPage();
        List<ScoringMethodUinfo> list = scoringMethodUinfoService.selectList(scoringMethodUinfo);
        return getDataTable(list);
    }

    /**
     * 导出用户评分办法信息列表
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:export')")
    @Log(title = "用户评分办法信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出用户评分办法信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoringMethodUinfo scoringMethodUinfo) {
        List<ScoringMethodUinfo> list = scoringMethodUinfoService.selectList(scoringMethodUinfo);
        ExcelUtil<ScoringMethodUinfo> util = new ExcelUtil<ScoringMethodUinfo>(ScoringMethodUinfo. class);
        util.exportExcel(response, list, "用户评分办法信息数据");
    }

    /**
     * 获取用户评分办法信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:query')")
    @ApiOperation(value = "获取用户评分办法信息详细信息")
    @ApiImplicitParam(name = "entMethodId", value = "用户选择评分办法id", required = true, dataType = "Long")
    @GetMapping(value = "/{entMethodId}")
    public AjaxResult getInfo(@PathVariable("entMethodId")Long entMethodId) {
        return success(scoringMethodUinfoService.getById(entMethodId));
    }
    @GetMapping(value = "/getInfoByProjectId/{projectId}/{resultId}")
    public AjaxResult getInfoByProjectId(@PathVariable("projectId")Long projectId,@PathVariable("resultId")Long resultId) {

        return AjaxResult.success(scoringMethodUinfoService.getInfoByProjectId(projectId,resultId));
    }
    /**
     * 新增用户评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:add')")
    @Log(title = "用户评分办法信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户评分办法信息")
    @PostMapping
    public AjaxResult add(@RequestBody ScoringMethodUinfo scoringMethodUinfo) {
        return toAjax(scoringMethodUinfoService.save(scoringMethodUinfo));
    }

    /**
     * 新增用户评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:add')")
    @Log(title = "用户评分办法信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户评分办法信息")
    @PostMapping("/saveInfo")
    public AjaxResult saveInfo(@RequestBody ScoringMethodUinfo scoringMethodUinfo) {
        return success(scoringMethodUinfoService.saveInfo(scoringMethodUinfo));
    }

    /**
     * 修改用户评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:edit')")
    @Log(title = "用户评分办法信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改用户评分办法信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ScoringMethodUinfo scoringMethodUinfo) {
        return toAjax(scoringMethodUinfoService.updateById(scoringMethodUinfo));
    }

    /**
     * 删除用户评分办法信息
     */
    @PreAuthorize("@ss.hasPermi('method:uinfo:remove')")
    @Log(title = "用户评分办法信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除用户评分办法信息")
    @DeleteMapping("/{entMethodIds}")
    public AjaxResult remove(@PathVariable Long[] entMethodIds) {
        return toAjax(scoringMethodUinfoService.removeByIds(Arrays.asList(entMethodIds)));
    }
}
