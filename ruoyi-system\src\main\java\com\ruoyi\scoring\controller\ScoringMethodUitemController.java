package com.ruoyi.scoring.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 用户评分办法因素Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Api(tags = "用户评分办法因素管理")
@RestController
@RequestMapping("/method/uitem")
public class ScoringMethodUitemController extends BaseController {
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;

/**
 * 查询用户评分办法因素列表
 */
@PreAuthorize("@ss.hasPermi('method:uitem:list')")
@ApiOperation(value = "查询用户评分办法因素列表")
@GetMapping("/list")
    public TableDataInfo list(ScoringMethodUitem scoringMethodUitem) {
        startPage();
        List<ScoringMethodUitem> list = scoringMethodUitemService.selectList(scoringMethodUitem);
        return getDataTable(list);
    }

    /**
     * 导出用户评分办法因素列表
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:export')")
    @Log(title = "用户评分办法因素", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出用户评分办法因素列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoringMethodUitem scoringMethodUitem) {
        List<ScoringMethodUitem> list = scoringMethodUitemService.selectList(scoringMethodUitem);
        ExcelUtil<ScoringMethodUitem> util = new ExcelUtil<ScoringMethodUitem>(ScoringMethodUitem. class);
        util.exportExcel(response, list, "用户评分办法因素数据");
    }

    /**
     * 获取用户评分办法因素详细信息
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:query')")
    @ApiOperation(value = "获取用户评分办法因素详细信息")
    @ApiImplicitParam(name = "entMethodItemId", value = "评分办法id", required = true, dataType = "Long")
    @GetMapping(value = "/{entMethodItemId}")
    public AjaxResult getInfo(@PathVariable("entMethodItemId")Long entMethodItemId) {
        return success(scoringMethodUitemService.getById(entMethodItemId));
    }

    /**
     * 新增用户评分办法因素
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:add')")
    @Log(title = "用户评分办法因素", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户评分办法因素")
    @PostMapping
    public AjaxResult add(@RequestBody ScoringMethodUitem scoringMethodUitem) {
        return toAjax(scoringMethodUitemService.save(scoringMethodUitem));
    }

    /**
     * 修改用户评分办法因素
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:edit')")
    @Log(title = "用户评分办法因素", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改用户评分办法因素")
    @PutMapping
    public AjaxResult edit(@RequestBody ScoringMethodUitem scoringMethodUitem) {
        return toAjax(scoringMethodUitemService.updateById(scoringMethodUitem));
    }

    /**
     * 删除用户评分办法因素
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:remove')")
    @Log(title = "用户评分办法因素", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除用户评分办法因素")
    @DeleteMapping("/{entMethodItemIds}")
    public AjaxResult remove(@PathVariable Long[] entMethodItemIds) {
        return toAjax(scoringMethodUitemService.removeByIds(Arrays.asList(entMethodItemIds)));
    }
}
