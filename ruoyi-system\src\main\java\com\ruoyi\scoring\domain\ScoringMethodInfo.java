package com.ruoyi.scoring.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;
import java.util.List;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 评分办法信息对象 scoring_method_info
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("评分办法信息对象")
@TableName(resultMap = "com.ruoyi.scoring.mapper.ScoringMethodInfoMapper.ScoringMethodInfoResult")
public class ScoringMethodInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long scoringMethodId;
    /**
     * 评分办法名称
     */
    @ApiModelProperty("评分办法名称")
    @Excel(name = "评分办法名称")
    private String methodName;
    /**
     * 评分办法编号
     */
    @ApiModelProperty("评分办法编号")
    @Excel(name = "评分办法编号")
    private String methodCode;
    /**
     * 评分办法配置文件
     */
    @ApiModelProperty("评分办法配置文件")
    @Excel(name = "评分办法配置文件")
    private String methodFile;
    /**
     * 采购方式（字典）
     */
    @ApiModelProperty("采购方式（字典）")
    @Excel(name = "采购方式", readConverterExp = "字=典")
    private Integer tenderMode;
    /**
     * 项目类别（字典）
     */
    @ApiModelProperty("项目类别（字典）")
    @Excel(name = "项目类别", readConverterExp = "字=典")
    private Integer projectType;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private ScoringMethodUinfo uInfo;

    @TableField(exist = false)
    private List<ScoringMethodItem> items;


    @TableField(exist = false)
    private Integer score;
}
