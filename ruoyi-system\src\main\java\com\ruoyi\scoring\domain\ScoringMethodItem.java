package com.ruoyi.scoring.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 评分办法详细信息对象 scoring_method_item
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("评分办法详细信息对象")
@TableName(resultMap = "com.ruoyi.scoring.mapper.ScoringMethodItemMapper.ScoringMethodItemResult")
public class ScoringMethodItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long scoringMethodItemId;
    /**
     * 评分信息id
     */
    @ApiModelProperty("评分信息id")
    @Excel(name = "评分信息id")
    private Long scoringMethodId;
    /**
     * 评分办法名称
     */
    @ApiModelProperty("评分办法名称")
    @Excel(name = "评分办法名称")
    private String itemName;
    /**
     * 评分办法编号
     */
    @ApiModelProperty("评分办法编号")
    @Excel(name = "评分办法编号")
    private String itemCode;
    /**
     * 评分办法配置文件（json格式）
     */
    @ApiModelProperty("评分办法配置文件（json格式）")
    @Excel(name = "评分办法配置文件", readConverterExp = "j=son格式")
    private String itemContent;
    /**
     * 评分办法类型（字典）
     */
    @ApiModelProperty("评分办法类型（字典）")
    @Excel(name = "评分办法类型", readConverterExp = "字=典")
    private Integer itemType;
    /**
     * 评分办法模式（字典）
     */
    @ApiModelProperty("评分办法模式（字典）")
    @Excel(name = "评分办法模式", readConverterExp = "字=典")
    private Integer itemMode;

    private Integer itemSort;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private List<ScoringMethodUitem> uItems;
    @TableField(exist = false)
    private Integer score;
    @TableField(exist = false)
    private Integer status;
    @TableField(exist = false)
    EvalProjectEvaluationProcess evalProjectEvaluationProcess;

    @TableField(exist = false)
    Map<Long, List<EvalExpertEvaluationDetail>> map;
}
