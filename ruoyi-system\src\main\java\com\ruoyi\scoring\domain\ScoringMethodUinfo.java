package com.ruoyi.scoring.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户评分办法信息对象 scoring_method_uinfo
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("用户评分办法信息对象")
@TableName(resultMap = "com.ruoyi.scoring.mapper.ScoringMethodUinfoMapper.ScoringMethodUinfoResult")
public class ScoringMethodUinfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户选择评分办法id
     */
    @ApiModelProperty("用户选择评分办法id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entMethodId;
    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @Excel(name = "评分办法id")
    private Long scoringMethodId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 评分办法配置文件
     */
    @ApiModelProperty("评分办法配置文件")
    @Excel(name = "评分办法配置文件")
    private String methodFile;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private String itemCode;

    //评审细则
    @TableField(exist = false)
    List<ScoringMethodItem> scoringMethodItems;

    //评审因素
    @TableField(exist = false)
    List<ScoringMethodUitem> scoringMethodUitems;
}
