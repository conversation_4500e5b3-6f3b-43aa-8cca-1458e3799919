package com.ruoyi.scoring.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户评分办法因素对象 scoring_method_uitem
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ApiModel("用户评分办法因素对象")
@TableName(resultMap = "com.ruoyi.scoring.mapper.ScoringMethodUitemMapper.ScoringMethodUitemResult")
public class ScoringMethodUitem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entMethodItemId;

    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法uinfoid")
    private Long entMethodId;


    /**
     * 评分办法id
     */
    @ApiModelProperty("评分办法id")
    @Excel(name = "评分办法id")
    private Long scoringMethodId;
    /**
     * 评分办法细则名称
     */
    @ApiModelProperty("评分办法细则名称")
    @Excel(name = "评分办法细则名称")
    private Long scoringMethodItemId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @Excel(name = "企业id")
    private Long entId;
    /**
     * 评审因素
     */
    @ApiModelProperty("评审因素")
    @Excel(name = "评审因素")
    private String itemName;
    /**
     * 评审内容
     */
    @ApiModelProperty("评审内容")
    @Excel(name = "评审内容")
    private String itemRemark;
    /**
     * 最低分
     */
    @ApiModelProperty("最低分")
    @Excel(name = "最低分")
    private Integer minScore;
    /**
     * 最高分
     */
    @ApiModelProperty("最高分")
    @Excel(name = "最高分")
    private Integer maxScore;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 分数挡位
     */
    @ApiModelProperty("分数挡位")
    @Excel(name = "分数挡位")
    private String scoreLevel;
    private Integer uitemSort;

    /**
     * 评分办法细则分值
     */
    @ApiModelProperty("评分办法细则分值")
    @Excel(name = "评分办法细则分值")
    private Integer score=0;

    @TableField(exist = false)
    private  List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails;

    @TableField(exist = false)
    private Map<String,Map<Long, BigDecimal> > map=new HashMap<>();
    //private  Map<String, Object> map=new HashMap<>();
}
