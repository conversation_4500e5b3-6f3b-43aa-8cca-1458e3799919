package com.ruoyi.scoring.service;

import java.util.List;

import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 评分办法信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IScoringMethodInfoService extends IService<ScoringMethodInfo> {
    /**
     * 查询评分办法信息列表
     *
     * @param scoringMethodInfo 评分办法信息
     * @return 评分办法信息集合
     */
    public List<ScoringMethodInfo> selectList(ScoringMethodInfo scoringMethodInfo);

    ScoringMethodInfo infoByParams(ScoringMethodInfo scoringMethodInfo);

    ScoringMethodInfo selectByProject(Long projectId);

}