package com.ruoyi.scoring.service;

import java.util.List;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scoring.domain.ScoringMethodUitem;

/**
 * 评分办法详细信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IScoringMethodItemService extends IService<ScoringMethodItem> {
    /**
     * 查询评分办法详细信息列表
     *
     * @param scoringMethodItem 评分办法详细信息
     * @return 评分办法详细信息集合
     */
    public List<ScoringMethodItem> selectList(ScoringMethodItem scoringMethodItem);

    List<ScoringMethodItem> listByInfoId(Long scoringMethodId);
}