package com.ruoyi.scoring.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 用户评分办法信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IScoringMethodUinfoService extends IService<ScoringMethodUinfo> {
    /**
     * 查询用户评分办法信息列表
     *
     * @param scoringMethodUinfo 用户评分办法信息
     * @return 用户评分办法信息集合
     */
    public List<ScoringMethodUinfo> selectList(ScoringMethodUinfo scoringMethodUinfo);


    public Map<String,Object> getInfoByProjectId(Long projectId,Long resultId);

    public ScoringMethodUinfo selectByProject(Long projectId);

    ScoringMethodUinfo saveInfo(ScoringMethodUinfo scoringMethodUinfo);

    ScoringMethodUinfo infoByParams(ScoringMethodUinfo queryUinfo);
}
