package com.ruoyi.scoring.service;

import java.util.List;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户评分办法因素Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IScoringMethodUitemService extends IService<ScoringMethodUitem> {
    /**
     * 查询用户评分办法因素列表
     *
     * @param scoringMethodUitem 用户评分办法因素
     * @return 用户评分办法因素集合
     */
    public List<ScoringMethodUitem> selectList(ScoringMethodUitem scoringMethodUitem);

    List<ScoringMethodUitem> listByParams(ScoringMethodUitem uitemQuery);

    void initNewProjectUitem(Long projectId);
}