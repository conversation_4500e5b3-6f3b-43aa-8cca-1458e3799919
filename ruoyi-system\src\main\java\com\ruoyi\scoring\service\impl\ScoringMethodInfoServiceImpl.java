package com.ruoyi.scoring.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.OptionalDouble;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.domain.ProcurementDocumentsInfo;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.scoring.mapper.ScoringMethodInfoMapper;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.service.IScoringMethodInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 评分办法信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ScoringMethodInfoServiceImpl extends ServiceImpl<ScoringMethodInfoMapper, ScoringMethodInfo> implements IScoringMethodInfoService {

    @Autowired
    IScoringMethodUinfoService iScoringMethodUinfoService;
    @Autowired
    IScoringMethodItemService iScoringMethodItemService;
    @Autowired
    IScoringMethodUitemService iScoringMethodUitemService;
    @Autowired
    IBusiTenderProjectService busiTenderProjectService;

    /**
     * 查询评分办法信息列表
     *
     * @param scoringMethodInfo 评分办法信息
     * @return 评分办法信息
     */
    @Override
    public List<ScoringMethodInfo> selectList(ScoringMethodInfo scoringMethodInfo) {
        QueryWrapper<ScoringMethodInfo> scoringMethodInfoQueryWrapper = getScoringMethodInfoQueryWrapper(scoringMethodInfo);
        List<ScoringMethodInfo> list = list(scoringMethodInfoQueryWrapper);
        String returnUinfo = (String) scoringMethodInfo.getParams().getOrDefault("returnUinfo","false");
        if(StringUtils.isNotBlank(returnUinfo) && returnUinfo.equals("true")){
            ScoringMethodUinfo queryUinfo = new ScoringMethodUinfo();
            String projectId = (String)scoringMethodInfo.getParams().get("projectId");
            queryUinfo.setProjectId(Long.parseLong(projectId));
            queryUinfo.setEntId(SecurityUtils.getLoginUser().getEntId());
            list.forEach(item->{
                queryUinfo.setScoringMethodId(item.getScoringMethodId());
                item.setUInfo(iScoringMethodUinfoService.infoByParams(queryUinfo));
            });
            for (ScoringMethodInfo methodInfo : list) {
                List<ScoringMethodItem> scoringMethodItems = iScoringMethodItemService.list(
                        new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", methodInfo.getScoringMethodId())
                );

                //取出List<ScoringMethodItem> scoringMethodItems中的itemCode是jsbps、swbps、tbbjdf的ScoringMethodItem的scoringMethodItemId
                // 定义要检查的itemCode列表
                List<String> targetItemCodes =new ArrayList<>();
                targetItemCodes.add("jsbps");
                targetItemCodes.add("swbps");
                targetItemCodes.add("tbbjdf");

                ScoringMethodUinfo scoringMethodId = iScoringMethodUinfoService.getOne(
                        new QueryWrapper<ScoringMethodUinfo>()
                                .eq("scoring_method_id", methodInfo.getScoringMethodId())
                                .eq("project_id",scoringMethodInfo.getParams().get("projectId"))
                );
                if (null!=scoringMethodId){
                    // 使用Stream API过滤并收集符合条件的scoringMethodItemId
                    List<Long> targetScoringMethodItemIds = scoringMethodItems.stream()
                            .filter(item -> targetItemCodes.contains(item.getItemCode()))
                            .map(ScoringMethodItem::getScoringMethodItemId)
                            .collect(Collectors.toList());
                    List<ScoringMethodUitem> scoringMethodUitems = iScoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                            .in("scoring_method_item_id", targetScoringMethodItemIds)
                            .eq("scoring_method_id", methodInfo.getScoringMethodId())
                            .eq("ent_method_id",scoringMethodId.getEntMethodId())
                    );
                    int sum = scoringMethodUitems.stream()
                            .mapToInt(ScoringMethodUitem::getScore)
                            .sum();
                    methodInfo.setScore(sum);
                }else {
                    methodInfo.setScore(0);
                }
            }
        }




        return list;
    }

    private QueryWrapper<ScoringMethodInfo> getScoringMethodInfoQueryWrapper(ScoringMethodInfo scoringMethodInfo) {
        QueryWrapper<ScoringMethodInfo> scoringMethodInfoQueryWrapper = new QueryWrapper<>();
        scoringMethodInfoQueryWrapper.like(ObjectUtil.isNotEmpty(scoringMethodInfo.getMethodName()), "method_name", scoringMethodInfo.getMethodName());
        scoringMethodInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodInfo.getMethodCode()), "method_code", scoringMethodInfo.getMethodCode());
        scoringMethodInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodInfo.getMethodFile()), "method_file", scoringMethodInfo.getMethodFile());
        scoringMethodInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodInfo.getTenderMode()), "tender_mode", scoringMethodInfo.getTenderMode());
        scoringMethodInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodInfo.getProjectType()), "project_type", scoringMethodInfo.getProjectType());
        scoringMethodInfoQueryWrapper.apply(
                ObjectUtil.isNotEmpty(scoringMethodInfo.getParams().get("dataScope")),
                scoringMethodInfo.getParams().get("dataScope") + ""
        );
        return scoringMethodInfoQueryWrapper;
    }
    @Transactional
    @Override
    public ScoringMethodInfo infoByParams(ScoringMethodInfo scoringMethodInfo) {
        List<ScoringMethodInfo> list = list(getScoringMethodInfoQueryWrapper(scoringMethodInfo));
        if (list.size() > 0) {
            ScoringMethodInfo data = list.get(0);
            String returnUinfo = (String) scoringMethodInfo.getParams().getOrDefault("returnUinfo","false");
            if(StringUtils.isNotBlank(returnUinfo) && returnUinfo.equals("true")){
                ScoringMethodUinfo queryUinfo = new ScoringMethodUinfo();
                Long entId = SecurityUtils.getLoginUser().getEntId();
                queryUinfo.setEntId(entId);
                queryUinfo.setScoringMethodId(data.getScoringMethodId());
                ScoringMethodUinfo uInfo = iScoringMethodUinfoService.infoByParams(queryUinfo);
                if(ObjectUtil.isNotEmpty(uInfo)){
                    data.setUInfo(uInfo);
                }
            }
            data.setItems(iScoringMethodItemService.listByInfoId(data.getScoringMethodId()));
            return data;
        }
        return null;
    }

    @Override
    public ScoringMethodInfo selectByProject(Long projectId) {
        BusiTenderProject project = busiTenderProjectService.getById(projectId);
        ScoringMethodInfo scoringMethodInfo = new ScoringMethodInfo();
        scoringMethodInfo.setTenderMode(Integer.valueOf(project.getTenderMode()));
        scoringMethodInfo.setProjectType(Integer.valueOf(project.getProjectType()));
        List<ScoringMethodInfo> infoList = selectList (scoringMethodInfo);
        if (infoList == null || infoList.isEmpty()) {
            return null;
        }
        if (infoList.size()>1) {
            throw new RuntimeException("有多个info");
        }
        return infoList.get(0);
    }
}
