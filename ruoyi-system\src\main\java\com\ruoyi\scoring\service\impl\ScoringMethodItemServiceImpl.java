package com.ruoyi.scoring.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.mapper.ScoringMethodUinfoMapper;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.scoring.mapper.ScoringMethodItemMapper;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.service.IScoringMethodItemService;

/**
 * 评分办法详细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ScoringMethodItemServiceImpl extends ServiceImpl<ScoringMethodItemMapper, ScoringMethodItem> implements IScoringMethodItemService {

    @Autowired
    IScoringMethodUitemService iScoringMethodUitemService;
    @Autowired
    ScoringMethodUinfoMapper scoringMethodUinfoMapper;

    /**
     * 查询评分办法详细信息列表
     *
     * @param scoringMethodItem 评分办法详细信息
     * @return 评分办法详细信息
     */
    @Override
    public List<ScoringMethodItem> selectList(ScoringMethodItem scoringMethodItem) {
        QueryWrapper<ScoringMethodItem> scoringMethodItemQueryWrapper = getScoringMethodItemQueryWrapper(scoringMethodItem);
        List<ScoringMethodItem> list = list(scoringMethodItemQueryWrapper);
        String returnUitem = (String) scoringMethodItem.getParams().getOrDefault("returnUitem","false");
        if(StringUtils.isNotBlank(returnUitem) && returnUitem.equals("true")){
            ScoringMethodUitem uitemQuery = new ScoringMethodUitem();
            uitemQuery.setEntId(SecurityUtils.getLoginUser().getEntId());
            //查询uinfo
            String projectId = (String)scoringMethodItem.getParams().get("projectId");
            ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoMapper.selectOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));
            if(null!=scoringMethodUinfo){
                uitemQuery.setEntMethodId(scoringMethodUinfo.getEntMethodId());
                list.forEach(item->{
                    uitemQuery.setScoringMethodId(item.getScoringMethodId());
                    uitemQuery.setScoringMethodItemId(item.getScoringMethodItemId());
                    //
                    List<ScoringMethodUitem> scoringMethodUitems = iScoringMethodUitemService.listByParams(uitemQuery);
                    if (scoringMethodUitems.size()>0){
                        int sum = scoringMethodUitems.stream()
                                .mapToInt(ScoringMethodUitem::getScore)
                                .sum();
                        item.setScore(sum);
                        item.setUItems(scoringMethodUitems);
                    }else {
                        item.setScore(0);
                        item.setUItems(scoringMethodUitems);
                    }
                });
            }
        }

        return list;
    }

    private QueryWrapper<ScoringMethodItem> getScoringMethodItemQueryWrapper(ScoringMethodItem scoringMethodItem) {
        QueryWrapper<ScoringMethodItem> scoringMethodItemQueryWrapper = new QueryWrapper<>();
        scoringMethodItemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodItem.getScoringMethodId()), "scoring_method_id", scoringMethodItem.getScoringMethodId());
        scoringMethodItemQueryWrapper.like(ObjectUtil.isNotEmpty(scoringMethodItem.getItemName()), "item_name", scoringMethodItem.getItemName());
        scoringMethodItemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodItem.getItemCode()), "item_code", scoringMethodItem.getItemCode());
        scoringMethodItemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodItem.getItemContent()), "item_content", scoringMethodItem.getItemContent());
        scoringMethodItemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodItem.getItemType()), "item_type", scoringMethodItem.getItemType());
        scoringMethodItemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodItem.getItemMode()), "item_mode", scoringMethodItem.getItemMode());
        scoringMethodItemQueryWrapper.apply(
                ObjectUtil.isNotEmpty(scoringMethodItem.getParams().get("dataScope")),
                scoringMethodItem.getParams().get("dataScope") + ""
        );
        scoringMethodItemQueryWrapper.orderByAsc("item_sort");
        return scoringMethodItemQueryWrapper;
    }

    @Override
    public List<ScoringMethodItem> listByInfoId(Long scoringMethodId) {
        return list(new QueryWrapper<ScoringMethodItem>().eq("scoring_method_id", scoringMethodId));
    }
}
