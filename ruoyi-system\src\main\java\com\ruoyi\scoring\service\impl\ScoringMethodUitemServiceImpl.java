package com.ruoyi.scoring.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.mapper.ScoringMethodInfoMapper;
import com.ruoyi.scoring.mapper.ScoringMethodUitemMapper;
import com.ruoyi.scoring.service.IScoringMethodInfoService;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 用户评分办法因素Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ScoringMethodUitemServiceImpl extends ServiceImpl<ScoringMethodUitemMapper, ScoringMethodUitem> implements IScoringMethodUitemService {

    @Autowired
    ScoringMethodInfoMapper iScoringMethodInfoMapper;
    @Autowired
    IScoringMethodInfoService scoringMethodInfoService;
    @Autowired
    IScoringMethodUitemService iScoringMethodUitemService;
    @Autowired
    IScoringMethodItemService iScoringMethodItemService;
    @Autowired
    IProcurementDocumentsUinfoService procurementDocumentsUinfoService;
    @Autowired
    IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    IBusiTenderProjectService busiTenderProjectService;



    /**
     * 查询用户评分办法因素列表
     *
     * @param scoringMethodUitem 用户评分办法因素
     * @return 用户评分办法因素
     */
    @Override
    public List<ScoringMethodUitem> selectList(ScoringMethodUitem scoringMethodUitem) {
        QueryWrapper<ScoringMethodUitem> scoringMethodUitemQueryWrapper = getScoringMethodUitemQueryWrapper(scoringMethodUitem);
        return list(scoringMethodUitemQueryWrapper);
    }

    private QueryWrapper<ScoringMethodUitem> getScoringMethodUitemQueryWrapper(ScoringMethodUitem scoringMethodUitem) {
        QueryWrapper<ScoringMethodUitem> scoringMethodUitemQueryWrapper = new QueryWrapper<>();
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getScoringMethodItemId()), "scoring_method_item_id", scoringMethodUitem.getScoringMethodItemId());
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getEntMethodId()), "ent_method_id", scoringMethodUitem.getEntMethodId());
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getEntId()), "ent_id", scoringMethodUitem.getEntId());
        scoringMethodUitemQueryWrapper.like(ObjectUtil.isNotEmpty(scoringMethodUitem.getItemName()), "item_name", scoringMethodUitem.getItemName());
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getItemRemark()), "item_remark", scoringMethodUitem.getItemRemark());
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getMinScore()), "min_score", scoringMethodUitem.getMinScore());
        scoringMethodUitemQueryWrapper.eq(ObjectUtil.isNotEmpty(scoringMethodUitem.getMaxScore()), "max_score", scoringMethodUitem.getMaxScore());
        scoringMethodUitemQueryWrapper.apply(
                ObjectUtil.isNotEmpty(scoringMethodUitem.getParams().get("dataScope")),
                scoringMethodUitem.getParams().get("dataScope") + ""
        );
        return scoringMethodUitemQueryWrapper;
    }

    @Override
    public List<ScoringMethodUitem> listByParams(ScoringMethodUitem uitemQuery) {
        return list(getScoringMethodUitemQueryWrapper(uitemQuery));
    }

    /**
     * 初始化新项目的用户评分办法因素
     */
    @Override
    public void initNewProjectUitem (Long projectId) {
        BusiTenderProject byId = busiTenderProjectService.getById(projectId);
        // 竞争性判断0，竞争性磋商1 询价3 单一来源4
        //询价只有货物，，
        if (byId.getTenderMode().equals("0")||byId.getTenderMode().equals("1")||byId.getTenderMode().equals("4")){
            ScoringMethodInfo info = scoringMethodInfoService.selectByProject(projectId);
            if (info==null) {
                throw new ServiceException("获取项目评分办法异常", HttpStatus.ERROR);
            }
            // 判断该项目是否为磋商采购方式的工程类项目，是则为其创建评分详细信息模板
            //  if (info.getTenderMode()==1 && info.getProjectType()==0) {
            ScoringMethodUinfo uinfo = scoringMethodUinfoService.selectByProject(projectId);
            if(uinfo==null) {
                uinfo = new ScoringMethodUinfo();
                ProcurementDocumentsUinfo procurementDocumentsUinfo = procurementDocumentsUinfoService.selectByProject(projectId);
                uinfo.setEntId(procurementDocumentsUinfo.getEntId());
//                uinfo.setEntMethodId(info.getScoringMethodId());
                uinfo.setScoringMethodId(info.getScoringMethodId());
                uinfo.setProjectId(projectId);
                scoringMethodUinfoService.save(uinfo);
                // 从scoring_method_item获取评分详细信息模板，插入scoring_method_uitem中
                QueryWrapper<ScoringMethodItem> itemQueryWrapper = new QueryWrapper<>();
                itemQueryWrapper.eq(ObjectUtil.isNotEmpty(info.getScoringMethodId()), "scoring_method_id", info.getScoringMethodId());
                List<ScoringMethodItem> itemList = iScoringMethodItemService.list(itemQueryWrapper);
                if (!itemList.isEmpty()) {
                    ScoringMethodUinfo finalUinfo = uinfo;
                    itemList.forEach(ele -> {
                        if(ele.getItemCode().equals("tbbjdf")){
                            ScoringMethodUitem uitem = new ScoringMethodUitem();
                            uitem.setEntMethodId(finalUinfo.getEntMethodId());
                            uitem.setEntId(finalUinfo.getEntId());
                            uitem.setScoringMethodId(info.getScoringMethodId());
                            uitem.setScoringMethodItemId(ele.getScoringMethodItemId());
                            uitem.setItemName(ele.getItemName());
                            uitem.setItemRemark(ele.getItemContent());
                            uitem.setCreateTime(new Date());
                            uitem.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                            uitem.setUpdateTime(new Date());
                            uitem.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                            uitem.setScore(30);
                            uitem.setMaxScore(30);
                            uitem.setDelFlag(0);
                            uitem.setUitemSort(1);
                            iScoringMethodUitemService.save(uitem);
                        }else{
                            JSONArray ja = JSONArray.parse(ele.getItemContent());
                            for (int i=0;i<ja.size();i++) {
                                JSONObject jo = ja.getJSONObject(i);
                                ScoringMethodUitem uitem = new ScoringMethodUitem();
                                uitem.setEntMethodId(finalUinfo.getEntMethodId());
                                uitem.setEntId(finalUinfo.getEntId());
                                uitem.setScoringMethodId(info.getScoringMethodId());
                                uitem.setScoringMethodItemId(ele.getScoringMethodItemId());
                                uitem.setItemName(jo.getString("name"));
                                uitem.setItemRemark(jo.getString("content"));
                                uitem.setCreateTime(new Date());
                                uitem.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                                uitem.setUpdateTime(new Date());
                                uitem.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                                uitem.setDelFlag(0);
                                if(ele.getItemCode().equals("jsbps") || ele.getItemCode().equals("swbps")){
                                    uitem.setScore(jo.getInteger("score"));
                                    uitem.setScoreLevel(jo.getString("scoreLevel"));
                                    uitem.setUitemSort(jo.getInteger("uitemSort"));
                                }
                                iScoringMethodUitemService.save(uitem);
                            }
                        }

                    });
                }
            }
            //}
        }else  if (byId.getTenderMode().equals("3")){

        }
    }

    public static void main(String[] args) {
        JSONArray ja1 = new JSONArray();
        JSONObject jo11 = new JSONObject();
        jo11.put("name", "符合《中华人民共和国政府采购法》第二十二条规定");
        jo11.put("content", "符合第二章“响应人须知前附表”的规定");
        ja1.add(jo11);
        JSONObject jo12 = new JSONObject();
        jo12.put("name", "特定资格要求");
        jo12.put("content", "符合第二章“响应人须知前附表”的规定");
        ja1.add(jo12);
        JSONObject jo13 = new JSONObject();
        jo13.put("name", "不是联合体");
        jo13.put("content", "符合第二章“响应人须知前附表”的规定");
        ja1.add(jo13);
        System.out.println(ja1.toJSONString());
        JSONArray ja2 = new JSONArray();
        JSONObject jo21 = new JSONObject();
        jo21.put("name", "响应人名称");
        jo21.put("content", "与营业执照、资质证书、安全生产许可证一致，相关证件须在有效期内");
        ja2.add(jo21);
        JSONObject jo22 = new JSONObject();
        jo22.put("name", "签字盖章");
        jo22.put("content", "符合第二章“响应人须知前附表”的规定");
        ja2.add(jo22);
        JSONObject jo23 = new JSONObject();
        jo23.put("name", "已标价工程量清单");
        jo23.put("content", "符合第五章“采购需求-工程量清单”的规定");
        ja2.add(jo23);
        JSONObject jo24 = new JSONObject();
        jo24.put("name", "合同履行期限");
        jo24.put("content", "符合第二章“响应人须知前附表”的规定");
        ja2.add(jo24);
        JSONObject jo25 = new JSONObject();
        jo25.put("name", "质保期");
        jo25.put("content", "符合第二章“响应人须知前附表”的规定");
        ja2.add(jo25);
        JSONObject jo26 = new JSONObject();
        jo26.put("name", "投标报价");
        jo26.put("content", "投标报价不超过采购预算价");
        ja2.add(jo26);
        JSONObject jo27 = new JSONObject();
        jo27.put("name", "其他要求");
        jo27.put("content", "采购文件规定的其他响应无效情形");
        ja2.add(jo27);
        System.out.println(ja2.toJSONString());


        JSONArray ja3 = new JSONArray();
        JSONObject jo31 = new JSONObject();
        jo31.put("name", "投标报价打分");
        jo31.put("content", "满足磋商文件要求且最后报价最低的供应商的价格为评标基准价，其价格分为满分【30】分。其他有效供应商的报价分按照下列公式计算:投标报价得分=(评标基准价/最后投标报价)x【30】\n" +
                "价格扣除:1.根据《政府采购促进中小企业发展管理办法》（财库[2020]46号）、《关于进一步加大政府采购支持中小企业力度的通知》（财库〔2022〕19号）、" +
                "《关于政府采购支持监狱企业发展有关问题的通知》（财库〔2014〕68 号）、《财政部 民政部 中国残疾人联合会关于促进残疾人就业政府采购政策的通知》" +
                "（财库〔2017〕141号）的要求：对小型、微型企业及监狱企业、残疾人福利性单位服务的价格给予20%的扣除，用扣除后的价格参与价格评审，" +
                "参与评审价格=有效投标报价×（1-20%）。供应商应当出具所提供服务企业的《中小企业声明函》，否则不予价格扣除计；\n" +
                "2.未尽事宜详见第二章“投标人须知前附表”第1.12项规定。\n3.如为专门面向中小企业采购的项目应删除上述内容。");
        jo31.put("score", "30");
        ja3.add(jo31);
        System.out.println(ja3.toJSONString());

        JSONArray ja4 = new JSONArray();
        JSONObject jo41 = new JSONObject();
        jo41.put("name", "施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划");
        jo41.put("content", "①内容完整全面可行且优于采购文件要求的，得11分；\n" +
                "②内容完整、规范的，得7分；\n" +
                "③内容缺项或未提供的，不得分。");
        jo41.put("score", "11");
        jo41.put("scoreLevel", "0,7,11");
        jo41.put("uitemSort", "1");
        ja4.add(jo41);
        JSONObject jo42 = new JSONObject();
        jo42.put("name", "工程进度计划与措施、施工进度或施工网络图、施工总平面布置图");
        jo42.put("content", "①内容完整全面可行且优于采购文件要求的，得11分；\n" +
                "②内容完整、规范的，得7分；\n" +
                "③内容缺项或未提供的，不得分。");
        jo42.put("score", "11");
        jo42.put("scoreLevel", "0,7,11");
        jo42.put("uitemSort", "2");
        ja4.add(jo42);
        JSONObject jo43 = new JSONObject();
        jo43.put("name", "节能减排（绿色施工、工艺创新）在本工程的具体应用措施");
        jo43.put("content", "①内容完整全面可行且优于采购文件要求的，得11分；\n" +
                "②内容完整、规范的，得7分；\n" +
                "③内容缺项或未提供的，不得分。");
        jo43.put("score", "11");
        jo43.put("scoreLevel", "0,7,11");
        jo43.put("uitemSort", "3");
        ja4.add(jo43);
        JSONObject jo44 = new JSONObject();
        jo44.put("name", "新工艺（新技术、新设备、新材料）的采用程度");
        jo44.put("content", "①内容完整全面可行且优于采购文件要求的，得11分；\n" +
                "②内容完整、规范的，得7分；\n" +
                "③内容缺项或未提供的，不得分。");
        jo44.put("score", "11");
        jo44.put("scoreLevel", "0,7,11");
        jo44.put("uitemSort", "4");
        ja4.add(jo44);
        JSONObject jo45 = new JSONObject();
        jo45.put("name", "风险管理措施");
        jo45.put("content", "①内容完整全面可行且优于采购文件要求的，得11分；\n" +
                "②内容完整、规范的，得7分；\n" +
                "③内容缺项或未提供的，不得分。");
        jo45.put("score", "11");
        jo45.put("scoreLevel", "0,7,11");
        jo45.put("uitemSort", "5");
        ja4.add(jo45);
        System.out.println(ja4.toJSONString());

        JSONArray ja5 = new JSONArray();
        JSONObject jo51 = new JSONObject();
        jo51.put("name", "体系认证");
        jo51.put("content", "供应商同时具有有效期内的质量管理体系认证、环境管理体系认证及职业健康安全管理体系认证的，得5分；缺项或未提供的，不得分。\n" +
                "注：须附体系认证书原件扫描件，否则，不得分。");
        jo51.put("score", "5");
        jo51.put("scoreLevel", "0,5");
        jo51.put("uitemSort", "1");
        ja5.add(jo51);
        JSONObject jo52 = new JSONObject();
        jo52.put("name", "项目技术负责人");
        jo52.put("content", "项目技术负责人具有工程师中级职称及以上且同时具有贰级或以上注册建造师证书的，得4分；缺项或未提供的，不得分。\n" +
                "注：须附职称证书及注册建造师证书原件扫描件及2024年1月1日以来在本单位任意一个月缴纳社保的证明，否则，不得分。");
        jo52.put("score", "4");
        jo52.put("scoreLevel", "0,4");
        jo52.put("uitemSort", "2");
        ja5.add(jo52);
        JSONObject jo53 = new JSONObject();
        jo53.put("name", "拟派项目管理人员");
        jo53.put("content", "人员配备：施工员、安全员、质量员或质检员、材料员、资料员、预算员同时配备齐全的，得6分；缺项或未提供的，不得分。\n" +
                "注：须附2024年1月1日以来在本单位任意一个月缴纳社保的证明，否则，不得分");
        jo53.put("score", "4");
        jo53.put("scoreLevel", "0,4");
        jo53.put("uitemSort", "3");
        ja5.add(jo53);
        JSONObject jo54 = new JSONObject();
        jo54.put("name", "以往业绩");
        jo54.put("content", "提供以往业绩截图或扫描件，1项加1分，最多2分");
        jo54.put("score", "2");
        jo54.put("scoreLevel", "0,1,2");
        jo54.put("uitemSort", "4");
        ja5.add(jo54);
        System.out.println(ja5.toJSONString());
    }
}
