package com.ruoyi.scoring.util;

import org.apache.poi.ss.usermodel.*;

public class ExcelStyleHelper {
    private Workbook workbook;
    public ExcelStyleHelper(Workbook workbook) {
        this.workbook = workbook;
    }
    private Font createFont(boolean bold, short fontSize) {
        Font font = workbook.createFont();
        font.setBold(bold);
        font.setFontHeightInPoints(fontSize);
        return font;
    }
    private CellStyle createCellStyle(Font font, HorizontalAlignment horizontalAlignment, VerticalAlignment verticalAlignment) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(horizontalAlignment);
        cellStyle.setVerticalAlignment(verticalAlignment);
        cellStyle.setWrapText(true); // 开启自动换行
        setBorderStyle(cellStyle);
        return cellStyle;
    }
    private void setBorderStyle(CellStyle cellStyle) {
        // 设置边框样式，这里只是一个示例，具体边框样式根据需要设置
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
    }
    public CellStyle createHeaderCellStyle() {
        Font headerFont = createFont(true, (short) 18);
        return createCellStyle(headerFont, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

    public CellStyle createTitleCellStyle() {
        Font titleFont = createFont(false, (short) 10);
        return createCellStyle(titleFont, HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
    }

    public CellStyle createTitleRightCellStyle() {
        Font titleFont = createFont(false, (short) 10);
        return createCellStyle(titleFont, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER);
    }

    public CellStyle createTitleRowCellStyle() {
        Font titleRowFont = createFont(true, (short) 14);
        return createCellStyle(titleRowFont, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

    public CellStyle createContentCellStyle() {
        Font titleFont = createFont(false, (short) 10);
        return createCellStyle(titleFont, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

/*

    // 第一行单元格样式
    Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 18);
    // 创建单元格样式
    CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    setBorderStyle(headerCellStyle, workbook); // 设置边框样式


    Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 10);
    CellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setFont(titleFont);
        titleCellStyle.setAlignment(HorizontalAlignment.LEFT);
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    setBorderStyle(titleCellStyle, workbook); // 设置边框样式
    CellStyle titleRightCellStyle = workbook.createCellStyle();
        titleRightCellStyle.setFont(titleFont);
        titleRightCellStyle.setAlignment(HorizontalAlignment.RIGHT);
        titleRightCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    setBorderStyle(titleRightCellStyle, workbook); // 设置边框样式

    Font titleRowFont = workbook.createFont();
        titleRowFont.setBold(true);
        titleRowFont.setFontHeightInPoints((short) 14);
    CellStyle titleRowCellStyle = workbook.createCellStyle();
        titleRowCellStyle.setFont(titleRowFont);
        titleRowCellStyle.setAlignment(HorizontalAlignment.CENTER);
        titleRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    setBorderStyle(titleRowCellStyle, workbook); // 设置边框样式

    CellStyle contentCellStyle = workbook.createCellStyle();
        contentCellStyle.setFont(titleFont);
        contentCellStyle.setAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    setBorderStyle(contentCellStyle, workbook); // 设置边框样式
*/

}
