package com.ruoyi.third.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.third.domain.ThirdServerUserBind;
import com.ruoyi.third.service.IThirdServerUserBindService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 第三方id映射关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Api(tags = "第三方id映射关系管理")
@RestController
@RequestMapping("/user/bind")
public class ThirdServerUserBindController extends BaseController {
    @Autowired
    private IThirdServerUserBindService thirdServerUserBindService;

/**
 * 查询第三方id映射关系列表
 */
@PreAuthorize("@ss.hasPermi('user:bind:list')")
@ApiOperation(value = "查询第三方id映射关系列表")
@GetMapping("/list")
    public TableDataInfo list(ThirdServerUserBind thirdServerUserBind) {
        startPage();
        List<ThirdServerUserBind> list = thirdServerUserBindService.selectList(thirdServerUserBind);
        return getDataTable(list);
    }

    /**
     * 导出第三方id映射关系列表
     */
    @PreAuthorize("@ss.hasPermi('user:bind:export')")
    @Log(title = "第三方id映射关系", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出第三方id映射关系列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThirdServerUserBind thirdServerUserBind) {
        List<ThirdServerUserBind> list = thirdServerUserBindService.selectList(thirdServerUserBind);
        ExcelUtil<ThirdServerUserBind> util = new ExcelUtil<ThirdServerUserBind>(ThirdServerUserBind. class);
        util.exportExcel(response, list, "第三方id映射关系数据");
    }

    /**
     * 获取第三方id映射关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:bind:query')")
    @ApiOperation(value = "获取第三方id映射关系详细信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataType = "Long")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")Long id) {
        return success(thirdServerUserBindService.getById(id));
    }

    /**
     * 新增第三方id映射关系
     */
    @PreAuthorize("@ss.hasPermi('user:bind:add')")
    @Log(title = "第三方id映射关系", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增第三方id映射关系")
    @PostMapping
    public AjaxResult add(@RequestBody ThirdServerUserBind thirdServerUserBind) {
        return toAjax(thirdServerUserBindService.save(thirdServerUserBind));
    }

    /**
     * 修改第三方id映射关系
     */
    @PreAuthorize("@ss.hasPermi('user:bind:edit')")
    @Log(title = "第三方id映射关系", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改第三方id映射关系")
    @PutMapping
    public AjaxResult edit(@RequestBody ThirdServerUserBind thirdServerUserBind) {
        return toAjax(thirdServerUserBindService.updateById(thirdServerUserBind));
    }

    /**
     * 删除第三方id映射关系
     */
    @PreAuthorize("@ss.hasPermi('user:bind:remove')")
    @Log(title = "第三方id映射关系", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除第三方id映射关系")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(thirdServerUserBindService.removeByIds(Arrays.asList(ids)));
    }
}
