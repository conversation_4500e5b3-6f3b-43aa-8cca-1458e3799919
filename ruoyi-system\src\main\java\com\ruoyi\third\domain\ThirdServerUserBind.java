package com.ruoyi.third.domain;

import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 第三方id映射关系对象 third_server_user_bind
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@ApiModel("第三方id映射关系对象")
@TableName(resultMap = "com.ruoyi.third.mapper.ThirdServerUserBindMapper.ThirdServerUserBindResult")
public class ThirdServerUserBind extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
            @Excel(name = "服务名称")
    private String serverName;
    /**
     * 服务的userId
     */
    @ApiModelProperty("服务的userId")
            @Excel(name = "服务的userId")
    private Long serverUserId;
    /**
     * 本系统的userId
     */
    @ApiModelProperty("本系统的userId")
            @Excel(name = "本系统的userId")
    private Long localUserId;

        }
