package com.ruoyi.third.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.third.mapper.ThirdServerUserBindMapper;
import com.ruoyi.third.domain.ThirdServerUserBind;
import com.ruoyi.third.service.IThirdServerUserBindService;

/**
 * 第三方id映射关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class ThirdServerUserBindServiceImpl extends ServiceImpl<ThirdServerUserBindMapper, ThirdServerUserBind> implements IThirdServerUserBindService {
    /**
     * 查询第三方id映射关系列表
     *
     * @param thirdServerUserBind 第三方id映射关系
     * @return 第三方id映射关系
     */
    @Override
    public List<ThirdServerUserBind> selectList(ThirdServerUserBind thirdServerUserBind) {
        QueryWrapper<ThirdServerUserBind> thirdServerUserBindQueryWrapper = new QueryWrapper<>();
                        thirdServerUserBindQueryWrapper.like(ObjectUtil.isNotEmpty(thirdServerUserBind.getServerName()),"server_name",thirdServerUserBind.getServerName());
                        thirdServerUserBindQueryWrapper.eq(ObjectUtil.isNotEmpty(thirdServerUserBind.getServerUserId()),"server_user_id",thirdServerUserBind.getServerUserId());
                        thirdServerUserBindQueryWrapper.eq(ObjectUtil.isNotEmpty(thirdServerUserBind.getLocalUserId()),"local_user_id",thirdServerUserBind.getLocalUserId());
            thirdServerUserBindQueryWrapper.apply(
                ObjectUtil.isNotEmpty(thirdServerUserBind.getParams().get("dataScope")),
        thirdServerUserBind.getParams().get("dataScope")+""
        );
        return list(thirdServerUserBindQueryWrapper);
    }
}
