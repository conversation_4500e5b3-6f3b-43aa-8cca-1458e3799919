package com.ruoyi.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictTypeService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.Key;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Log4j2
public class AttachmentUtil {
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    @Autowired
    private ISysConfigService iSysConfigService;

    public Map<String, Object> getAttachmentMap(Long busiId, String dictDataValue){
        List<BusiAttachment> noticeAttachmentList = iBusiAttachmentService.getByBusiId(busiId);
        List<SysDictData> noticeDataList = sysDictTypeService.selectDictDataByType(dictDataValue);
        List<Map<String, Object>> ja = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        for (SysDictData data : noticeDataList) {
            String filePaths = "";
            for (BusiAttachment attachment : noticeAttachmentList) {
                if (attachment.getFileType().equals(data.getDictValue())) {
                    filePaths = filePaths + "," + attachment.getFilePath();
                }
            }
            if (filePaths.length() > 0) {
                filePaths = filePaths.substring(1);
            }
            map.put(data.getDictValue(), filePaths);
        }
        return map;
    }

    public Map<String, Object> getAttachmentNameMap(Long busiId, String dictDataValue){
        List<BusiAttachment> noticeAttachmentList = iBusiAttachmentService.getByBusiId(busiId);
        List<SysDictData> noticeDataList = sysDictTypeService.selectDictDataByType(dictDataValue);
        List<Map<String, Object>> ja = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        for (SysDictData data : noticeDataList) {
            String fileNames = "";
            for (BusiAttachment attachment : noticeAttachmentList) {
                if (attachment.getFileType().equals(data.getDictValue())) {
                    fileNames = fileNames + "," + attachment.getFileName();
                }
            }
            if (fileNames.length() > 0) {
                fileNames = fileNames.substring(1);
            }
            map.put(data.getDictValue(), fileNames);
        }
        return map;
    }

//    public Map<String, Object> getAttachmentNameMap(Long busiId, String dictDataValue){
//        List<BusiAttachment> noticeAttachmentList = iBusiAttachmentService.getByBusiId(busiId);
//        List<SysDictData> noticeDataList = sysDictTypeService.selectDictDataByType(dictDataValue);
//        List<Map<String, Object>> ja = new ArrayList<>();
//        Map<String, Object> map = new HashMap<>();
//        for (SysDictData data : noticeDataList) {
//            String filePaths = "";
//            for (BusiAttachment attachment : noticeAttachmentList) {
//                if (attachment.getFileType().equals(data.getDictValue())) {
//                    filePaths = filePaths + "," + attachment.getFilePath();
//                }
//            }
//            if (filePaths.length() > 0) {
//                filePaths = filePaths.substring(1);
//            }
//            map.put(data.getDictLabel(), filePaths);
//        }
//        return map;
//    }

    public JSONArray getAttachmentList(Long busiId, String dictDataValue){
        String fileUrl = iSysConfigService.selectConfigByKey("fileUrl");
        log.info("----------------getAttachmentList  start--------------------");
        log.info("fileUrl: "+fileUrl);
        List<BusiAttachment> noticeAttachmentList = iBusiAttachmentService.getByBusiId(busiId);
        List<SysDictData> noticeDataList = sysDictTypeService.selectDictDataByType(dictDataValue);
        JSONArray ja = new JSONArray();
        for (SysDictData data : noticeDataList) {
            JSONObject dictData = new JSONObject();
            dictData.put("code", data.getDictValue());
            dictData.put("name", data.getDictLabel());
            JSONArray files = new JSONArray();
            for (BusiAttachment attachment : noticeAttachmentList) {
                if (attachment.getFileType().equals(data.getDictValue())) {
                    JSONObject file = new JSONObject();
                    file.put("fileName", attachment.getFileName());
                    System.out.println(""+fileUrl + attachment.getFilePath());
                    file.put("filePath", fileUrl + attachment.getFilePath());
                    log.info("filePath: "+fileUrl + attachment.getFilePath());
                    files.add(file);
                }
            }
            if (files.size() > 0) {
                dictData.put("files", files);
                ja.add(dictData);
            }
        }
        log.info("----------------getAttachmentList end--------------------");
        return ja;
    }

    public void addNewAttachments(BaseEntity baseEntity, String noHandleTypes){
        QueryWrapper<BusiAttachment> query = new QueryWrapper<>();
        query.eq("busi_id",baseEntity.getId());
        if (StringUtils.isNoneBlank(noHandleTypes)) {
            query.notInSql("file_type", noHandleTypes);
        }
        iBusiAttachmentService.remove(query);
        List<BusiAttachment> batchSaveList = new ArrayList<>();
        if (baseEntity.getAttachmentMap()!=null){
            for (String type : baseEntity.getAttachmentMap().keySet()) {
                if (StringUtils.isNoneBlank(baseEntity.getAttachmentMap().get(type).toString()) && !noHandleTypes.contains("'"+type+"'")) {
                    String[] filePaths = baseEntity.getAttachmentMap().get(type).toString().split(",");
                    for (String filePath : filePaths) {
                        BusiAttachment attachment = new BusiAttachment();
                        attachment.setFileType(type);
                        attachment.setFilePath(filePath);
                        attachment.setFileName(filePath.substring(filePath.lastIndexOf("/")+1));
                        attachment.setFileSuffix(filePath.substring(filePath.lastIndexOf(".")+1));
                        attachment.setBusiId(baseEntity.getId());
                        batchSaveList.add(attachment);
                    }
                }
            }
            if(!batchSaveList.isEmpty()) {
                boolean b = iBusiAttachmentService.saveOrUpdateBatch(batchSaveList);
                if (!b) {
                    throw new RuntimeException("采购公告附件信息保存失败！");
                }
            }
        }
    }

    public void addNewAttachments(BaseEntity baseEntity){
        addNewAttachments(baseEntity, "");
    }

    public static String urlToReal(String url) {
        int si = url.lastIndexOf("/upload");
        String urlPath = "";
        if(si!=-1){
            urlPath = url.substring(si);
        }
        String filePath = RuoYiConfig.getProfile();
        String realPath = filePath + urlPath;
//        System.out.println(realPath);
        log.debug(realPath);
        return realPath;
    }

    public static String realToUrl(String url) {

        // 上传文件路径
        if(url.contains("\\")){
            url = url.replace("\\", "/");
        }
        String filePath = RuoYiConfig.getProfile();
        String currentDir = url.replace(filePath, "");
//        String currentDir = com.ruoyi.common.utils.StringUtils.substring(filePath, dirLastIndex);
        currentDir = Constants.RESOURCE_PREFIX + currentDir;
        return currentDir;
    }

    public static String getAttachmentSuffix(String filePath){
        if (StringUtils.isNoneBlank(filePath)) {
            int i = filePath.lastIndexOf(".");
            if (i != -1) {
                return filePath.substring(i+1);
            }
        }
        return null;
    }


    public static void main(String[] args) {
        String filePath = "/home/<USER>/xeyx/uploadPath/upload/procurement/1208131724354565/8f168abb-4b5d-4d85-b9c4-f5e05d7de186.pdf";
        System.out.println(filePath);
        System.out.println(AttachmentUtil.realToUrl(filePath));


        System.out.println(AttachmentUtil.realToUrl("/home/<USER>/xeyx/uploadPath/upload/procurement/1208131724354565/8f168abb-4b5d-4d85-b9c4-f5e05d7de186.pdf"));

        System.out.println(AttachmentUtil.urlToReal("/home/<USER>/xeyx/uploadPath/upload/procurement/1208131724354565/8f168abb-4b5d-4d85-b9c4-f5e05d7de186.pdf"));

        System.out.println(AttachmentUtil.getAttachmentSuffix(filePath));
    }
}
