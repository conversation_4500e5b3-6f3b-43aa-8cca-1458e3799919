package com.ruoyi.utils;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiTenderIntention;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.enums.SystemRoleEnum;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;

import java.util.List;

public class BaseUtil {

    public static void checkUser(QueryWrapper queryWrapper, BaseEntity baseEntity){
        if(!baseEntity.getParams().containsKey("isScope") || Boolean.parseBoolean(baseEntity.getParams().get("isScope").toString())){
            queryWrapper.apply(ObjectUtil.isNotEmpty(baseEntity.getParams().get("dataScope")),
                    String.valueOf(baseEntity.getParams().get("dataScope")
                    ));
            if(!(baseEntity instanceof BusiTenderProject) && !(baseEntity instanceof BusiTenderIntention)){
                LoginUser user = SecurityUtils.getLoginUser();
                if (user != null) {
                    List<SysRole> roleList = user.getUser().getRoles();
                    boolean isAdmin = false;
                    boolean isPurchaser = false;
                    boolean isSupplier = false;
                    for (SysRole role : roleList) {
                        if(SystemRoleEnum.XIAOHE_AI.getCode().equals(role.getRoleKey()) ||
                                SystemRoleEnum.ADMIN.getCode().equals(role.getRoleKey())){
                            isAdmin = true;
                            break;
                        }else if(SystemRoleEnum.PURCHASER.getCode().equals(role.getRoleKey()) ||
                                SystemRoleEnum.AGENCY.getCode().equals(role.getRoleKey())){
                            isPurchaser = true;
                            break;
                        }else if(SystemRoleEnum.SUPPLIER.getCode().equals(role.getRoleKey())){
                            isSupplier = true;
                            break;
                        }
                    }
                    if(!isAdmin) {
                        if (isPurchaser) {
                            queryWrapper.inSql("project_id",
                                    "SELECT project_id FROM busi_tender_project WHERE del_flag=0 AND (tenderer_id = " + user.getEntId() + "  OR agency_id = " + user.getEntId() + " )");
                        } else if (isSupplier) {
                            queryWrapper.inSql("project_id",
                                    "SELECT project_id FROM busi_tender_documents_download WHERE del_flag=0 AND bidder_id = " + user.getEntId() );
                        }
                    }
                }
            }
        }
        if(baseEntity.getParams().containsKey("orderByAsc")) {
            queryWrapper.orderByAsc(baseEntity.getParams().get("orderByAsc") + "");
        }else if(baseEntity.getParams().containsKey("orderByDesc")){
            queryWrapper.orderByDesc(baseEntity.getParams().get("orderByDesc") + "");
        }else{
            queryWrapper.orderByDesc("create_time");
        }
    }
}
