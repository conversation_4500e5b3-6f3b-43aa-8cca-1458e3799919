package com.ruoyi.utils;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.procurement.designs.template.DetailContent;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ExcelReader {

    /**
     * 从明细报价表中获取每项的单价
     */
    public static List<String> getUnitPriceFromMxbjb(String filePath) throws IOException {
        List<String> values = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(new File(filePath));
             Workbook workbook = new XSSFWorkbook(file)) {
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 获取最后一行的行号
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum >= 2) { // 至少要有三行数据
                // 从第二行开始遍历到倒数第二行
                for (int i = 1; i < lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        // 获取最后一列的列号
                        int lastCellNum = row.getLastCellNum();
                        if (lastCellNum > 0) {
                            // 获取最后一列的单元格
                            Cell lastCell = row.getCell(lastCellNum - 1);
                            if (lastCell != null) {
                                // 根据单元格类型获取值
                                String cellValue = getCellValueAsString(lastCell);
                                if (StringUtils.isNotEmpty(cellValue)) {
                                    values.add(cellValue);
                                }
                            }
                        }
                    }
                }
            }
        }
        return values;
    }

    /**
     * 从技术偏离表中获取偏离说明
     */
    public static List<String> getDeviationFromJsplb(String filePath) throws IOException {
        List<String> values = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(new File(filePath));
             Workbook workbook = new XSSFWorkbook(file)) {
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 获取最后一行的行号
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum >= 1) { // 至少要有两行数据
                // 从第二行开始遍历
                for (int i = 1; i <= lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        // 获取第五列的单元格（索引为 4）
                        Cell index = row.getCell(0);
                        Cell name = row.getCell(1);
                            Cell cell = row.getCell(4);
                            if (cell != null && index != null && name != null) {
                                if(StringUtils.isNoneBlank(getCellValueAsString(index)) && StringUtils.isNoneBlank(getCellValueAsString(name))) {
                                // 根据单元格类型获取值
                                values.add(getCellValueAsString(cell));
                            }
                        }
                    }
                }
            }
        }
        return values;
    }

    private static String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    public static void main(String[] args) {

        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\上传\\技术偏离表.xlsx";

        try {
            List<String> strList = ExcelReader.getDeviationFromJsplb(excelFilePath);
            System.out.println(strList);
        } catch (Exception e) {
            System.err.println("导出过程中出现未知错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
