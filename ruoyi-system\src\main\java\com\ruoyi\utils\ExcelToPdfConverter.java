package com.ruoyi.utils;

import com.aspose.words.FontSettings;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.http.HttpHelper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;

@Component
public class ExcelToPdfConverter {

    @Value("${spring.profiles.active}")
    private String activeValue;

    private static String active;

    @PostConstruct  // 在 Bean 初始化后执行
    public void init() {
        active = this.activeValue;
    }


    private static final Logger logger = LoggerFactory.getLogger(ExcelToPdfConverter.class);

    public static void convertExcelToPdf(String excelFilePath, String pdfFilePath) throws IOException {
        // 读取 Excel 文件
        FileInputStream excelFile = new FileInputStream(new File(excelFilePath));
        XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
        Sheet sheet = workbook.getSheetAt(0);

        // 创建 PDF 文档
        PdfWriter writer = new PdfWriter(new FileOutputStream(pdfFilePath));
        PdfDocument pdf = new PdfDocument(writer);
        Document document = new Document(pdf, PageSize.A4);

        String fontAbsolutePath = "";
        // 加载中文字体
        if (active.equals("release")){
            fontAbsolutePath = RuoYiConfig.getProfile() + "/fonts/simfang.ttf";
        } else {
            fontAbsolutePath = "C:/Windows/Fonts/simfang.ttf";
        }
        File fontFile = new File(fontAbsolutePath);
        if (!fontFile.exists()) {
            logger.error("字体不存在");
            throw new ServiceException("字体不存在");
        }
        PdfFont font = PdfFontFactory.createFont(fontAbsolutePath, "Identity-H", true);
        // 获取 Excel 表格的列数
        Row firstRow = sheet.getRow(0);
        int columnCount = firstRow.getLastCellNum();
        Table table = new Table(columnCount);
        logger.debug("表格列数: " + columnCount);

        // 遍历 Excel 表格的每一行
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                // 处理空行，添加空单元格
                for (int i = 0; i < columnCount; i++) {
                    Cell pdfCell = createStyledCell(null, "", font, workbook);
                    table.addCell(pdfCell);
                }
                continue;
            }
            for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                org.apache.poi.ss.usermodel.Cell excelCell = row.getCell(colIndex);
                if (excelCell == null) {
                    // 处理空单元格
                    excelCell = getMergedCell(sheet, rowIndex, colIndex);
                    if (excelCell == null) {
                        excelCell = row.createCell(colIndex);
                    }
                }
                String cellValue = getCellValue(excelCell);
                Cell pdfCell = createStyledCell(excelCell, cellValue, font, workbook);
                if (pdfCell != null) {
                    table.addCell(pdfCell);
                } else {
                    logger.debug("Cell 对象创建失败，单元格值: " + cellValue);
                }
            }
        }

        if (table.getNumberOfRows() == 0) {
            logger.debug("表格为空，可能是创建单元格时出现问题");
            return;
        }
        // 将表格添加到 PDF 文档中
        document.add(table);

        // 关闭资源
        document.close();
        pdf.close();
        writer.close();
        workbook.close();
        excelFile.close();
    }

    private static org.apache.poi.ss.usermodel.Cell getMergedCell(Sheet sheet, int rowIndex, int colIndex) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            if (mergedRegion.isInRange(rowIndex, colIndex)) {
                int firstRow = mergedRegion.getFirstRow();
                int firstCol = mergedRegion.getFirstColumn();
                return sheet.getRow(firstRow).getCell(firstCol);
            }
        }
        return null;
    }

    private static String getCellValue(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue) && !Double.isInfinite(numericValue)) {
                        // 判断是否为整数
                        if (numericValue <= Integer.MAX_VALUE) {
                            return Integer.toString((int) numericValue);
                        } else {
                            return Long.toString((long) numericValue);
                        }
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private static Cell createStyledCell(org.apache.poi.ss.usermodel.Cell excelCell, String cellValue, PdfFont font, XSSFWorkbook workbook) {
        try {
            Cell pdfCell = new Cell();
            Paragraph paragraph = new Paragraph(cellValue).setFont(font);

            if (excelCell != null) {
                // 处理字体样式
                Font excelFont = excelCell.getSheet().getWorkbook().getFontAt(excelCell.getCellStyle().getFontIndex());
                if (excelFont.getBold()) {
                    paragraph.setBold();
                }
                if (excelFont.getItalic()) {
                    paragraph.setItalic();
                }
                paragraph.setFontSize(excelFont.getFontHeightInPoints());

                // 处理文字颜色
                if (excelFont instanceof XSSFFont) {
                    XSSFFont xssfFont = (XSSFFont) excelFont;
                    org.apache.poi.ss.usermodel.Color excelColor = xssfFont.getXSSFColor();
                    if (excelColor != null) {
                        byte[] rgb = ((XSSFColor) excelColor).getRGB();
                        if (rgb != null) {
                            Color pdfColor = new DeviceRgb(rgb[0] & 0xff, rgb[1] & 0xff, rgb[2] & 0xff);
                            paragraph.setFontColor(pdfColor);
                        }
                    }
                }

                // 处理对齐方式
                HorizontalAlignment horizontalAlignment = convertAlignment(excelCell.getCellStyle().getAlignment());
                pdfCell.setTextAlignment(TextAlignment.valueOf(horizontalAlignment.name()));
                pdfCell.setVerticalAlignment(convertVerticalAlignment(excelCell.getCellStyle().getVerticalAlignment()));

                // 处理边框
                applyBorders(excelCell.getCellStyle(), pdfCell, workbook);

                // 处理背景颜色
                applyBackgroundColor(excelCell.getCellStyle(), pdfCell, workbook);
            }

            pdfCell.add(paragraph);
            return pdfCell;
        } catch (Exception e) {
            logger.error("创建 Cell 对象时出现异常，单元格值: " + cellValue);
            e.printStackTrace();
            return null;
        }
    }

    private static HorizontalAlignment convertAlignment(HorizontalAlignment excelAlignment) {
        switch (excelAlignment) {
            case LEFT:
                return HorizontalAlignment.LEFT;
            case CENTER:
                return HorizontalAlignment.CENTER;
            case RIGHT:
                return HorizontalAlignment.RIGHT;
            default:
                return HorizontalAlignment.LEFT;
        }
    }

    private static com.itextpdf.layout.property.VerticalAlignment convertVerticalAlignment(VerticalAlignment excelAlignment) {
        switch (excelAlignment) {
            case TOP:
                return com.itextpdf.layout.property.VerticalAlignment.TOP;
            case CENTER:
                return com.itextpdf.layout.property.VerticalAlignment.MIDDLE;
            case BOTTOM:
                return com.itextpdf.layout.property.VerticalAlignment.BOTTOM;
            default:
                return com.itextpdf.layout.property.VerticalAlignment.MIDDLE;
        }
    }

    private static void applyBorders(CellStyle excelCellStyle, Cell pdfCell, XSSFWorkbook workbook) {
        if (excelCellStyle instanceof XSSFCellStyle) {
            XSSFCellStyle xssfCellStyle = (XSSFCellStyle) excelCellStyle;
            BorderStyle topBorder = xssfCellStyle.getBorderTop();
            BorderStyle bottomBorder = xssfCellStyle.getBorderBottom();
            BorderStyle leftBorder = xssfCellStyle.getBorderLeft();
            BorderStyle rightBorder = xssfCellStyle.getBorderRight();

            org.apache.poi.ss.usermodel.Color topBorderColor = xssfCellStyle.getTopBorderXSSFColor();
            org.apache.poi.ss.usermodel.Color bottomBorderColor = xssfCellStyle.getBottomBorderXSSFColor();
            org.apache.poi.ss.usermodel.Color leftBorderColor = xssfCellStyle.getLeftBorderXSSFColor();
            org.apache.poi.ss.usermodel.Color rightBorderColor = xssfCellStyle.getRightBorderXSSFColor();

            Color defaultColor = new DeviceRgb(0, 0, 0); // 默认颜色为黑色

            if (topBorder != BorderStyle.NONE) {
                Color pdfTopBorderColor = getColor(topBorderColor);
                if (pdfTopBorderColor == null) {
                    pdfTopBorderColor = defaultColor;
                }
                pdfCell.setBorderTop(new SolidBorder(pdfTopBorderColor, 1));
            }
            if (bottomBorder != BorderStyle.NONE) {
                Color pdfBottomBorderColor = getColor(bottomBorderColor);
                if (pdfBottomBorderColor == null) {
                    pdfBottomBorderColor = defaultColor;
                }
                pdfCell.setBorderBottom(new SolidBorder(pdfBottomBorderColor, 1));
            }
            if (leftBorder != BorderStyle.NONE) {
                Color pdfLeftBorderColor = getColor(leftBorderColor);
                if (pdfLeftBorderColor == null) {
                    pdfLeftBorderColor = defaultColor;
                }
                pdfCell.setBorderLeft(new SolidBorder(pdfLeftBorderColor, 1));
            }
            if (rightBorder != BorderStyle.NONE) {
                Color pdfRightBorderColor = getColor(rightBorderColor);
                if (pdfRightBorderColor == null) {
                    pdfRightBorderColor = defaultColor;
                }
                pdfCell.setBorderRight(new SolidBorder(pdfRightBorderColor, 1));
            }
        }
    }

    private static Color getColor(org.apache.poi.ss.usermodel.Color excelColor) {
        if (excelColor instanceof XSSFColor) {
            XSSFColor xssfColor = (XSSFColor) excelColor;
            byte[] rgb = xssfColor.getRGB();
            if (rgb != null) {
                return new DeviceRgb(rgb[0] & 0xff, rgb[1] & 0xff, rgb[2] & 0xff);
            }
        }
        return null;
    }

    private static void applyBackgroundColor(CellStyle excelCellStyle, Cell pdfCell, XSSFWorkbook workbook) {
        if (excelCellStyle instanceof XSSFCellStyle) {
            XSSFCellStyle xssfCellStyle = (XSSFCellStyle) excelCellStyle;
            org.apache.poi.ss.usermodel.Color excelColor = xssfCellStyle.getFillForegroundXSSFColor();
            if (excelColor != null) {
                byte[] rgb = ((XSSFColor) excelColor).getRGB();
                if (rgb != null) {
                    Color pdfColor = new DeviceRgb(rgb[0] & 0xff, rgb[1] & 0xff, rgb[2] & 0xff);
                    pdfCell.setBackgroundColor(pdfColor);
                }
            }
        }
    }

    public static void main(String[] args) {
        String excelFilePath = "D:\\ruoyi\\input.xls";
        String pdfFilePath = "D:\\ruoyi\\output.pdf";

        try {
            ExcelToPdfConverter.convertExcelToPdf(excelFilePath, pdfFilePath);
        } catch (IOException e) {
            System.err.println("文件读写过程中出现错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("导出过程中出现未知错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

