package com.ruoyi.utils;

import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.ParagraphStyle;
import com.deepoove.poi.data.style.CellStyle;
import com.deepoove.poi.data.style.Style;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class ExcelToPoiTlConverter {

    /**
     * 将Excel文件转换为poi-tl的TableRenderData对象
     */
    public static TableRenderData convertToTableData(InputStream inputStream, String sheetName,
                                                     int startRow, int endRow, boolean useHeaderRow)
            throws IOException {
        try (Workbook workbook = createWorkbook(inputStream)) {
            Sheet sheet = sheetName != null ? workbook.getSheet(sheetName) : workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Sheet not found: " + sheetName);
            }

            // 计算实际的结束行
            if (endRow < 0 || endRow > sheet.getLastRowNum()) {
                endRow = sheet.getLastRowNum();
            }

            // 确保开始行和结束行的有效性
            if (startRow < 0 || startRow > endRow) {
                throw new IllegalArgumentException("Invalid startRow or endRow");
            }

            // 准备表格数据
            List<RowRenderData> tableData = new ArrayList<>();
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

            // 处理表头行
            Row headerRow = null;
            if (useHeaderRow && startRow <= endRow) {
                headerRow = sheet.getRow(startRow);
                startRow++; // 表头处理后，数据行从下一行开始
            }

            // 处理表头样式
            List<CellRenderData> headerCells = null;
            if (headerRow != null) {
                headerCells = createRowCells(headerRow, evaluator, true);
            }

            // 处理数据行
            for (int i = startRow; i <= endRow; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    List<CellRenderData> rowCells = createRowCells(row, evaluator, false);
                    RowRenderData rowRenderData = new RowRenderData();
                    rowRenderData.setCells(rowCells);
                    tableData.add(rowRenderData);
                }
            }

            // 创建TableRenderData对象（适配无参构造函数版本）
            TableRenderData tableRenderData = new TableRenderData();

            // 添加表头行（如果有）
            if (headerCells != null) {
                RowRenderData header = new RowRenderData();
                header.setCells(headerCells);
                tableRenderData.addRow(header);
            }

            // 添加所有数据行
            for (RowRenderData row : tableData) {
                tableRenderData.addRow(row);
            }

            return tableRenderData;
        }
    }

    /**
     * 根据输入流创建Workbook对象
     */
    private static Workbook createWorkbook(InputStream inputStream) throws IOException {
        try {
            return new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            inputStream.reset();
            return new HSSFWorkbook(inputStream);
        }
    }

    /**
     * 创建行的单元格数据 - 适配旧版本API
     */
   /* private static List<CellRenderData> createRowCells(Row row, FormulaEvaluator evaluator, boolean isHeader) {
        List<CellRenderData> cells = new ArrayList<>();
        int lastCellNum = row.getLastCellNum();

        for (int j = 0; j < lastCellNum; j++) {
            Cell cell = row.getCell(j);
            String cellValue = getCellValueAsString(cell, evaluator);

            // 创建单元格样式
            CellStyle cellStyle = new CellStyle();
            boolean hasStyle = false;

            if (isHeader) {
                // 设置表头样式
               // cellStyle.setBackgroundColor("4A86E8");

                // 创建段落样式
                ParagraphStyle paragraphStyle = new ParagraphStyle();

                // 创建文本样式
                Style textStyle = new Style();
                textStyle.setBold(true);
                textStyle.setColor("FFFFFF");

                // 将文本样式应用到段落样式
                paragraphStyle.setDefaultTextStyle(textStyle);

                // 将段落样式应用到单元格样式
                cellStyle.setDefaultParagraphStyle(paragraphStyle);
                hasStyle = true; // 标记为有样式

            }

            // 创建单元格数据
            CellRenderData cellData = new CellRenderData();

            // 创建段落数据并添加文本
            ParagraphRenderData paragraph = new ParagraphRenderData();
            TextRenderData textData = new TextRenderData(cellValue);
            paragraph.addText(textData);

            // 将段落添加到单元格
            cellData.addParagraph(paragraph);
            if (hasStyle){
                cellData.setCellStyle(cellStyle);
            }
            cells.add(cellData);
        }

        return cells;
    }
*/
    /**
     * 创建行的单元格数据 - 适配旧版本API
     */
    private static List<CellRenderData> createRowCells(Row row, FormulaEvaluator evaluator, boolean isHeader) {
        List<CellRenderData> cells = new ArrayList<>();
        int lastCellNum = row.getLastCellNum();

        for (int j = 0; j < lastCellNum; j++) {
            Cell cell = row.getCell(j);
            String cellValue = getCellValueAsString(cell, evaluator);

            // 创建单元格样式
            CellStyle cellStyle = new CellStyle();

            if (isHeader) {
                // 设置表头样式（至少保留一项样式设置）
                Style textStyle = new Style();
                textStyle.setBold(true);
                textStyle.setColor("000000"); // 确保文本颜色非空

                // 创建段落样式并应用文本样式
                ParagraphStyle paragraphStyle = new ParagraphStyle();
                paragraphStyle.setDefaultTextStyle(textStyle);

                // 将段落样式应用到单元格样式
                cellStyle.setDefaultParagraphStyle(paragraphStyle);
            }

            // 创建单元格数据
            CellRenderData cellData = new CellRenderData();

            // 创建段落数据
            ParagraphRenderData paragraph = new ParagraphRenderData();

            // 关键修改：必须设置段落的文本列表
            List<RenderData> texts = new ArrayList<>();
            TextRenderData textData = new TextRenderData(cellValue);
            texts.add(textData);
            paragraph.setContents(texts); // 显式设置文本列表

            // 将段落添加到单元格
            cellData.addParagraph(paragraph);

            // 应用单元格样式
            cellData.setCellStyle(cellStyle);

            cells.add(cellData);
        }

        return cells;
    }


    /**
     * 获取单元格的值并转换为字符串
     */
    private static String getCellValueAsString(Cell cell, FormulaEvaluator evaluator) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.format("%d", (long) numericValue);
                    } else {
                        return String.format("%.2f", numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                CellValue cellValue = evaluator.evaluate(cell);
                return getCellValueAsString(cellValue, evaluator);
            case BLANK:
                return "";
            default:
                return cell.toString();
        }
    }

    /**
     * 获取CellValue的值并转换为字符串
     */
    private static String getCellValueAsString(CellValue cellValue, FormulaEvaluator evaluator) {
        if (cellValue == null) {
            return "";
        }

        switch (cellValue.getCellType()) {
            case STRING:
                return cellValue.getStringValue();
            case NUMERIC:
                double numericValue = cellValue.getNumberValue();
                if (numericValue == (long) numericValue) {
                    return String.format("%d", (long) numericValue);
                } else {
                    return String.format("%.2f", numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cellValue.getBooleanValue());
            case ERROR:
                return "ERROR";
            default:
                return "";
        }
    }
}
