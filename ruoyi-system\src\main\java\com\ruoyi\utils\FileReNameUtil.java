package com.ruoyi.utils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class FileReNameUtil {
    public static String renameAndSaveFile(String originalFilePath, String newFileName) {
        Path originalPath = Paths.get(originalFilePath);
        if (!Files.exists(originalPath)) {
            System.out.println("原文件不存在: " + originalFilePath);
            return "";
        }

        String fileExtension = getFileExtension(originalFilePath);
        String newFilePath = originalPath.getParent() + java.io.File.separator + newFileName + fileExtension;
        Path newPath = Paths.get(newFilePath);

        try {
            Files.copy(originalPath, newPath);
            System.out.println("文件已成功保存为: " + newFilePath);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        }
        return newFilePath;
    }

    private static String getFileExtension(String filePath) {
        int lastIndex = filePath.lastIndexOf('.');
        if (lastIndex != -1) {
            return filePath.substring(lastIndex);
        }
        return "";
    }

}
