package com.ruoyi.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.eval.domain.EvalExpertEvaluationInfo;
import com.ruoyi.eval.service.IEvalExpertEvaluationInfoService;
import okhttp3.RequestBody;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@ComponentScan
public class LoginZFCGRequest {
    @Autowired
    private IEvalExpertEvaluationInfoService evalExpertEvaluationInfoService;
    @Value("${extractcode.username}")
    String username;
    @Value("${extractcode.thirdPartySecret}")
    String thirdPartySecret;
    @Value("${extractcode.password}")
    String password;
    @Value("${extractcode.url}")
    String tokenUrl;
    @Value("${extractcode.zhuanJiaInfo}")
    String zhuanJiaInfo;
    @Value("${extractcode.ZhuanJia}")
    String getZhuanJia;
    @Autowired
    IBusiExtractExpertResultService iBusiExtractExpertResultService;
    @Autowired
    IEvalExpertEvaluationInfoService iEvalExpertEvaluationInfoService;
    @Value("${extractcode.EditZhuanJiaInfo}")
    String editZhuanJiaInfo;
    @Value("${extractcode.chouQu}")
    String chouqu;
    @Value("${extractcode.resetExtract}")
    String resetExtract;
    @Value("${extractcode.deleteExtract}")
    String deleteExtract;
   @RequestMapping("/LoginRequest/getToken")
   public String getToken() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\"t\":"+System.currentTimeMillis()+",\r\n\"username\":\""+username+"\",\r\n\"password\":\""+password+"\",\r\n\"thirdPartySecret\":\""+thirdPartySecret+"\"\r\n}");

       System.out.println("{\r\n\"t\":"+System.currentTimeMillis()+",\r\n\"username\":\""+username+"\",\r\n\"password\":\""+password+"\",\r\n\"thirdPartySecret\":\""+thirdPartySecret+"\"\r\n}");
        Request request = new Request.Builder()
                .url(tokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
       if (response.body() != null) {
           JSONObject jsonObject = JSON.parseObject(response.body().string());
           return jsonObject.getString("token");
       }else {
           throw new RuntimeException("请求专家抽取token 失败");
       }
    }


    @RequestMapping("/LoginRequest/getZhuanJia")
   public AjaxResult getZhuanJia() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(getZhuanJia)
                .method("GET", null)
                .addHeader("Token", getToken())
                .addHeader("Cookie", "JSESSIONID=6733129108BF13A527206E2A72BD14FF")
                .build();
        Response response = client.newCall(request).execute();
        ResultData resultData=JSONObject.parseObject(response.body().string(), ResultData.class);
        //List<ResultData.DataBean.ExpertsBean> list=resultData.getData().getExperts();
        return AjaxResult.success(resultData);
    }
    @GetMapping(value = "/LoginRequest/zhuanJiaInfo/{resultId}")
    public AjaxResult zhuanJiaInfo(@PathVariable String resultId) throws IOException {
        BusiExtractExpertResult extractExpertResultServiceById = iBusiExtractExpertResultService.getById(resultId);
//        if (extractExpertResultServiceById.getIsOwner()==0){
//            OkHttpClient client = new OkHttpClient().newBuilder()
//                    .build();
//            Request request = new Request.Builder()
//                    .url(zhuanJiaInfo+extractExpertResultServiceById.getExpertId())
//                    .method("GET", null)
//                    .addHeader("Token", getToken())
//                    .build();
//            Response response = client.newCall(request).execute();
//            ZhuanJiaInfoVo zhuanJiaInfoVo=JSONObject.parseObject(response.body().string(), ZhuanJiaInfoVo.class);
//            extractExpertResultServiceById.setZhuanJiaInfoVo(zhuanJiaInfoVo.getExpert());
//        }
        return AjaxResult.success(extractExpertResultServiceById);
    }

    @PostMapping(value = "/LoginRequest/editZhuanJiaInfo")
    public AjaxResult EditZhuanJiaInfo(@org.springframework.web.bind.annotation.RequestBody UpdateZhuanJiaInfoParams updateZhuanJiaInfoParams) throws IOException {
        EvalExpertEvaluationInfo evaluationInfo = iEvalExpertEvaluationInfoService.getById(updateZhuanJiaInfoParams.getEvalExpertEvaluationInfoId());

        BusiExtractExpertResult extractExpertResult = iBusiExtractExpertResultService.getById(evaluationInfo.getExpertResultId());
//        if (extractExpertResult.getIsOwner()==0){
//            OkHttpClient client = new OkHttpClient().newBuilder()
//                    .build();
//            MediaType mediaType = MediaType.parse("application/json");
//            //UpdateZhuanJiaInfoParams updateZhuanJiaInfoParams = JSON.parseObject(json, UpdateZhuanJiaInfoParams.class);
//            RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(updateZhuanJiaInfoParams));
//            Request request = new Request.Builder()
//                    .url(editZhuanJiaInfo)
//                    .method("POST", body)
//                    .addHeader("Token", getToken())
//                    .addHeader("Content-Type", "application/json")
//                    .build();
//            Response response = client.newCall(request).execute();
//            ResultData resultData=JSONObject.parseObject(response.body().string(), ResultData.class);
//            if (resultData.getCode()!=0){
//                return AjaxResult.error();
//            }
//        }

       return AjaxResult.success();
    }



   public Boolean getChouQu(ParamVo paramVo) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
       RequestBody body = RequestBody.create(MediaType.parse("application/json"),JSON.toJSONString(paramVo).toString());

        Request request = new Request.Builder()
                .url(chouqu)
                .method("GET", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=071327329DCF80B72EA2FFFDB9396A46")
                .build();
        try {
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            if (jsonObject.get("msg").equals("操作成功")){
                return  true;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
       return  false;
   }


    public void   quxiao(String ids) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, ids);
        Request request = new Request.Builder()
                .url(deleteExtract)
                .method("POST", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=C67EE2FCCCFA74425CEDCBD01FE50067")
                .build();
        Response response = client.newCall(request).execute();
    }


    public static void main(String[] args) {
        String[] strArray={"111111"};
        System.out.println(strArray);

    }
}
