package com.ruoyi.utils;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ParamVo {


    /**
     * exclusionConditions : ["culpa"]
     * startTime : 1981-10-10 05:16:00
     * endTime : 2002-06-01 02:30:12
     * projectCode : 35
     * demands : [{"id":"80","proId":"10","qwsl":29,"cqfs":"dolor","tzfs":"exercitation laboris tempor Duis","bz":"adipisicing","pspm":"aliqua adipisicing ullamco tempor","expIds":"23"}]
     */

    private String startTime;
    private String endTime;
    //项目id
    private Long projectCode;
    //评审地点
    private String psdd;
    //评审具体时间
    private String psjtsj;
    //抽取规避的专家名称和公司名称
    private List<String> exclusionConditions;
    private List<DemandsBean> demands;
    @Data
    public static class DemandsBean {
        /**
         * id : 80
         * proId : 10
         * qwsl : 29
         * cqfs : dolor
         * tzfs : exercitation laboris tempor Duis
         * bz : adipisicing
         * pspm : aliqua adipisicing ullamco tempor
         * expIds : 23
         */
        //修改时才留id
        private String id;
        //项目id
        private Long proId;
        //数量
        private int qwsl;
        //抽取方式  随机抽取/自主抽取
        private String cqfs;
        private String tzfs;
        private String bz;
        //评审品目
        private String pspm;
        //自主抽取专家ids
        private String expIds;
    }
//新增需求，打通专家信息，代理机构扣费等问题
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目类别（工程/货物/服务）
     */
    private String projectCategory;
    /**
     * 采购方式（直接传汉字，如公开招标、竞争性谈判等）
     */
    private String procurementMethod;
    /**
     * 抽取类别
     */
    private String cqlb="限额以下评审";
    /**
     * 采购人名称
     */
    private String purchaserName;
    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;
    /**
     * 采购单位联系人
     */
    private String purchasingUnitContact;
    /**
     * 采购单位联系电话
     */
    private String purchasingUnitPhone;
    /**
     * 项目隶属县区（汉字，如某某区、某某县）
     */
    private String projectAffiliatedDistrict;
    /**
     * 包数量，固定为 1
     */
    private Integer packageQuantity = 1;
    /**
     * 说明信息
     */
    private String description;
    /**
     * 项目状态，固定为 2（需确认状态值 2 对应的具体业务含义，如已立项等）
     */
    private Integer projectStatus = 2;
    private String projectType;

}
