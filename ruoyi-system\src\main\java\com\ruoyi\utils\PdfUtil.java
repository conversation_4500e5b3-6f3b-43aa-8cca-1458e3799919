package com.ruoyi.utils;

import com.alibaba.fastjson2.JSONObject;
import com.itextpdf.io.IOException;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.parser.PdfTextExtractor;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.procurement.vo.ProcurementScoreItemKeywordEnum;
import com.ruoyi.procurement.vo.ScoreItemKeywordEnum;
import freemarker.template.Template;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.canvas.parser.PdfCanvasProcessor;
import com.itextpdf.kernel.pdf.canvas.parser.listener.LocationTextExtractionStrategy;
import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class PdfUtil {

    @Resource
    private FreeMarkerConfigurer configurer;

    public String toHtml(Map<String, Object> map, String ftlPath) throws Exception{
        Template template = configurer.getConfiguration().getTemplate(ftlPath);
        return FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
    }

    public static String addPdf(String basePath, String insertPath, String keyword, String path) throws Exception{
        // 指定源PDF文件和新PDF文件的路径
        String[] srcPdfPaths = insertPath.split(",");
        String srcPdfPath = AttachmentUtil.urlToReal(insertPath); // 原始PDF文件
        String destPdfPath = path + "/"+IdUtils.longUUID()+".pdf"; // 合并后的PDF文件
        String pdfToInsertPath = basePath; // 要插入的PDF文件
        int insertPageNumber = 1; // 指定在源PDF中的插入位置（从1开始计数）

        try {
            // 创建一个PdfDocument实例来读取源PDF文件
            PdfDocument pdf = new PdfDocument(new PdfWriter(destPdfPath));

            // 创建一个PdfDocument实例来读取要插入的PDF文件
//            PdfDocument pdfsrc = new PdfDocument(new PdfReader(srcPdfPath));
            List<PdfDocument> pdfsrcList = new ArrayList<>();
            for (String pdfPath : srcPdfPaths) {
                pdfsrcList.add(new PdfDocument(new PdfReader(AttachmentUtil.urlToReal(pdfPath))));
            }
            // 创建一个PdfDocument实例来读取要插入的PDF文件
            PdfDocument pdfToInsert = new PdfDocument(new PdfReader(pdfToInsertPath));


            for(int i=1;i<=pdfToInsert.getNumberOfPages();i++) {
                String pageContent = PdfTextExtractor.getTextFromPage(pdfToInsert.getPage(i));
                if (pageContent.contains(keyword)) {
                    insertPageNumber = i;
//                    break;
                }
            }

            // 获取源PDF文档中要插入页面的索引位置
            int insertPageIndex = 1;

            // 插入PDF文档的页面到源PDF文档的指定位置
            for (int i = 1; i <= pdfToInsert.getNumberOfPages(); i++) {
                PdfPage pageToInsert = pdfToInsert.getPage(i);
                pdf.addPage(insertPageIndex++, pageToInsert.copyTo(pdf));
                if(i==insertPageNumber){
                    for(PdfDocument pDoc : pdfsrcList) {
                        for (int j = 1; j <= pDoc.getNumberOfPages(); j++) {
                            PdfPage pageSrc = pDoc.getPage(j);
                            pdf.addPage(insertPageIndex++, pageSrc.copyTo(pdf));
                        }
                    }
                }
            }

            // 关闭文档
            pdfToInsert.close();
            pdf.close();

            System.out.println("PDF合并成功。");
            return destPdfPath;
        } catch (FileNotFoundException e) {
            System.err.println("PDF文件未找到。");
            e.printStackTrace();
        } catch (IOException e) {
            System.err.println("无法读取或写入PDF文件。");
            e.printStackTrace();
        }
        return "";
    }


    public static String getPageNum(String basePath, List<ScoreItemKeywordEnum> keywords) throws Exception{
        JSONObject map = new JSONObject();

        // 创建一个PdfDocument实例来读取PDF文件
        PdfDocument basePdf = new PdfDocument(new PdfReader(basePath));
        for(int i=1;i<=basePdf.getNumberOfPages();i++) {
            String pageContent = PdfTextExtractor.getTextFromPage(basePdf.getPage(i)).replace("\n", "");
            for (ScoreItemKeywordEnum keyword : keywords) {
                if (pageContent.contains(keyword.getKeyword())) {
                    map.put(keyword.getScoreItemName(), i);
                }
            }
        }
        return map.toJSONString();
    }

    public static String getProcurementPageNum(String basePath, List<ProcurementScoreItemKeywordEnum> keywords) throws Exception{
        JSONObject map = new JSONObject();

        // 创建一个PdfDocument实例来读取PDF文件
        PdfDocument basePdf = new PdfDocument(new PdfReader(basePath));
        for(int i=1;i<=basePdf.getNumberOfPages();i++) {
            String pageContent = PdfTextExtractor.getTextFromPage(basePdf.getPage(i)).replace("\n", "");
            for (ProcurementScoreItemKeywordEnum keyword : keywords) {
                if (pageContent.contains(keyword.getKeyword())) {
                    map.put(keyword.getScoreItemName(), i);
                }
            }
        }
        return map.toJSONString();
    }

    public static String deleteBlankPageAndCreateFooter(String genPath, String path) throws java.io.IOException {
        String endPath = path + "/"+IdUtils.longUUID()+".pdf"; // 删除后的PDF文件
        // 创建PdfDocument对象，用于读取和写入PDF文档
        PdfDocument pdfDoc = new PdfDocument(new PdfReader(genPath), new PdfWriter(endPath));
        // 创建Document对象，用于在PDF文档中添加内容
        Document doc = new Document(pdfDoc);
        // 删除空白页
        for(int i=1; i<=pdfDoc.getNumberOfPages(); i++) {
//            String pageContent = PdfTextExtractor.getTextFromPage(pdfDoc.getPage(i)).replace("\n", "").trim();
//            if (StringUtils.isBlank(pageContent)) {
//                pdfDoc.removePage(i);
//                i--;
//            }
            PdfPage page = pdfDoc.getPage(i);
            System.out.println("第" + i + "页");
            if (!hasNonTextContent(page)) {
                System.out.println("无数据---" + i);
                pdfDoc.removePage(i);
                i--;
            }
        }
        // 添加页码
        // 获取PDF文档的总页数
        int totalPages = pdfDoc.getNumberOfPages();
        for(int i=1; i<=totalPages; i++) {
            // 在页面底部居中位置添加页码
            doc.showTextAligned(new Paragraph(i + "/" + totalPages)
                            .setFont(PdfFontFactory.createFont()),
                    pdfDoc.getPage(i).getPageSize().getWidth() / 2,
                    30, // 页码距离页面底部的距离
                    i,
                    TextAlignment.CENTER,
                    VerticalAlignment.BOTTOM,
                    0);
        }
        // 关闭文档
        doc.close();
        return endPath;
    }

    /**
     * 判断pdf页面种是否内容，有返回true
     * @param page
     * @return
     */
    public static boolean hasNonTextContent(PdfPage page) {
        // 提取文本内容
        LocationTextExtractionStrategy strategy = new LocationTextExtractionStrategy();
        PdfCanvasProcessor parser = new PdfCanvasProcessor(strategy);
        parser.processPageContent(page);
        // 如果提取到的文本不为空，说明页面有内容
        if (strategy.getResultantText().replace("\n", "").trim().isEmpty()) {// 判断文本为空
            // 如果判断文本为空，还需要判断是否是图片报表等资源
            // 检查页面资源字典
            PdfResources resources = page.getResources();
            if (resources != null) {
                // 检查是否有 XObject（通常表示图片）资源
                if (resources.getResource(PdfName.XObject)!=null && !resources.getResource(PdfName.XObject).isEmpty()) {
                    return true;
                }
                // 检查是否有字体资源
//                if (resources.getResource(PdfName.Font)!=null && !resources.getResource(PdfName.Font).isEmpty()) {
//                    return true;
//                }
            }
            return false;
        }
        return true;
    }

    public static void main(String[] args) throws Exception{
        // 创建一个PdfDocument实例来读取源PDF文件
        String inputPath = "C:/Users/<USER>/Desktop/上传/input.pdf"; // 原始PDF文件
        String path = "C:/Users/<USER>/Desktop/上传/"; // 目标 PDF 文件夹
        deleteBlankPageAndCreateFooter(inputPath,path);

        // 指定源PDF文件和新PDF文件的路径
//        String srcPdfPath = "D:/tmp/111.pdf"; // 原始PDF文件
//        String destPdfPath = "d:/tmp/111222.pdf"; // 合并后的PDF文件
//        String pdfToInsertPath = "D:/tmp/222.pdf"; // 要插入的PDF文件
//        int insertPageNumber = 1; // 指定在源PDF中的插入位置（从1开始计数）
//
//        String basePath = "d:/3.pdf";
//        String m = PdfUtil.getPageNum(basePath, ScoreItemKeywordEnum.getKeywordList(1,0));
//        System.out.println(m);
    }

//    public static boolean hasNonTextContent(String filePath) {
//        try (PdfReader reader = new PdfReader(new File(filePath));
//             PdfDocument pdfDocument = new PdfDocument(reader)) {
//            // 获取 PDF 文件的页面数量
//            int pageCount = pdfDocument.getNumberOfPages();
//            // 如果页面数量为 0，说明 PDF 为空
//            if (pageCount == 0) {
//                return false;
//            }
//            // 遍历每一页
//            for (int i = 1; i <= pageCount; i++) {
//                PdfPage page = pdfDocument.getPage(i);
//                // 提取文本内容
//                LocationTextExtractionStrategy strategy = new LocationTextExtractionStrategy();
//                PdfCanvasProcessor parser = new PdfCanvasProcessor(strategy);
//                parser.processPageContent(page);
//                // 如果提取到的文本不为空，说明页面有内容
//                if (!strategy.getResultantText().isEmpty()) {
//                    return true;
//                }
//                // 检查页面资源字典
//                PdfResources resources = page.getResources();
//                if (resources != null) {
//                    // 检查是否有 XObject（通常表示图片）资源
//                    if (resources.getResource(PdfName.XObject)!=null && !resources.getResource(PdfName.XObject).isEmpty()) {
//                        return true;
//                    }
//                    // 检查是否有字体资源
//                    if (resources.getResource(PdfName.Font)!=null && !resources.getResource(PdfName.Font).isEmpty()) {
//                        return true;
//                    }
//                }
//            }
//            return false;
//        } catch (IOException | java.io.IOException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }
}
