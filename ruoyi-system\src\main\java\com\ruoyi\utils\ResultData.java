package com.ruoyi.utils;

import java.util.List;

public class ResultData {

    /**
     * msg : 操作成功
     * code : 0
     * data : {"category":"","expertId":"","expertCount":171,"pspm":"","experts":[{"id":377,"xm":"王庆利","xb":"","zzmm":"","byxx":"","xcszy":"","xrzw":"","sjhm":"17335528750","txdz":"","gznx":"","zjlx":"居民身份证","csrq":"","zgxl":"文化程度/大学本科.../本科","bysj":"","xgzdw":"河南云中鹤大数据产业发展有限公司","zc":"职称/工程技术人员.../工程师","dzyx":"<EMAIL>","hyfl":"","zjhm":"410782198611241571","mz":"","zp":"https://hb.huicai360.cn/file/zcxh/expertPic/2024-04-24/e2323914-153a-4055-9722-9f6112be8496.jpg","sxzy":"从事专业/工学/电气信息类/自动化","czd":"","zcdj":0,"yb":"","zpqy":"","pspm":"专业品目/货物类/电子产品及通信设备,专业品目/工程类/电力工程","gzjl":"2009.09-2011.07 河南城建学院自动化专业大学本科学习，获学士学位；\n2011.07-2012.09 河南平高电气股份有限公司电控设备厂技术科，主要负责高压带电显示、温控器等产品，兼职质量管理员、兼职分工会组织委员、党支部组织委员；\n2012.10-2018.09 河南平高电气股份有限公司智能控制事业部电控车间，负责SF6气体密度继电器、阀体、内置式局放传感器等产品，兼职分工会组织委员、兼职党支部组织委员；\n2018.10-2020.04 河南平高电气股份有限公司智能控制事业部仪器仪表业务部负责人，兼职分工会组织委员；智能化元器件评标专家。\n2020.09-2022.03 河南平高电气股份有限公司电控设备事业部技术处工艺技术、劳模(技能大师)创新工作室(集团级)带头人、五级职员；智能化元器件评标专家。\n2022.04-今     在河南云中鹤大数据产业发展有限公司数字产业部担任鹤壁政府采购E慧采平台运营岗位。","dwyj":"","zt":1,"cjsj":"2024-04-24 17:21:04","zh":"","mm":"","userId":"","hm":"王庆利","khh":"中国建设银行股份有限公司平顶山湛南支行","yhzh":"6217002440000540112"},{"id":375,"xm":"秦爱玲","xb":"","zzmm":"","byxx":"","xcszy":"","xrzw":"","sjhm":"13839239602","txdz":"","gznx":"","zjlx":"居民身份证","csrq":"","zgxl":"文化程度/大学本科.../大普","bysj":"","xgzdw":"鹤壁市审计局（已退休）","zc":"职称/会计人员.../高级会计师","dzyx":"","hyfl":"","zjhm":"410603196604122021","mz":"","zp":"https://hb.huicai360.cn/file/zcxh/expertPic/2024-04-19/e9ac9db0-e157-4259-b4ff-8a55bdc41367.jpg","sxzy":"从事专业/经济学/经济学类/经济学","czd":"","zcdj":1,"yb":"","zpqy":"","pspm":"专业品目/服务类/税务服务,专业品目/服务类/办公综合服务","gzjl":"鹤壁市审计局（已退休）","dwyj":"","zt":1,"cjsj":"2024-04-19 20:46:45","zh":"","mm":"","userId":"","hm":"秦爱玲","khh":"工商银行淇滨支行","yhzh":"6222 0317  1000 0554 494"}]}
     */

    private String msg;
    private int code;
    private DataBean data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * category :
         * expertId :
         * expertCount : 171
         * pspm :
         * experts : [{"id":377,"xm":"王庆利","xb":"","zzmm":"","byxx":"","xcszy":"","xrzw":"","sjhm":"17335528750","txdz":"","gznx":"","zjlx":"居民身份证","csrq":"","zgxl":"文化程度/大学本科.../本科","bysj":"","xgzdw":"河南云中鹤大数据产业发展有限公司","zc":"职称/工程技术人员.../工程师","dzyx":"<EMAIL>","hyfl":"","zjhm":"410782198611241571","mz":"","zp":"https://hb.huicai360.cn/file/zcxh/expertPic/2024-04-24/e2323914-153a-4055-9722-9f6112be8496.jpg","sxzy":"从事专业/工学/电气信息类/自动化","czd":"","zcdj":0,"yb":"","zpqy":"","pspm":"专业品目/货物类/电子产品及通信设备,专业品目/工程类/电力工程","gzjl":"2009.09-2011.07 河南城建学院自动化专业大学本科学习，获学士学位；\n2011.07-2012.09 河南平高电气股份有限公司电控设备厂技术科，主要负责高压带电显示、温控器等产品，兼职质量管理员、兼职分工会组织委员、党支部组织委员；\n2012.10-2018.09 河南平高电气股份有限公司智能控制事业部电控车间，负责SF6气体密度继电器、阀体、内置式局放传感器等产品，兼职分工会组织委员、兼职党支部组织委员；\n2018.10-2020.04 河南平高电气股份有限公司智能控制事业部仪器仪表业务部负责人，兼职分工会组织委员；智能化元器件评标专家。\n2020.09-2022.03 河南平高电气股份有限公司电控设备事业部技术处工艺技术、劳模(技能大师)创新工作室(集团级)带头人、五级职员；智能化元器件评标专家。\n2022.04-今     在河南云中鹤大数据产业发展有限公司数字产业部担任鹤壁政府采购E慧采平台运营岗位。","dwyj":"","zt":1,"cjsj":"2024-04-24 17:21:04","zh":"","mm":"","userId":"","hm":"王庆利","khh":"中国建设银行股份有限公司平顶山湛南支行","yhzh":"6217002440000540112"},{"id":375,"xm":"秦爱玲","xb":"","zzmm":"","byxx":"","xcszy":"","xrzw":"","sjhm":"13839239602","txdz":"","gznx":"","zjlx":"居民身份证","csrq":"","zgxl":"文化程度/大学本科.../大普","bysj":"","xgzdw":"鹤壁市审计局（已退休）","zc":"职称/会计人员.../高级会计师","dzyx":"","hyfl":"","zjhm":"410603196604122021","mz":"","zp":"https://hb.huicai360.cn/file/zcxh/expertPic/2024-04-19/e9ac9db0-e157-4259-b4ff-8a55bdc41367.jpg","sxzy":"从事专业/经济学/经济学类/经济学","czd":"","zcdj":1,"yb":"","zpqy":"","pspm":"专业品目/服务类/税务服务,专业品目/服务类/办公综合服务","gzjl":"鹤壁市审计局（已退休）","dwyj":"","zt":1,"cjsj":"2024-04-19 20:46:45","zh":"","mm":"","userId":"","hm":"秦爱玲","khh":"工商银行淇滨支行","yhzh":"6222 0317  1000 0554 494"}]
         */

        private String category;
        private String expertId;
        private int expertCount;
        private String pspm;
        private List<ExpertsBean> experts;

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getExpertId() {
            return expertId;
        }

        public void setExpertId(String expertId) {
            this.expertId = expertId;
        }

        public int getExpertCount() {
            return expertCount;
        }

        public void setExpertCount(int expertCount) {
            this.expertCount = expertCount;
        }

        public String getPspm() {
            return pspm;
        }

        public void setPspm(String pspm) {
            this.pspm = pspm;
        }

        public List<ExpertsBean> getExperts() {
            return experts;
        }

        public void setExperts(List<ExpertsBean> experts) {
            this.experts = experts;
        }

        public static class ExpertsBean {
            /**
             * id : 377
             * xm : 王庆利
             * xb :
             * zzmm :
             * byxx :
             * xcszy :
             * xrzw :
             * sjhm : 17335528750
             * txdz :
             * gznx :
             * zjlx : 居民身份证
             * csrq :
             * zgxl : 文化程度/大学本科.../本科
             * bysj :
             * xgzdw : 河南云中鹤大数据产业发展有限公司
             * zc : 职称/工程技术人员.../工程师
             * dzyx : <EMAIL>
             * hyfl :
             * zjhm : 410782198611241571
             * mz :
             * zp : https://hb.huicai360.cn/file/zcxh/expertPic/2024-04-24/e2323914-153a-4055-9722-9f6112be8496.jpg
             * sxzy : 从事专业/工学/电气信息类/自动化
             * czd :
             * zcdj : 0
             * yb :
             * zpqy :
             * pspm : 专业品目/货物类/电子产品及通信设备,专业品目/工程类/电力工程
             * gzjl : 2009.09-2011.07 河南城建学院自动化专业大学本科学习，获学士学位；
             2011.07-2012.09 河南平高电气股份有限公司电控设备厂技术科，主要负责高压带电显示、温控器等产品，兼职质量管理员、兼职分工会组织委员、党支部组织委员；
             2012.10-2018.09 河南平高电气股份有限公司智能控制事业部电控车间，负责SF6气体密度继电器、阀体、内置式局放传感器等产品，兼职分工会组织委员、兼职党支部组织委员；
             2018.10-2020.04 河南平高电气股份有限公司智能控制事业部仪器仪表业务部负责人，兼职分工会组织委员；智能化元器件评标专家。
             2020.09-2022.03 河南平高电气股份有限公司电控设备事业部技术处工艺技术、劳模(技能大师)创新工作室(集团级)带头人、五级职员；智能化元器件评标专家。
             2022.04-今     在河南云中鹤大数据产业发展有限公司数字产业部担任鹤壁政府采购E慧采平台运营岗位。
             * dwyj :
             * zt : 1
             * cjsj : 2024-04-24 17:21:04
             * zh :
             * mm :
             * userId :
             * hm : 王庆利
             * khh : 中国建设银行股份有限公司平顶山湛南支行
             * yhzh : 6217002440000540112
             */

            private int id;
            private String xm;
            private String xb;
            private String zzmm;
            private String byxx;
            private String xcszy;
            private String xrzw;
            private String sjhm;
            private String txdz;
            private String gznx;
            private String zjlx;
            private String csrq;
            private String zgxl;
            private String bysj;
            private String xgzdw;
            private String zc;
            private String dzyx;
            private String hyfl;
            private String zjhm;
            private String mz;
            private String zp;
            private String sxzy;
            private String czd;
            private int zcdj;
            private String yb;
            private String zpqy;
            private String pspm;
            private String gzjl;
            private String dwyj;
            private int zt;
            private String cjsj;
            private String zh;
            private String mm;
            private String userId;
            private String hm;
            private String khh;
            private String yhzh;

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public String getXm() {
                return xm;
            }

            public void setXm(String xm) {
                this.xm = xm;
            }

            public String getXb() {
                return xb;
            }

            public void setXb(String xb) {
                this.xb = xb;
            }

            public String getZzmm() {
                return zzmm;
            }

            public void setZzmm(String zzmm) {
                this.zzmm = zzmm;
            }

            public String getByxx() {
                return byxx;
            }

            public void setByxx(String byxx) {
                this.byxx = byxx;
            }

            public String getXcszy() {
                return xcszy;
            }

            public void setXcszy(String xcszy) {
                this.xcszy = xcszy;
            }

            public String getXrzw() {
                return xrzw;
            }

            public void setXrzw(String xrzw) {
                this.xrzw = xrzw;
            }

            public String getSjhm() {
                return sjhm;
            }

            public void setSjhm(String sjhm) {
                this.sjhm = sjhm;
            }

            public String getTxdz() {
                return txdz;
            }

            public void setTxdz(String txdz) {
                this.txdz = txdz;
            }

            public String getGznx() {
                return gznx;
            }

            public void setGznx(String gznx) {
                this.gznx = gznx;
            }

            public String getZjlx() {
                return zjlx;
            }

            public void setZjlx(String zjlx) {
                this.zjlx = zjlx;
            }

            public String getCsrq() {
                return csrq;
            }

            public void setCsrq(String csrq) {
                this.csrq = csrq;
            }

            public String getZgxl() {
                return zgxl;
            }

            public void setZgxl(String zgxl) {
                this.zgxl = zgxl;
            }

            public String getBysj() {
                return bysj;
            }

            public void setBysj(String bysj) {
                this.bysj = bysj;
            }

            public String getXgzdw() {
                return xgzdw;
            }

            public void setXgzdw(String xgzdw) {
                this.xgzdw = xgzdw;
            }

            public String getZc() {
                return zc;
            }

            public void setZc(String zc) {
                this.zc = zc;
            }

            public String getDzyx() {
                return dzyx;
            }

            public void setDzyx(String dzyx) {
                this.dzyx = dzyx;
            }

            public String getHyfl() {
                return hyfl;
            }

            public void setHyfl(String hyfl) {
                this.hyfl = hyfl;
            }

            public String getZjhm() {
                return zjhm;
            }

            public void setZjhm(String zjhm) {
                this.zjhm = zjhm;
            }

            public String getMz() {
                return mz;
            }

            public void setMz(String mz) {
                this.mz = mz;
            }

            public String getZp() {
                return zp;
            }

            public void setZp(String zp) {
                this.zp = zp;
            }

            public String getSxzy() {
                return sxzy;
            }

            public void setSxzy(String sxzy) {
                this.sxzy = sxzy;
            }

            public String getCzd() {
                return czd;
            }

            public void setCzd(String czd) {
                this.czd = czd;
            }

            public int getZcdj() {
                return zcdj;
            }

            public void setZcdj(int zcdj) {
                this.zcdj = zcdj;
            }

            public String getYb() {
                return yb;
            }

            public void setYb(String yb) {
                this.yb = yb;
            }

            public String getZpqy() {
                return zpqy;
            }

            public void setZpqy(String zpqy) {
                this.zpqy = zpqy;
            }

            public String getPspm() {
                return pspm;
            }

            public void setPspm(String pspm) {
                this.pspm = pspm;
            }

            public String getGzjl() {
                return gzjl;
            }

            public void setGzjl(String gzjl) {
                this.gzjl = gzjl;
            }

            public String getDwyj() {
                return dwyj;
            }

            public void setDwyj(String dwyj) {
                this.dwyj = dwyj;
            }

            public int getZt() {
                return zt;
            }

            public void setZt(int zt) {
                this.zt = zt;
            }

            public String getCjsj() {
                return cjsj;
            }

            public void setCjsj(String cjsj) {
                this.cjsj = cjsj;
            }

            public String getZh() {
                return zh;
            }

            public void setZh(String zh) {
                this.zh = zh;
            }

            public String getMm() {
                return mm;
            }

            public void setMm(String mm) {
                this.mm = mm;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public String getHm() {
                return hm;
            }

            public void setHm(String hm) {
                this.hm = hm;
            }

            public String getKhh() {
                return khh;
            }

            public void setKhh(String khh) {
                this.khh = khh;
            }

            public String getYhzh() {
                return yhzh;
            }

            public void setYhzh(String yhzh) {
                this.yhzh = yhzh;
            }
        }
    }
}
