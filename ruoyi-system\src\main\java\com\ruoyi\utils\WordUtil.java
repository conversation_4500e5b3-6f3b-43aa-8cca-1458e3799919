package com.ruoyi.utils;

import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WordUtil {
    /***
     * @Description :替换段落文本
     * @param document docx解析对象
     * @param textMap  需要替换的信息集合
     * @return void
     * @Date 2022/11/17 17:22
     */
    public static void changeText(XWPFDocument document, Map<String, Object> textMap) {
        // 获取段落集合
        Iterator<XWPFParagraph> iterator = document.getParagraphsIterator();
        XWPFParagraph paragraph = null;
        while (iterator.hasNext()) {
            paragraph = iterator.next();
            // 判断此段落是否需要替换
            if (checkText(paragraph.getText())) {
                replaceValue(paragraph, textMap);
            }
        }
    }

    /***
     * @Description :检查文本中是否包含指定的字符(此处为“$”)
     * @param text
     * @return boolean
     * @Date 2022/11/17 17:22
     */
    public static boolean checkText(String text) {
        boolean check = false;
        if (text.contains("$")) {
            check = true;
        }
        return check;
    }

    /**
     * 替换图片
     *
     * @param document
     * @param picData
     * @throws Exception
     */

    public static void changePic(XWPFDocument document, Map<String, Object> picData) throws Exception {
        // 获取段落集合
        Iterator<XWPFParagraph> iterator = document.getParagraphsIterator();
        XWPFParagraph paragraph;
        while (iterator.hasNext()) {
            paragraph = iterator.next();
            // 判断此段落是否需要替换
            String text = paragraph.getText();
            if (checkText(text)) {
                replacePicValue(paragraph, picData);
            }
        }
    }

    /***
     * @Description :替换表格内的文字
     * @param document
     * @param data
     * @return void
     * @Date 2022/11/18 11:29
     */
    public static void changeTableText(XWPFDocument document, Map<String, Object> data) {
        // 获取文件的表格
        Iterator<XWPFTable> tableList = document.getTablesIterator();
        XWPFTable table;
        List<XWPFTableRow> rows;
        List<XWPFTableCell> cells;
        // 循环所有需要进行替换的文本，进行替换
        while (tableList.hasNext()) {
            table = tableList.next();
            if (checkText(table.getText())) {
                rows = table.getRows();
                // 遍历表格，并替换模板
                for (XWPFTableRow row : rows) {
                    cells = row.getTableCells();
                    for (XWPFTableCell cell : cells) {
                        // 判断单元格是否需要替换
                        if (checkText(cell.getText())) {
                            List<XWPFParagraph> paragraphs = cell.getParagraphs();
                            for (XWPFParagraph paragraph : paragraphs) {
                                replaceValue(paragraph, data);
                            }
                        }
                    }
                }
            }
        }
    }

    /***
     * @Description :替换表格内图片
     * @param document
     * @param picData
     * @return void
     * @Date 2022/11/18 11:29
     */
    public static void changeTablePic(XWPFDocument document, Map<String, Object> picData) throws Exception {
        // 获取文件的表格
        Iterator<XWPFTable> tableList = document.getTablesIterator();
        XWPFTable table;
        List<XWPFTableRow> rows;
        List<XWPFTableCell> cells;
        // 循环所有需要进行替换的文本，进行替换
        while (tableList.hasNext()) {
            table = tableList.next();
            if (checkText(table.getText())) {
                rows = table.getRows();
                // 遍历表格，并替换模板
                for (XWPFTableRow row : rows) {
                    cells = row.getTableCells();
                    for (XWPFTableCell cell : cells) {
                        // 判断单元格是否需要替换
                        if (checkText(cell.getText())) {
                            List<XWPFParagraph> paragraphs = cell.getParagraphs();
                            for (XWPFParagraph paragraph : paragraphs) {
                                replacePicValue(paragraph, picData);
                            }
                        }
                    }
                }
            }
        }
    }

    /***
     * @Description :替换内容
     * @param paragraph
     * @param textMap
     * @return void
     * @Date 2022/11/18 11:33
     */
    public static void replaceValue(XWPFParagraph paragraph, Map<String, Object> textMap) {
        XWPFRun run, nextRun;
        String runsText;
        List<XWPFRun> runs = paragraph.getRuns();
       try {
           for (int i = 0; i < runs.size(); i++) {
               run = runs.get(i);
               runsText = run.getText(0)+"";
               if (runsText.contains("${") || (runsText.contains("$") && runs.get(i + 1).getText(0).substring(0, 1).equals("{"))) {
                   while (!runsText.contains("}")) {
                       nextRun = runs.get(i + 1);
                       // 检查 nextRun 是否为 null
                       runsText = runsText + nextRun.getText(0);
                       //删除该节点下的数据
                       paragraph.removeRun(i + 1);
                   }
                   Pattern pattern = Pattern.compile("\\$\\{[^}]*\\}");
                   Matcher matcher = pattern.matcher(runsText);
                   String content = "";
                   while (matcher.find()) {
                       content = matcher.group(); // 获取匹配到的内容，包括${}
                   }
                   Object value = changeValue(content, textMap);
                   //判断key在Map中是否存在

                   if (textMap.containsKey(runsText)) {
                       run.setText(value.toString(), 0);
                   } else {
                       //如果匹配不到，则不修改
                       run.setText(runsText, 0);
                   }
               }
           }
       }catch (Exception e){
           //todo 空指针问题
           System.out.println(1);
           e.printStackTrace();
       }
    }

    /***
     * @Description :替换图片内容
     * @param paragraph
     * @param picData
     * @return void
     * @Date 2022/11/18 11:33
     */
    public static void replacePicValue(XWPFParagraph paragraph, Map<String, Object> picData) throws Exception {
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            Object value = changeValue(run.toString(), picData);
            if (picData.containsKey(run.toString())) {
                //清空内容
                run.setText("", 0);
                FileInputStream is = new FileInputStream((String) value);
                //图片宽度、高度
                int width = Units.toEMU(100), height = Units.toEMU(100);
                //添加图片信息，段落高度需要在模板中自行调整
                run.addPicture(is, XWPFDocument.PICTURE_TYPE_PNG, (String) value, width, height);
            }
        }
    }

    /***
     * @Description :匹配参数
     * @param value
     * @param textMap
     * @return java.lang.Object
     * @Date 2022/11/18 11:33
     */
    public static Object changeValue(String value, Map<String, Object> textMap) {
        Object valu = "";
        for (Map.Entry<String, Object> textSet : textMap.entrySet()) {
            // 匹配模板与替换值 格式${key}
            String key = textSet.getKey();
            if (value.contains(key)) {
                valu = textSet.getValue();
            }
        }
        return valu;
    }

    public static void main(String[] args) throws Exception {
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> picData = new HashMap<>();
        data.put("${project.projectName}", "123456789");
        data.put("${aac003}", "刘毅");
        data.put("${aac147}", "43102119001212109X");
        data.put("${aae004}", "刘毅");
        data.put("${aae005}", "13800000000");
        data.put("${aae013}", "");
        data.put("${aae123}", "刘毅");
        data.put("${aae123_1}", "");
        data.put("${aae123_2}", "");
        data.put("${aae124}", "刘小毅");
        data.put("${aae124_1}", "");
        data.put("${aae124_2}", "");
        picData.put("${aac013}", "C:\\Users\\<USER>\\Desktop\\屏幕截图 2024-09-20 094211.png");
        operateWord(data, picData);
    }
    public static Map<String, Object> convertEntityToMap(Object entity, String prefix) {
        Map<String, Object> map = new HashMap<>();
        Field[] fields = entity.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 使得私有属性也可以访问
            try {
                String key = prefix + "." + field.getName();
                Object value = field.get(entity);
                map.put("${"+key+"}", value);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return map;
    }
    public static void operateWord(Map<String, Object> data, Map<String, Object> picData) {
        try {
            FileInputStream is = new FileInputStream("C:\\Users\\<USER>\\Pictures\\shuiyin\\限额以下模板文件\\响应文件编制模板.docx");
            XWPFDocument document = new XWPFDocument(is);
            if (data!=null && data.size() > 0) {
                // 替换掉表格之外的文本(仅限文本)
                WordUtil.changeText(document, data);
                // 替换表格内的文本对象
                WordUtil.changeTableText(document, data);
            }
            if (picData!=null && picData.size() > 0) {
                // 替换内容图片
                WordUtil.changePic(document, picData);
                // 替换表格内的图片对象
                WordUtil.changeTablePic(document, picData);
            }
            FileOutputStream out = new FileOutputStream("C:\\Users\\<USER>\\Pictures\\shuiyin\\限额以下模板文件\\响应文件编制模板111.docx");
            document.write(out);
            is.close();
            out.close();
            document.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
