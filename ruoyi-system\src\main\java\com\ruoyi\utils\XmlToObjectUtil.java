package com.ruoyi.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;

public class XmlToObjectUtil {

    public static <T> T convertXmlToObject(String xml, Class<T> beanType){
        if(null!=xml&&!"".equals(xml)){
            try {
                return new XmlMapper().readValue(xml,beanType);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String convertObjectToXml(Object object){
        if(object!=null){
            try {
                XmlMapper xmlMapper = new XmlMapper();
                //设置xml格式携带版本和编码信息<?xml version='1.0' encoding='UTF-8'?>
                xmlMapper.enable(ToXmlGenerator.Feature.WRITE_XML_DECLARATION);
                //字段值为null，自动忽略，不再序列化
                xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                return xmlMapper.writeValueAsString(object);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
