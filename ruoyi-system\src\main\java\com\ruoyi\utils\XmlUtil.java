package com.ruoyi.utils;

import cn.hutool.json.XML;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.vo.XmlToEntityVo;
import freemarker.template.Template;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.lang.reflect.Array;
import java.util.List;
import java.util.Map;

public class XmlUtil {

    public static void main(String[] args) throws Exception {
        String xmlPath = "C:\\Users\\<USER>\\Desktop\\projectInfo.xml";
//        String xmlUrl = "http://localhost:8089/profile/upload/2024/07/08/bidInfo.xml";
//        ZipUtil.createZip(zipPath, filePath1, filePath2);
//        ZipUtil.encrypt(zipPath, encodeFile);
//        XmlUtil.createXml(xmlPath);
//        JSONObject xmlJson = XmlUtil.getXml2Json(xmlPath);
//        JSONObject xmlJson = new JSONObject();
//        xmlJson.put("projectInfo", JSONObject.parseObject("{\"name\":\"xxx项目\",\"code\":\"xxx\",\"a\":{\"name\":\"唱标项1\",\"value\":\"唱标内容1\"}}"));
//        xmlJson.put("bidAnnounces", JSONArray.parseArray("[{\"name\":\"唱标项1\",\"value\":\"唱标内容1\"},{\"name\":\"唱标项2\",\"value\":\"唱标内容2\"},{\"name\":\"唱标项3\",\"value\":\"唱标内容3\"}]"));
//        json2XmlFile(xmlPath, xmlJson);
//        System.out.println(xmlJson);
        System.out.println(XmlUtil.getXml2String(xmlPath));
        XmlToEntityVo xmlToEntityVo = XmlToObjectUtil.convertXmlToObject(XmlUtil.getXml2String(xmlPath), XmlToEntityVo.class);
        System.out.println(xmlToEntityVo);
    }


    /**
     * json生成xml
     *
     * @param xmlPath
     * @param jsonObject
     * @throws Exception
     */
    public static void json2XmlFile(String xmlPath, JSONObject jsonObject) throws Exception {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        Document doc = dBuilder.newDocument();
        // 创建根元素
        Element rootElement = doc.createElement("root");
        doc.appendChild(rootElement);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Element element = doc.createElement(entry.getKey());
            extracted(jsonObject, doc, entry, element);
            rootElement.appendChild(element);
        }
        // 将DOM内容写入文件
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        DOMSource source = new DOMSource(doc);
        StreamResult result = new StreamResult(new File(xmlPath));
        transformer.transform(source, result);
        System.out.println("XML Generated Successfully");
    }

    private static void extracted(JSONObject jsonObject, Document doc, Map.Entry<String, Object> entry, Element element) {
        if (entry.getValue() instanceof Map) {
            JSONObject jsonObject1 = jsonObject.getJSONObject(entry.getKey());
            for (Map.Entry<String, Object> entry1 : jsonObject1.entrySet()) {
                element.setAttribute(entry1.getKey(), jsonObject1.get(entry1.getKey()) + "");
            }
        } else if (entry.getValue() instanceof List) {
            JSONArray jsonArray = jsonObject.getJSONArray(entry.getKey());
            for (int i = 0; i < jsonArray.size(); i++) {
                Element element1 = doc.createElement(generateObjectKey(entry.getKey()));
                JSONObject jsonObject1 = jsonArray.getJSONObject(i);
                for (Map.Entry<String, Object> entry1 : jsonObject1.entrySet()) {
                    element1.setAttribute(entry1.getKey(), jsonObject1.get(entry1.getKey()) + "");
                }
                element.appendChild(element1);
            }
        } else {
            element.setAttribute(entry.getKey(), jsonObject.get(entry.getKey()) + "");
        }
    }


    private static String generateObjectKey(String keyStr) {
        String key = keyStr.toLowerCase();
        if (key.endsWith("es")) {
            // 如果键以 "es" 结尾，可能需要移除 "es" 来生成对象键
            return keyStr.substring(0, key.length() - 2);
        } else if (key.endsWith("s")) {
            // 如果键以 "s" 结尾，可能需要移除 "s" 来生成对象键
            return keyStr.substring(0, key.length() - 1);
        } else if (key.endsWith("list")) {
            // 如果键以 "list" 结尾，可能需要移除 "list" 来生成对象键
            return keyStr.substring(0, key.length() - 4);
        } else {
            // 如果键不以这些结尾，直接返回键本身
            return keyStr;
        }
    }


    /**
     * 生成xml文件
     *
     * @param xmlPath
     * @throws Exception
     */
    public static void createXml(String xmlPath) throws Exception {
        try {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.newDocument();

            // 创建根元素
            Element rootElement = doc.createElement("root");
            doc.appendChild(rootElement);

            // 项目内容
            Element projectInfo = doc.createElement("projectInfo");
            rootElement.appendChild(projectInfo);
            Element projectName = doc.createElement("name");
            projectName.appendChild(doc.createTextNode("xxx项目"));
            projectInfo.appendChild(projectName);
            Element projectCode = doc.createElement("code");
            projectCode.appendChild(doc.createTextNode("xxx"));
            projectInfo.appendChild(projectCode);

            // 唱标项
            Element bidAnnounces = doc.createElement("bidAnnounces");
            rootElement.appendChild(bidAnnounces);
            for (int i = 1; i < 4; i++) {
                Element bidAnnounce = doc.createElement("bidAnnounce");
                bidAnnounce.setAttribute("id", i + "");

                Element name = doc.createElement("name");
                name.appendChild(doc.createTextNode("唱标项" + i));
                bidAnnounce.appendChild(name);
                Element value = doc.createElement("value");
                value.appendChild(doc.createTextNode("唱标内容" + i));
                bidAnnounce.appendChild(value);
                bidAnnounces.appendChild(bidAnnounce);
            }
            // 将DOM内容写入文件
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            DOMSource source = new DOMSource(doc);
            StreamResult result = new StreamResult(new java.io.File(xmlPath));
            // 输出到文件
            transformer.transform(source, result);
            System.out.println("XML Generated Successfully");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void createXml(String ftlPath, JSONObject params, String xmlPath, FreeMarkerConfigurer configurer) throws Exception {
        try {
            // 将DOM内容写入文件
            Template template = configurer.getConfiguration().getTemplate(ftlPath);
            try (Writer writer = new OutputStreamWriter(new FileOutputStream(xmlPath), "UTF-8")) {
                template.process(params, writer);
                System.out.println("XML Generated Successfully");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取唱标xml内容
     *
     * @param xmlPath
     * @return
     */
    public static JSONObject getXml2Json(String xmlPath) {
        JSONObject info = new JSONObject();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
//            URL url = new URL(xmlPath);

//            Document document = builder.parse(url.openConnection().getInputStream());
            Document document = builder.parse(xmlPath);
            // 创建TransformerFactory
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            // 通过TransformerFactory创建Transformer
            Transformer transformer = transformerFactory.newTransformer();

            // 使用StringWriter获取转换后的字符串
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));

            // 输出转换后的字符串
            String xmlString = writer.toString();
            return JSONObject.parseObject(XML.toJSONObject(xmlString).toJSONString(2));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return info;
    }

    /**
     * 获取唱标xml内容
     *
     * @param xmlPath
     * @return
     */
    public static String getXml2String(String xmlPath) {
        String str = "";

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
//            URL url = new URL(xmlPath);
            System.out.println(xmlPath);
//            Document document = builder.parse(url.openConnection().getInputStream());
            Document document = builder.parse(xmlPath);
            // 创建TransformerFactory
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            // 通过TransformerFactory创建Transformer
            Transformer transformer = transformerFactory.newTransformer();

            // 使用StringWriter获取转换后的字符串
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));

            // 输出转换后的字符串
            str = writer.toString();
            return str;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }
}
