package com.ruoyi.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

@Component
public class ZcxhUtil {

    @Value("${extractcode.baseUrl}")
    private String baseUrl;
    @Value("${extractcode.url}")
    private String tokenUrl;
    @Value("${extractcode.username}")
    private String username;
    @Value("${extractcode.thirdPartySecret}")
    private String thirdPartySecret;
    @Value("${extractcode.password}")
    private String password;

    public JSONObject send2Zcxh(JSONObject params, String url) throws Exception{
        if(params.getString("noticeTitle").contains("【测试】")){
            return new JSONObject();
        }
        JSONObject jo = new JSONObject();
        jo.put("appId", "4c161294d629b6d79d861205d3716c06");
        jo.put("timestamp", new Date().getTime());
        jo.put("data", params.toJSONString());
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        System.out.println("---------------send2Zcxh-------------");
//        String baseUrl = "http://127.0.0.1:9999/zcxhapi";
        System.out.println(baseUrl+url);
        System.out.println(jo.toJSONString());

        RequestBody body = RequestBody.create(MediaType.parse("application/json"), jo.toJSONString());
        Request request = new Request.Builder()
                .url(baseUrl+url)
                .method("POST", body)
                .addHeader("token", getToken())
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", "JSESSIONID=071327329DCF80B72EA2FFFDB9396A46")
                .build();
        try {
            Response response = client.newCall(request).execute();
            return JSON.parseObject(response.body().string());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getToken() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\"t\":"
                + System.currentTimeMillis() + ",\r\n\"username\":\""
                + username + "\",\r\n\"password\":\""
                + password + "\",\r\n\"thirdPartySecret\":\""
                + thirdPartySecret + "\"\r\n}");
        Request request = new Request.Builder()
                .url(tokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.body() != null) {
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            return jsonObject.getString("token");
        } else {
            throw new RuntimeException("请求专家抽取token 失败");
        }
    }



    public static void main(String[] args) {
        JSONObject params = new JSONObject();
        params.put("a", "a");
        params.put("b", "b");
        params.put("c", "c");
        JSONObject jo = new JSONObject();
        jo.put("appId", "4c161294d629b6d79d861205d3716c06");
        jo.put("timestamp", new Date().getTime());
        jo.put("data", params.toJSONString());
        System.out.println(jo);
        System.out.println(jo.toJSONString());
        System.out.println(jo.toString());
    }
}
