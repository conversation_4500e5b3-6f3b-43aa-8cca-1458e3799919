package com.ruoyi.utils;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.Seq;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.Key;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {

    public static void main(String[] args) throws Exception {
        String zipPath = "D:/tmp/111.zip";
        String filePath1 = "D:/tmp/111.pdf";
        String filePath2 = "D:/tmp/projectInfo.xml";
        String filePath3 = "D:/tmp/bidInfo.xml";
        String encodeFile = "D:/tmp/cgr1.zbwj";
//        String bidEncodeFile = "D:/tmp/tbr1.tbwj";
        String bidEncodeFile = "D:\\ruoyi\\uploadPath\\upload\\2024\\09\\05\\1173866061456389\\responseProject.tbwj";
//        String decodeFile = "D:/tmp/222.zip";
        String decodeFile = "D:\\ruoyi\\uploadPath\\upload\\2024\\09\\05\\1173866061456389\\responseProject.zip";
        String encodeUrl = "http://localhost:8089/profile/upload/2024/06/26/gys1.tbwj";
//        ZipUtil.createZip(zipPath, filePath1, filePath3);
//        ZipUtil.encrypt(zipPath, bidEncodeFile, "e10adc3949ba59ab");
        ZipUtil.decrypt(bidEncodeFile, decodeFile, "e10adc3949ba59ab");
//        ZipUtil.decryptUrl(encodeUrl, null);

    }

    public static void createZip(String zipPath, String... filePaths) throws Exception {
        FileOutputStream fos = new FileOutputStream(zipPath);
        ZipOutputStream zos = new ZipOutputStream(fos);
        for (String filePath : filePaths) {
            File file = new File(filePath);
            if (file.isFile()) {
                FileInputStream fis = new FileInputStream(file);
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }

                fis.close();
            }
        }
        zos.closeEntry();
        zos.close();
        fos.close();
    }

    public static String unzip(String zipFilePath) throws Exception {
        String destDir = zipFilePath.substring(0, zipFilePath.length() - 4);
        File dest = new File(destDir);
        if (!dest.exists()) {
            dest.mkdir();
        }
        ZipFile zipFile = new ZipFile(zipFilePath);
        Enumeration<ZipArchiveEntry> entrys = zipFile.getEntries();
        while (entrys.hasMoreElements()) {
            ZipArchiveEntry entry = entrys.nextElement();
            if (entry.isDirectory()) {
                File dir = new File(destDir + File.separator + entry.getName());
                dir.mkdirs();
            } else {
                InputStream inputStream = zipFile.getInputStream(entry);
                FileOutputStream outputStream = new FileOutputStream(destDir + File.separator + entry.getName());
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
                outputStream.close();
                inputStream.close();
            }
        }
        zipFile.close();
        return destDir;
    }

    private static final String ALGORITHM = "AES";
    private static final String KEY_STRING = "1234567812345678"; // 密钥应该是16个字符长度

    public static void encrypt(String inputFile, String outputFile, String encodeKey) throws Exception {
        Key key = new SecretKeySpec(StringUtils.isNoneBlank(encodeKey)?encodeKey.getBytes():KEY_STRING.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);

        byte[] inputBytes = Files.readAllBytes(Paths.get(inputFile));
        byte[] outputBytes = cipher.doFinal(inputBytes);

        Files.write(Paths.get(outputFile), outputBytes);
    }

    public static String decrypt(String inputFile, String outputFile, String encodeKey) throws Exception {
        Key key = new SecretKeySpec(StringUtils.isNoneBlank(encodeKey)?encodeKey.getBytes():KEY_STRING.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);

        byte[] inputBytes = Files.readAllBytes(Paths.get(inputFile));
        byte[] outputBytes = cipher.doFinal(inputBytes);

        Files.write(Paths.get(outputFile), outputBytes);
        return outputFile;
    }
    public static String decryptUrl(String url, String encodeKey) throws Exception {
        int si = url.lastIndexOf("/upload");
        String urlPath = "";
        if(si!=-1){
            urlPath = url.substring(si);
        }
        String filePath = RuoYiConfig.getProfile();
        String realPath = filePath + urlPath;
        System.out.println(realPath);
        String outputFile = realPath.replace("tbwj", "zip");
        System.out.println(outputFile);



        Key key = new SecretKeySpec(StringUtils.isNoneBlank(encodeKey)?encodeKey.getBytes():KEY_STRING.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] inputBytes = Files.readAllBytes(Paths.get(realPath));
        byte[] outputBytes = cipher.doFinal(inputBytes);
        Files.write(Paths.get(outputFile), outputBytes);


        return outputFile;
    }



    public static String decryptUrlAndUnzip(String url, String encodeKey){
        try {
            String zipPath = decryptUrl(url, encodeKey);
            String unzip = unzip(zipPath);
            int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
            String currentDir = StringUtils.substring(unzip, dirLastIndex);
            currentDir = Constants.RESOURCE_PREFIX + "/" + currentDir + "/responseBidTxt.pdf";
            return currentDir;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
