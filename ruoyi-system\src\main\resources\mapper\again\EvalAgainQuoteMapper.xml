<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.eval.mapper.EvalAgainQuoteMapper">

    <resultMap type="EvalAgainQuote" id="EvalAgainQuoteResult">
        <result property="againQuoteId"    column="again_quote_id"    />
        <result property="projectEvaluationId"    column="project_evaluation_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="quoteNumber"    column="quote_number"    />
        <result property="quoteAmount"    column="quote_amount"    />
        <result property="quoteFile"    column="quote_file"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />

    </resultMap>

    <select id="getAmountByEntAndProjectEvaluationInfo" >
        SELECT * FROM `eval_again_quote`
        WHERE project_evaluation_id=#{projectEvaluationId}
        AND ent_id=#{entId}
        AND quote_number = (SELECT MAX(quote_number) FROM `eval_again_quote` WHERE project_evaluation_id=#{projectEvaluationId})
    </select>
</mapper>
