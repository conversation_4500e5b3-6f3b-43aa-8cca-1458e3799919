<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiAttachmentMapper">
    
    <resultMap type="BusiAttachment" id="BusiAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="busiId"    column="busi_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileMd5"    column="file_md5"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
    
    <select id="selectByBusiIdAndType" parameterType="java.lang.Long">
        select * from busi_attachment where busi_id=#{busiId}
        <if test="fileType!=null and fileType!=''">
            AND file_type=#{fileType}
        </if>
        AND del_flag=1
    </select>

    <delete id="deleteByBusiId" parameterType="java.lang.Long">
        delete from  busi_attachment where busi_id=#{busiId}
    </delete>

    <delete id="deleteByBusiIdAndType" >
        delete from  busi_attachment where busi_id=#{busiId} AND file_type=#{fileType}
    </delete>
</mapper>