<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiAuditProcessMapper">
    
    <resultMap type="BusiAuditProcess" id="BusiAuditProcessResult">
        <result property="auditProcessId"    column="audit_process_id"    />
        <result property="busiId"    column="busi_id"    />
        <result property="auditResult"    column="audit_result"    />
        <result property="auditResultName"    column="audit_result_name"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="busiState"    column="busi_state"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="operator"    column="operator"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="nextOperator"    column="next_operator"    />
    </resultMap>

</mapper>