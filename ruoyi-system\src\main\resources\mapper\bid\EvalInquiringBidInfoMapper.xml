<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.eval.mapper.EvalInquiringBidInfoMapper">
    
    <resultMap type="EvalInquiringBidInfo" id="EvalInquiringBidInfoResult">
        <result property="inquiringBidId"    column="inquiring_bid_id"    />
        <result property="projectEvaluationId"    column="project_evaluation_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="expertResultId"    column="expert_result_id"    />
        <result property="inquiringContent"    column="inquiring_content"    />
        <result property="inquiringTime"    column="inquiring_time"    />
        <result property="replyContent"    column="reply_content"    />
        <result property="replyTime"    column="reply_time"    />
        <result property="replyFile"    column="reply_file"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>
</mapper>