<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiBiddingRecordMapper">
    
    <resultMap type="BusiBiddingRecord" id="BusiBiddingRecordResult">
        <result property="biddingId"    column="bidding_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="bidderId"    column="bidder_id"    />
        <result property="bidderName"    column="bidder_name"    />
        <result property="bidderCode"    column="bidder_code"    />
        <result property="uploadIp"    column="upload_ip"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="noticeVersion"    column="notice_version"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <select id="selectByProject">
        select * from busi_bidding_record where project_id = #{projectId} and del_flag=0
    </select>
</mapper>