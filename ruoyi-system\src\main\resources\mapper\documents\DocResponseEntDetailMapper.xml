<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.procurement.mapper.DocResponseEntDetailMapper">
    
    <resultMap type="DocResponseEntDetail" id="DocResponseEntDetailResult">
        <result property="id"    column="id"    />
        <result property="docResponseItemId"    column="doc_response_item_id"    />
        <result property="docResponseEntId"    column="doc_response_ent_id"    />
        <result property="detailCode"    column="detail_code"    />
        <result property="detailName"    column="detail_name"    />
        <result property="detailType"    column="detail_type"    />
        <result property="scoringItemId"    column="scoring_item_id"    />
        <result property="detailContent"    column="detail_content"    />
        <result property="detailSort"    column="detail_sort"    />
        <result property="filePath"    column="file_path"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>


</mapper>