<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.procurement.mapper.DocResponseEntInfoMapper">
    
    <resultMap type="DocResponseEntInfo" id="DocResponseEntInfoResult">
        <result property="id"    column="id"    />
        <result property="docResponseId"    column="doc_response_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="pdfPath"    column="pdf_path"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <select id="entList" resultType="com.ruoyi.procurement.domain.DocResponseEntInfo">
        SELECT * FROM
            (SELECT btdd.bidder_id AS ent_id, btdd.project_id, drei.id , drei.file_path, btn.bid_opening_time, btp.project_name, btp.project_code
            FROM busi_tender_documents_download btdd
            LEFT JOIN doc_response_ent_info drei ON btdd.bidder_id=drei.ent_id AND btdd.project_id=drei.project_id AND drei.del_flag=0
            LEFT JOIN busi_tender_notice btn ON btdd.project_id=btn.project_id AND btn.del_flag=0
            LEFT JOIN busi_tender_project btp ON btdd.project_id=btp.project_id AND btp.del_flag=0
            WHERE bidder_id=#{info.entId}
                <if test="info.projectName != null and info.projectName != ''">
                    AND project_name like concat('%', #{info.projectName}, '%')
                </if>
                <if test="info.startTime != null">
                    AND bid_opening_time &gt;= #{info.startTime}
                </if>
                <if test="info.endTime != null">
                    AND bid_opening_time &lt;= #{info.endTime}
                </if>
              AND btdd.del_flag=0)
            ddd /* WHERE (file_path IS NOT NULL OR bid_opening_time > NOW())*/
            ORDER BY bid_opening_time DESC
    </select>
</mapper>