<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.procurement.mapper.DocResponseItemMapper">
    
    <resultMap type="DocResponseItem" id="DocResponseItemResult">
        <result property="id"    column="id"    />
        <result property="pId"    column="p_id"    />
        <result property="docResponseId"    column="doc_response_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemType"    column="item_type"    />
        <result property="itemContent"    column="item_content"    />
        <result property="itemSort"    column="item_sort"    />
        <result property="itemLevel"    column="item_level"    />
        <result property="isLeaf"    column="is_leaf"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>
</mapper>