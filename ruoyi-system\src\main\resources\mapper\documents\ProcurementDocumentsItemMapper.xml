<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.procurement.mapper.ProcurementDocumentsItemMapper">
    
    <resultMap type="ProcurementDocumentsItem" id="ProcurementDocumentsItemResult">
        <result property="projectFileItemId"    column="project_file_item_id"    />
        <result property="projectFileId"    column="project_file_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemContent"    column="item_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <select id="selectItemByInfoAndCode" resultType="com.ruoyi.procurement.domain.ProcurementDocumentsItem">
        select item.* from procurement_documents_info info
             join procurement_documents_item item on info.project_file_id=item.project_file_id
        where info.tender_mode=#{tenderMode} and info.project_type=#{projectType}
          and item.item_code=#{code}
    </select>
</mapper>