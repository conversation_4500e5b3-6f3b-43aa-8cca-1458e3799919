<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.procurement.mapper.ProcurementDocumentsUitemMapper">
    
    <resultMap type="ProcurementDocumentsUitem" id="ProcurementDocumentsUitemResult">
        <result property="entFileItemId"    column="ent_file_item_id"    />
        <result property="entFileId"    column="ent_file_id"    />
        <result property="projectFileId"    column="project_file_id"    />
        <result property="projectFileItemId" column="project_file_item_id"  />
        <result property="entId"    column="ent_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="itemName" column="item_name"/>
        <result property="itemContent"    column="item_content" />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <select id="selectByProject" resultType="com.ruoyi.procurement.domain.ProcurementDocumentsUitem">
        select uitem.* from procurement_documents_uinfo uinfo
            join procurement_documents_uitem uitem on uinfo.ent_file_id=uitem.ent_file_id
        where uinfo.project_id=#{projectId} and uitem.project_file_item_id=#{projectFileItemId}
    </select>

    <select id="selectByProjectId" resultType="com.ruoyi.procurement.domain.ProcurementDocumentsUitem">
        select uitem.* from procurement_documents_uinfo uinfo
            inner join procurement_documents_uitem uitem on uinfo.ent_file_id=uitem.ent_file_id
        where uinfo.project_id = #{projectId}
    </select>
</mapper>