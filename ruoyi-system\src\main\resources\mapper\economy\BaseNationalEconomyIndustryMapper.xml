<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseNationalEconomyIndustryMapper">
    
    <resultMap type="BaseNationalEconomyIndustry" id="BaseNationalEconomyIndustryResult">
        <result property="industrCode"    column="industr_code"    />
        <result property="industrLevel"    column="industr_level"    />
        <result property="industryName"    column="industry_name"    />
        <result property="industrySort"    column="Industry_sort"    />
        <result property="validFlag"    column="valid_flag"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectBaseNationalEconomyIndustryVo">
        select industr_code, industr_level, industry_name, Industry_sort, valid_flag, del_flag, create_time, create_by, update_time, update_by from base_national_economy_industry
    </sql>

    <select id="selectBaseNationalEconomyIndustryList" parameterType="BaseNationalEconomyIndustry" resultMap="BaseNationalEconomyIndustryResult">
        <include refid="selectBaseNationalEconomyIndustryVo"/>
        <where>
            <if test="industrCode != null  and industrCode != ''"> and industr_code = #{industrCode}</if>
            <if test="industrLevel != null "> and industr_level = #{industrLevel}</if>
            <if test="industryName != null  and industryName != ''"> and industry_name like concat('%', #{industryName}, '%')</if>
            <if test="industrySort != null "> and Industry_sort = #{industrySort}</if>
            <if test="validFlag != null "> and valid_flag = #{validFlag}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
    </select>
    
    <select id="selectBaseNationalEconomyIndustryByIndustrCode" parameterType="String" resultMap="BaseNationalEconomyIndustryResult">
        <include refid="selectBaseNationalEconomyIndustryVo"/>
        where industr_code = #{industrCode}
    </select>
        
    <insert id="insertBaseNationalEconomyIndustry" parameterType="BaseNationalEconomyIndustry">
        insert into base_national_economy_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industrCode != null and industrCode != ''">industr_code,</if>
            <if test="industrLevel != null">industr_level,</if>
            <if test="industryName != null and industryName != ''">industry_name,</if>
            <if test="industrySort != null">Industry_sort,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industrCode != null and industrCode != ''">#{industrCode},</if>
            <if test="industrLevel != null">#{industrLevel},</if>
            <if test="industryName != null and industryName != ''">#{industryName},</if>
            <if test="industrySort != null">#{industrySort},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBaseNationalEconomyIndustry" parameterType="BaseNationalEconomyIndustry">
        update base_national_economy_industry
        <trim prefix="SET" suffixOverrides=",">
            <if test="industrLevel != null">industr_level = #{industrLevel},</if>
            <if test="industryName != null and industryName != ''">industry_name = #{industryName},</if>
            <if test="industrySort != null">Industry_sort = #{industrySort},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where industr_code = #{industrCode}
    </update>

    <delete id="deleteBaseNationalEconomyIndustryByIndustrCode" parameterType="String">
--         delete from base_national_economy_industry where industr_code = #{industrCode}
update  base_national_economy_industry set del_flag=1 where industr_code = #{industrCode}
    </delete>

    <delete id="deleteBaseNationalEconomyIndustryByIndustrCodes" parameterType="String">
--         delete from base_national_economy_industry where industr_code in
        update  base_national_economy_industry set del_flag=1 where industr_code in
        <foreach item="industrCode" collection="array" open="(" separator="," close=")">
            #{industrCode}
        </foreach>
    </delete>
</mapper>