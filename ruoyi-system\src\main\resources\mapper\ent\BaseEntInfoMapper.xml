<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseEntInfoMapper">

    <resultMap type="BaseEntInfo" id="BaseEntInfoResult">
        <result property="entId"    column="ent_id"    />
        <result property="secretKey"    column="secret_key"    />
        <result property="entName"    column="ent_name"    />
        <result property="entCode"    column="ent_code"    />
        <result property="entNature"    column="ent_nature"    />
        <result property="entLegalPersonCardFile" column="ent_legal_person_card_file" />
        <result property="entAddress"    column="ent_address"    />
        <result property="entKeyAgencyAreas"    column="ent_key_agency_areas"    />
        <result property="entOpeningBank"    column="ent_opening_bank"    />
        <result property="entBankCode"    column="ent_bank_code"    />
        <result property="entWebsite"    column="ent_website"    />
        <result property="entIntro"    column="ent_intro"    />
        <result property="entLinkman"    column="ent_linkman"    />
        <result property="entContactPhone"    column="ent_contact_phone"    />
        <result property="entLegalPerson"    column="ent_legal_person"    />
        <result property="entLegalPersonPhone"    column="ent_legal_person_phone"    />
        <result property="entStatus"    column="ent_status"    />
        <result property="entLogo"    column="ent_logo"    />
        <result property="entType"    column="ent_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="busiState"    column="busi_state"    />
        <result property="businessLicense"    column="business_license"    />
        <result property="zipCode"    column="zip_code"    />

    </resultMap>
</mapper>
