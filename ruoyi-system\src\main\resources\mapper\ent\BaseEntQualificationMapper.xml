<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseEntQualificationMapper">
    
    <resultMap type="BaseEntQualification" id="BaseEntQualificationResult">
        <result property="qualificationId"    column="qualification_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="qualificationType"    column="qualification_type"    />
        <result property="qualificationFile"    column="qualification_file"    />
        <result property="qualificationStartDate"    column="qualification_start_date"    />
        <result property="qualificationEndDate"    column="qualification_end_date"    />
        <result property="isLongTerm"    column="is_long_term"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
</mapper>