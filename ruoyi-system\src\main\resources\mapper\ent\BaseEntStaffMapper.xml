<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseEntStaffMapper">
    
    <resultMap type="BaseEntStaff" id="BaseEntStaffResult">
        <result property="staffId"    column="staff_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="staffName"    column="staff_name"    />
        <result property="staffCode"    column="staff_code"    />
        <result property="staffSex"    column="staff_sex"    />
        <result property="staffPhone"    column="staff_phone"    />
        <result property="staffDegree"    column="staff_degree"    />
        <result property="staffTitles"    column="staff_titles"    />
        <result property="staffSpeciality"    column="staff_speciality"    />
        <result property="staffCertificate"    column="staff_certificate"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
</mapper>