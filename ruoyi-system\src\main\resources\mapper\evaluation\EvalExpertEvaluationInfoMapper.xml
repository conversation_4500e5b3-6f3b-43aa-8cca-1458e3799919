<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.eval.mapper.EvalExpertEvaluationInfoMapper">
    
    <resultMap type="EvalExpertEvaluationInfo" id="EvalExpertEvaluationInfoResult">
        <result property="evalExpertEvaluationInfoId"    column="eval_expert_evaluation_info_id"    />
        <result property="projectEvaluationId"    column="project_evaluation_id"    />
        <result property="expertResultId"    column="expert_result_id"    />
        <result property="evalNode"    column="eval_node"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="getInfo">

    </sql>
</mapper>