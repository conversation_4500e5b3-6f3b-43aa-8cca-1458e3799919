<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.eval.mapper.EvalProjectEvaluationInfoMapper">
    
    <resultMap type="EvalProjectEvaluationInfo" id="EvalProjectEvaluationInfoResult">
        <result property="projectEvaluationId"    column="project_evaluation_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="evaluationTime"    column="evaluation_time"    />
        <result property="evaluationEndTime"    column="evaluation_end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>
</mapper>