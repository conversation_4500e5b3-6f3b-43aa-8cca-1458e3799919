<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.eval.mapper.EvalProjectEvaluationProcessMapper">

    <resultMap type="EvalProjectEvaluationProcess" id="EvalProjectEvaluationProcessResult">
        <result property="evaluationProcessId"    column="evaluation_process_id"    />
        <result property="projectEvaluationId"    column="project_evaluation_id"    />
        <result property="scoringMethodItemId"    column="scoring_method_item_id"    />
        <result property="evaluationState"    column="evaluation_state"    />
        <result property="evaluationResult"    column="evaluation_result"    />
        <result property="evaluationResultRemark"    column="evaluation_result_remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="startTime"    column="start_time"    />
        <result property="minutes"    column="minutes"    />
    </resultMap>
</mapper>
