<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiExtractExpertEvadeMapper">
    
    <resultMap type="BusiExtractExpertEvade" id="BusiExtractExpertEvadeResult">
        <result property="evadeId"    column="evade_id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="evadeName"    column="evade_name"    />
        <result property="evadeType"    column="evade_type"    />
        <result property="evadeReason" column="evade_reason" />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
</mapper>