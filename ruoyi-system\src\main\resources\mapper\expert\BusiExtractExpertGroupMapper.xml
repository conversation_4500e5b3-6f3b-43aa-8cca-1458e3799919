<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiExtractExpertGroupMapper">
    
    <resultMap type="BusiExtractExpertGroup" id="BusiExtractExpertGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="expertNumber"    column="expert_number"    />
        <result property="expertClassificationCode"    column="expert_classification_code"    />
        <result property="expertType"    column="expert_type"    />
        <result property="groupAddress"    column="group_address"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
</mapper>