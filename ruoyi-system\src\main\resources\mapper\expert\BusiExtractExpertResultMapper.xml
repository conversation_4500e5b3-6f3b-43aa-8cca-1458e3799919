<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiExtractExpertResultMapper">

    <resultMap type="BusiExtractExpertResult" id="BusiExtractExpertResultResult">
        <result property="resultId"    column="result_id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="expertId"    column="expert_id"    />
        <result property="expertName"    column="expert_name"    />
        <result property="expertCode"    column="expert_code"    />
        <result property="expertAppraise"    column="expert_appraise"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="groupId"    column="group_id"    />
        <result property="thirtyId"    column="thirty_id"    />
        <result property="company"    column="company"    />
        <result property="phone"    column="phone"    />
        <result property="isAvoid"    column="is_avoid"    />
        <result property="expertLeader"    column="expert_leader"    />
        <result property="isOwner"    column="is_owner"    />
        <result property="signTime"    column="sign_time"    />
        <result property="signStatus"    column="sign_status"    />

    </resultMap>
</mapper>
