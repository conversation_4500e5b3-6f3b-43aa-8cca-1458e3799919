<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.scoring.mapper.ScoringMethodInfoMapper">
    
    <resultMap type="ScoringMethodInfo" id="ScoringMethodInfoResult">
        <result property="scoringMethodId"    column="scoring_method_id"    />
        <result property="methodName"    column="method_name"    />
        <result property="methodCode"    column="method_code"    />
        <result property="methodFile"    column="method_file"    />
        <result property="tenderMode"    column="tender_mode"    />
        <result property="projectType"    column="project_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <association property="uInfo" javaType="ScoringMethodUinfo">
            <id     property="entMethodId"      column="ent_method_id"      />
            <result property="entId"     column="ent_id"     />
        </association>
    </resultMap>

    <select id="selectByProjectId" parameterType="Long" resultMap="ScoringMethodInfoResult">
        select info.*, uinfo.ent_method_id, uinfo.ent_id from scoring_method_uinfo uinfo
        join scoring_method_info info on uinfo.scoring_method_id=info.scoring_method_id
        where uinfo.project_id=#{projectId}
    </select>
</mapper>