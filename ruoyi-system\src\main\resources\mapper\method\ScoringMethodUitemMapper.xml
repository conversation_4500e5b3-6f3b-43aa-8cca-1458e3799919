<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.scoring.mapper.ScoringMethodUitemMapper">
    
    <resultMap type="ScoringMethodUitem" id="ScoringMethodUitemResult">
        <result property="entMethodItemId"    column="ent_method_item_id"    />
        <result property="entMethodId"    column="ent_method_id"    />
        <result property="scoringMethodItemId"    column="scoring_method_item_id"    />
        <result property="entId"    column="ent_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemRemark"    column="item_remark"    />
        <result property="minScore"    column="min_score"    />
        <result property="maxScore"    column="max_score"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <select id="selectBusinessUitemIds" parameterType="com.ruoyi.scoring.domain.ScoringMethodUinfo" resultMap="ScoringMethodUitemResult">
        SELECT
            uitem.*
        FROM
            scoring_method_item item
                JOIN scoring_method_uitem uitem ON uitem.scoring_method_item_id = item.scoring_method_item_id
        where
            uitem.del_flag=0
          and item.item_code=#{itemCode}
          and item.scoring_method_id=#{scoringMethodId}
          and uitem.ent_method_id=#{entMethodId}

    </select>
</mapper>