<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseExpertProfessionClassificationMapper">
    
    <resultMap type="BaseExpertProfessionClassification" id="BaseExpertProfessionClassificationResult">
        <result property="classificationCode"    column="classification_code"    />
        <result property="classificationLevel"    column="classification_level"    />
        <result property="classificationName"    column="classification_name"    />
        <result property="classificationSort"    column="classification_sort"    />
        <result property="validFlag"    column="valid_flag"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectBaseExpertProfessionClassificationVo">
        select classification_code, classification_level, classification_name, classification_sort, valid_flag, del_flag, create_time, create_by, update_time, update_by from base_expert_profession_classification
    </sql>

    <select id="selectBaseExpertProfessionClassificationList" parameterType="BaseExpertProfessionClassification" resultMap="BaseExpertProfessionClassificationResult">
        <include refid="selectBaseExpertProfessionClassificationVo"/>
        <where>
            <if test="classificationCode != null  and classificationCode != ''"> and classification_code = #{classificationCode}</if>
            <if test="classificationLevel != null "> and classification_level = #{classificationLevel}</if>
            <if test="classificationName != null  and classificationName != ''"> and classification_name like concat('%', #{classificationName}, '%')</if>
            <if test="classificationSort != null "> and classification_sort = #{classificationSort}</if>
            <if test="validFlag != null "> and valid_flag = #{validFlag}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
    </select>
    
    <select id="selectBaseExpertProfessionClassificationByClassificationCode" parameterType="String" resultMap="BaseExpertProfessionClassificationResult">
        <include refid="selectBaseExpertProfessionClassificationVo"/>
        where classification_code = #{classificationCode}
    </select>
        
    <insert id="insertBaseExpertProfessionClassification" parameterType="BaseExpertProfessionClassification">
        insert into base_expert_profession_classification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classificationCode != null and classificationCode != ''">classification_code,</if>
            <if test="classificationLevel != null">classification_level,</if>
            <if test="classificationName != null and classificationName != ''">classification_name,</if>
            <if test="classificationSort != null">classification_sort,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classificationCode != null and classificationCode != ''">#{classificationCode},</if>
            <if test="classificationLevel != null">#{classificationLevel},</if>
            <if test="classificationName != null and classificationName != ''">#{classificationName},</if>
            <if test="classificationSort != null">#{classificationSort},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBaseExpertProfessionClassification" parameterType="BaseExpertProfessionClassification">
        update base_expert_profession_classification
        <trim prefix="SET" suffixOverrides=",">
            <if test="classificationLevel != null">classification_level = #{classificationLevel},</if>
            <if test="classificationName != null and classificationName != ''">classification_name = #{classificationName},</if>
            <if test="classificationSort != null">classification_sort = #{classificationSort},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where classification_code = #{classificationCode}
    </update>

    <delete id="deleteBaseExpertProfessionClassificationByClassificationCode" parameterType="String">
--         delete from base_expert_profession_classification where classification_code = #{classificationCode}
update  base_expert_profession_classification set del_flag=1 where classification_code = #{classificationCode}
    </delete>

    <delete id="deleteBaseExpertProfessionClassificationByClassificationCodes" parameterType="String">
--         delete from base_expert_profession_classification where classification_code in
        update  base_expert_profession_classification set del_flag=1 where classification_code in
        <foreach item="classificationCode" collection="array" open="(" separator="," close=")">
            #{classificationCode}
        </foreach>
    </delete>
</mapper>