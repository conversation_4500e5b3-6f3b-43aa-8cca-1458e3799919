<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiTenderIntentionMapper">
    
    <resultMap type="BusiTenderIntention" id="BusiTenderIntentionResult">
        <result property="intentionId"    column="intention_id"    />
        <result property="intentionCode"    column="intention_code"    />
        <result property="intentionName"    column="intention_name"    />
        <result property="intentionContent"    column="intention_content"    />
        <result property="budgetAmount"    column="budget_amount"    />
        <result property="projectDuration"    column="project_duration"    />
        <result property="tenderMode"    column="tender_mode"    />
        <result property="intentionStartTime"    column="intention_start_time"    />
        <result property="intentionEndTime"    column="intention_end_time"    />
        <result property="tendererId"    column="tenderer_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <select id="selectByProject" parameterType="java.lang.Long">
        select ti.* from busi_tender_intention ti, busi_tender_project tp
        WHERE tp.project_intention_id=ti.intention_id
        AND tp.project_id=#{tenderProjectId}
        AND ti.del_flag=0
    </select>

</mapper>