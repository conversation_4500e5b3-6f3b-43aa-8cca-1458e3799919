<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiTenderNoticeMapper">

    <resultMap type="BusiTenderNotice" id="BusiTenderNoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="noticeCode"    column="notice_code"    />
        <result property="noticeName"    column="notice_name"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeStartTime"    column="notice_start_time"    />
        <result property="noticeEndTime"    column="notice_end_time"    />
        <result property="bidOpeningTime"    column="bid_opening_time"    />
        <result property="bidOpeningEndTime"    column="bid_opening_end_time"    />
        <result property="bidEvaluationTime"    column="bid_evaluation_time"    />
        <result property="bidEvaluationEndTime"    column="bid_evaluation_end_time"    />
        <result property="projectStartTime"    column="project_start_time"    />
        <result property="projectDeadline"    column="project_deadline"    />
        <result property="allowCoalition"    column="allow_coalition"    />
        <result property="toSme"    column="to_sme"    />
        <result property="bidOpeningMode"    column="bid_opening_mode"    />
        <result property="bidEvaluationMode"    column="bid_evaluation_mode"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="noticeStats"    column="notice_stats"    />
        <result property="changeNum"    column="change_num"    />
        <result property="priceUnit"    column="price_unit"    />
        <result property="venueType"    column="venue_type"    />
        <result property="paymentMethod" column="payment_method" />
        <result column="doc_acquisition_start_time" property="docAcquisitionStartTime" />
        <result column="doc_acquisition_end_time" property="docAcquisitionEndTime" />
        <result column="doc_response_over_time" property="docResponseOverTime"/>
        <result column="subcontracting_allowed" property="subcontractingAllowed" />

        <result column="invite_bidder" property="inviteBidder" />

        <result column="payment_method" property="paymentMethod" />
        <result column="contract_performance_period" property="contractPerformancePeriod" />
        <result column="shared_first_rule" property="sharedFirstRule" />
        <result column="shared_first_rule_remark" property="sharedFirstRuleRemark" />
    </resultMap>

    <select id="selectForBidOpening" >
        select tn.*, tp.project_code, tp.project_name from busi_tender_notice tn, busi_tender_project tp
        WHERE tn.project_id = tp.project_id
        <if test="tendererId != null "> and tp.tenderer_id = #{tendererId}</if>
        <if test="agencyId != null "> and tp.agency_id = #{agencyId}</if>
        and tn.bid_opening_time &lt;  NOW()
        and tn.del_flag = 0
        and tp.project_status between 20 and 40
        and tn.project_id not in (select project_id from busi_bid_opening where del_flag=0)
    </select>

    <select id="getOneIgnoreDeleted" parameterType="com.ruoyi.busi.domain.BusiTenderNotice">
        select * from busi_tender_notice
        <where>
            <if test="info.noticeId != null "> and notice_id = #{info.noticeId}</if>
            <if test="info.projectId != null "> and project_id = #{info.projectId}</if>
            <if test="info.delFlag != null "> and del_flag = #{info.delFlag}</if>
            <if test="info.changeNum != null "> and change_num = #{info.changeNum}</if>
            <if test="info.noticeType != null "> and notice_type = #{info.noticeType}</if>
            <if test="info.venueType != null "> and venue_type = #{info.venueType}</if>
        </where>
    </select>

    <select id="getListIgnoreDeleted" parameterType="com.ruoyi.busi.domain.BusiTenderNotice">
        select * from busi_tender_notice
        <where>
            <if test="info.noticeId != null "> and notice_id = #{info.noticeId}</if>
            <if test="info.projectId != null "> and project_id = #{info.projectId}</if>
            <if test="info.delFlag != null "> and del_flag = #{info.delFlag}</if>
            <if test="info.changeNum != null "> and change_num = #{info.changeNum}</if>
            <if test="info.noticeType != null "> and notice_type = #{info.noticeType}</if>
            <if test="info.venueType != null "> and venue_type = #{info.venueType}</if>
        </where>
        order by create_time desc
    </select>
</mapper>
