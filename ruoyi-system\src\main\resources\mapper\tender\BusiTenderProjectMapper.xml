<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiTenderProjectMapper">

    <resultMap type="BusiTenderProject" id="BusiTenderProjectResult">
        <result property="projectId"    column="project_id"    />
        <result property="projectStatus" column="project_status" />
        <result property="projectCode"    column="project_code"    />
        <result property="projectIntentionId"    column="project_intention_id"    />
        <result property="isEmergencyProject"    column="is_emergency_project"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectContent"    column="project_content"    />
        <result property="bidderQualification"    column="bidder_qualification"    />
        <result property="projectArea"    column="project_area"    />
        <result property="projectIndustry"    column="project_industry"    />
        <result property="tendererId"    column="tenderer_id"    />
        <result property="tendererCode"    column="tenderer_code"    />
        <result property="tendererName"    column="tenderer_name"    />
        <result property="tendererContactPerson"    column="tenderer_contact_person"    />
        <result property="tendererPhone"    column="tenderer_phone"    />
        <result property="tenderMode"    column="tender_mode"    />
        <result property="projectType" column="project_type" />
        <result property="agencyFlag"    column="agency_flag"    />
        <result property="agencyId"    column="agency_id"    />
        <result property="agencyCode"    column="agency_code"    />
        <result property="agencyName"    column="agency_name"    />
        <result property="agencyContactPerson"    column="agency_contact_person"    />
        <result property="agencyPhone"    column="agency_phone"    />
        <result property="budgetAmount"    column="budget_amount"    />
        <result property="controlPrice"    column="control_price"    />
        <result property="tenderFundSource"    column="tender_fund_source"    />
        <result property="tenderSelfFund"    column="tender_self_fund"    />
        <result property="tenderFinancialFund"    column="tender_financial_fund"    />
        <result property="projectDuration"    column="project_duration"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="chargingStandard" column="charging_standard" />
        <result property="chargingStandard" column="charging_standard" />

    </resultMap>

    <sql id="selectBusiTenderProjectVo">
        select project_id, project_code, project_intention_id, is_emergency_project, project_name, project_content, bidder_qualification, project_area, project_industry, tenderer_id, tenderer_code, tenderer_name, tenderer_contact_person, tenderer_phone, tender_mode, agency_flag, agency_id, agency_code, agency_name, agency_contact_person, agency_phone, budget_amount, control_price, tender_fund_source, tender_self_fund, tender_financial_fund, project_duration, del_flag, create_time, create_by, update_time, update_by from busi_tender_project
    </sql>

    <sql id="selectMultBusiTenderProjectVo">
        m.project_id, m.project_code, m.project_intention_id, m.is_emergency_project, m.project_name,
        m.project_area, m.project_industry, m.tenderer_id, m.tenderer_code, m.tenderer_name,
        m.tenderer_contact_person, m.tenderer_phone, m.tender_mode, m.agency_flag, m.agency_id,
        m.agency_code, m.agency_name, m.agency_contact_person, m.agency_phone, m.budget_amount,
        m.control_price, m.tender_fund_source, m.tender_self_fund, m.tender_financial_fund,
        m.project_duration, m.del_flag, m.create_time, m.create_by, m.update_time, m.update_by
    </sql>

    <select id="selectBusiTenderProjectList" parameterType="BusiTenderProject" resultMap="BusiTenderProjectResult">
        <include refid="selectBusiTenderProjectVo"/>
        <where>
            <if test="projectCode != null  and projectCode != ''"> and project_code = #{projectCode}</if>
            <if test="projectIntentionId != null "> and project_intention_id = #{projectIntentionId}</if>
            <if test="isEmergencyProject != null "> and is_emergency_project = #{isEmergencyProject}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectContent != null  and projectContent != ''"> and project_content = #{projectContent}</if>
            <if test="bidderQualification != null  and bidderQualification != ''"> and bidder_qualification = #{bidderQualification}</if>
            <if test="projectArea != null  and projectArea != ''"> and project_area = #{projectArea}</if>
            <if test="projectIndustry != null  and projectIndustry != ''"> and project_industry = #{projectIndustry}</if>
            <if test="tendererId != null "> and tenderer_id = #{tendererId}</if>
            <if test="tendererCode != null  and tendererCode != ''"> and tenderer_code = #{tendererCode}</if>
            <if test="tendererName != null  and tendererName != ''"> and tenderer_name like concat('%', #{tendererName}, '%')</if>
            <if test="tendererContactPerson != null  and tendererContactPerson != ''"> and tenderer_contact_person = #{tendererContactPerson}</if>
            <if test="tendererPhone != null  and tendererPhone != ''"> and tenderer_phone = #{tendererPhone}</if>
            <if test="tenderMode != null  and tenderMode != ''"> and tender_mode = #{tenderMode}</if>
            <if test="agencyFlag != null "> and agency_flag = #{agencyFlag}</if>
            <if test="agencyId != null "> and agency_id = #{agencyId}</if>
            <if test="agencyCode != null  and agencyCode != ''"> and agency_code = #{agencyCode}</if>
            <if test="agencyName != null  and agencyName != ''"> and agency_name like concat('%', #{agencyName}, '%')</if>
            <if test="agencyContactPerson != null  and agencyContactPerson != ''"> and agency_contact_person = #{agencyContactPerson}</if>
            <if test="agencyPhone != null  and agencyPhone != ''"> and agency_phone = #{agencyPhone}</if>
            <if test="budgetAmount != null "> and budget_amount = #{budgetAmount}</if>
            <if test="controlPrice != null "> and control_price = #{controlPrice}</if>
            <if test="tenderFundSource != null  and tenderFundSource != ''"> and tender_fund_source = #{tenderFundSource}</if>
            <if test="tenderSelfFund != null "> and tender_self_fund = #{tenderSelfFund}</if>
            <if test="tenderFinancialFund != null "> and tender_financial_fund = #{tenderFinancialFund}</if>
            <if test="projectDuration != null "> and project_duration = #{projectDuration}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
    </select>

    <select id="selectBusiTenderProjectByProjectId" parameterType="Long" resultMap="BusiTenderProjectResult">
        <include refid="selectBusiTenderProjectVo"/>
        where project_id = #{projectId}
    </select>

    <insert id="insertBusiTenderProject" parameterType="BusiTenderProject">
        insert into busi_tender_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="projectCode != null">project_code,</if>
            <if test="projectIntentionId != null">project_intention_id,</if>
            <if test="isEmergencyProject != null">is_emergency_project,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectContent != null">project_content,</if>
            <if test="bidderQualification != null">bidder_qualification,</if>
            <if test="projectArea != null">project_area,</if>
            <if test="projectIndustry != null">project_industry,</if>
            <if test="tendererId != null">tenderer_id,</if>
            <if test="tendererCode != null">tenderer_code,</if>
            <if test="tendererName != null">tenderer_name,</if>
            <if test="tendererContactPerson != null">tenderer_contact_person,</if>
            <if test="tendererPhone != null">tenderer_phone,</if>
            <if test="tenderMode != null">tender_mode,</if>
            <if test="agencyFlag != null">agency_flag,</if>
            <if test="agencyId != null">agency_id,</if>
            <if test="agencyCode != null">agency_code,</if>
            <if test="agencyName != null">agency_name,</if>
            <if test="agencyContactPerson != null">agency_contact_person,</if>
            <if test="agencyPhone != null">agency_phone,</if>
            <if test="budgetAmount != null">budget_amount,</if>
            <if test="controlPrice != null">control_price,</if>
            <if test="tenderFundSource != null">tender_fund_source,</if>
            <if test="tenderSelfFund != null">tender_self_fund,</if>
            <if test="tenderFinancialFund != null">tender_financial_fund,</if>
            <if test="projectDuration != null">project_duration,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="projectCode != null">#{projectCode},</if>
            <if test="projectIntentionId != null">#{projectIntentionId},</if>
            <if test="isEmergencyProject != null">#{isEmergencyProject},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectContent != null">#{projectContent},</if>
            <if test="bidderQualification != null">#{bidderQualification},</if>
            <if test="projectArea != null">#{projectArea},</if>
            <if test="projectIndustry != null">#{projectIndustry},</if>
            <if test="tendererId != null">#{tendererId},</if>
            <if test="tendererCode != null">#{tendererCode},</if>
            <if test="tendererName != null">#{tendererName},</if>
            <if test="tendererContactPerson != null">#{tendererContactPerson},</if>
            <if test="tendererPhone != null">#{tendererPhone},</if>
            <if test="tenderMode != null">#{tenderMode},</if>
            <if test="agencyFlag != null">#{agencyFlag},</if>
            <if test="agencyId != null">#{agencyId},</if>
            <if test="agencyCode != null">#{agencyCode},</if>
            <if test="agencyName != null">#{agencyName},</if>
            <if test="agencyContactPerson != null">#{agencyContactPerson},</if>
            <if test="agencyPhone != null">#{agencyPhone},</if>
            <if test="budgetAmount != null">#{budgetAmount},</if>
            <if test="controlPrice != null">#{controlPrice},</if>
            <if test="tenderFundSource != null">#{tenderFundSource},</if>
            <if test="tenderSelfFund != null">#{tenderSelfFund},</if>
            <if test="tenderFinancialFund != null">#{tenderFinancialFund},</if>
            <if test="projectDuration != null">#{projectDuration},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBusiTenderProject" parameterType="BusiTenderProject">
        update busi_tender_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectCode != null">project_code = #{projectCode},</if>
            <if test="projectIntentionId != null">project_intention_id = #{projectIntentionId},</if>
            <if test="isEmergencyProject != null">is_emergency_project = #{isEmergencyProject},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectContent != null">project_content = #{projectContent},</if>
            <if test="bidderQualification != null">bidder_qualification = #{bidderQualification},</if>
            <if test="projectArea != null">project_area = #{projectArea},</if>
            <if test="projectIndustry != null">project_industry = #{projectIndustry},</if>
            <if test="tendererId != null">tenderer_id = #{tendererId},</if>
            <if test="tendererCode != null">tenderer_code = #{tendererCode},</if>
            <if test="tendererName != null">tenderer_name = #{tendererName},</if>
            <if test="tendererContactPerson != null">tenderer_contact_person = #{tendererContactPerson},</if>
            <if test="tendererPhone != null">tenderer_phone = #{tendererPhone},</if>
            <if test="tenderMode != null">tender_mode = #{tenderMode},</if>
            <if test="agencyFlag != null">agency_flag = #{agencyFlag},</if>
            <if test="agencyId != null">agency_id = #{agencyId},</if>
            <if test="agencyCode != null">agency_code = #{agencyCode},</if>
            <if test="agencyName != null">agency_name = #{agencyName},</if>
            <if test="agencyContactPerson != null">agency_contact_person = #{agencyContactPerson},</if>
            <if test="agencyPhone != null">agency_phone = #{agencyPhone},</if>
            <if test="budgetAmount != null">budget_amount = #{budgetAmount},</if>
            <if test="controlPrice != null">control_price = #{controlPrice},</if>
            <if test="tenderFundSource != null">tender_fund_source = #{tenderFundSource},</if>
            <if test="tenderSelfFund != null">tender_self_fund = #{tenderSelfFund},</if>
            <if test="tenderFinancialFund != null">tender_financial_fund = #{tenderFinancialFund},</if>
            <if test="projectDuration != null">project_duration = #{projectDuration},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deleteBusiTenderProjectByProjectId" parameterType="Long">
--         delete from busi_tender_project where project_id = #{projectId}
update  busi_tender_project set del_flag=1 where project_id = #{projectId}
    </delete>

    <delete id="deleteBusiTenderProjectByProjectIds" parameterType="String">
--         delete from busi_tender_project where project_id in
        update  busi_tender_project set del_flag=1 where project_id in
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>


    <select id="selectBidOpeningProject" parameterType="BusiTenderProject" resultMap="BusiTenderProjectResult">
        select <include refid="selectMultBusiTenderProjectVo"/>
        from busi_tender_project m, busi_tender_notice n
        where
            n.project_id=m.project_id
            <if test="tendererId != null "> and m.tenderer_id = #{tendererId}</if>
            <if test="agencyId != null "> and m.agency_id = #{agencyId}</if>
             and n.bid_opening_time &lt;  NOW()
             and m.project_id not in (select project_id from busi_bid_opening where del_flag=0)
    </select>

    <select id="getMax" resultType="java.lang.Integer">
        select COUNT(*) from busi_tender_project WHERE project_code LIKE CONCAT(#{projectId}, '%')
    </select>
</mapper>
