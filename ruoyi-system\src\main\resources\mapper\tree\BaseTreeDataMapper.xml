<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.BaseTreeDataMapper">
    
    <resultMap type="BaseTreeData" id="BaseTreeDataResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="thirdId"    column="third_id"    />
        <result property="thirdPid"    column="third_pid"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>
</mapper>