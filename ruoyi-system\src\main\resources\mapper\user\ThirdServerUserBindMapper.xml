<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.third.mapper.ThirdServerUserBindMapper">
    
    <resultMap type="ThirdServerUserBind" id="ThirdServerUserBindResult">
        <result property="id"    column="id"    />
        <result property="serverName"    column="server_name"    />
        <result property="serverUserId"    column="server_user_id"    />
        <result property="localUserId"    column="local_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
</mapper>