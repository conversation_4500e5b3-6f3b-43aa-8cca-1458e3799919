<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.busi.mapper.BusiVenueOccupyMapper">
    
    <resultMap type="BusiVenueOccupy" id="BusiVenueOccupyResult">
        <result property="occupyId"    column="occupy_id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="venueId"    column="venue_id"    />
        <result property="venueName"    column="venue_name"    />
        <result property="venueType"    column="venue_type"    />
        <result property="occupyStartTime"    column="occupy_start_time"    />
        <result property="occupyEndTime"    column="occupy_end_time"    />
        <result property="peopleNumber"    column="people_number"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="bidEvaluationPeriod"    column="bid_evaluation_period"    />
    </resultMap>

    <select id="getListIgnoreDeleted" parameterType="com.ruoyi.busi.domain.BusiVenueOccupy">
        select * from busi_venue_occupy
        <where>
            <if test="info.occupyId != null "> and occupy_id = #{info.occupyId}</if>
            <if test="info.noticeId != null "> and notice_id = #{info.noticeId}</if>
            <if test="info.delFlag != null "> and del_flag = #{info.delFlag}</if>
            <if test="info.venueType != null "> and venue_type = #{info.venueType}</if>
            <if test="info.venueId != null "> and venue_id = #{info.venueId}</if>
        </where>
    </select>
</mapper>