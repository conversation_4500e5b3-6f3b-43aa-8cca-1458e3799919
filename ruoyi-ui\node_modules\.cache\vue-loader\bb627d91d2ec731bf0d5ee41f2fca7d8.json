{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue?vue&type=style&index=0&id=605e801c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue", "mtime": 1753865965427}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRocmVlIHsNCiAgcGFkZGluZzogMjBweCA0MHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KfQ0KDQoubGVmdC1wYW5lbCB7DQogIHdpZHRoOiA3MCU7DQp9DQoNCi5yaWdodC1wYW5lbCB7DQogIHdpZHRoOiAzMCU7DQp9DQoNCi50aXRsZSB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgZm9udC1mYW1pbHk6IFNvdXJjZUhhblNhbnNTQy1Cb2xkOw0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMzMzMzMzOw0KICBsZXR0ZXItc3BhY2luZzogMDsNCn0NCg0KLmZ1bGwtd2lkdGgtdGFibGUgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnJlc3VsdC10aXRsZSB7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2VIYW5TYW5zU0MtQm9sZDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogIzMzMzMzMzsNCiAgbGV0dGVyLXNwYWNpbmc6IDA7DQogIG1hcmdpbjogMjBweDsNCglkaXNwbGF5OiBmbGV4Ow0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5yZXN1bHQtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCn0NCg0KLnJlc3VsdC1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgbWFyZ2luLXJpZ2h0OiAzMHB4Ow0KICBmb250LWZhbWlseTogU291cmNlSGFuU2Fuc1NDLUJvbGQ7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgY29sb3I6ICMzMzMzMzM7DQogIGxldHRlci1zcGFjaW5nOiAwOw0KfQ0KDQouc3VwcGxpZXItbmFtZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCn0NCg0KLnJldmlldy1idXR0b24gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjMTc2YWRiICFpbXBvcnRhbnQ7DQp9DQoNCi52b3RpbmctdGl0bGUgew0KICBmb250LWZhbWlseTogU291cmNlSGFuU2Fuc1NDLUJvbGQ7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgY29sb3I6ICMzMzMzMzM7DQogIGxldHRlci1zcGFjaW5nOiAwOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoud2FybmluZy1tZXNzYWdlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogI2U2YTIzYzsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZkZjZlYzsNCiAgYm9yZGVyOiAxcHggc29saWQgI2Y1ZGFiMTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCi5lbC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBjb2xvcjogIzMzMzsNCiAgZm9udC1zaXplOiAyNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiAxMDBweDsNCiAgYm9yZGVyLWJvdHRvbTogIzMzMyAxcHggc29saWQ7DQp9DQouZWwtbWFpbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjMzMzOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4MHB4Ow0KICAuaXRlbS10aXRsZSB7DQogICAgd2lkdGg6IDEyMHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICB9DQp9DQouaXRlbS1idXR0b24gew0KICB3aWR0aDogMTUwcHg7DQogIGhlaWdodDogNDBweDsNCiAgbWFyZ2luOiAyMHB4IDI4cHg7DQogIGNvbG9yOiAjZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTc2YWRiOw0KICBib3JkZXI6IDA7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQoucmVzdWx0IHsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgbWFyZ2luLWxlZnQ6IDIwcHg7DQp9DQoub3BlcmF0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoudGV4dCB7DQogIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogICAgYm9yZGVyLXJhZGl1czogMDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZjVmNWY1Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8bA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/business", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div class=\"left-panel\">\r\n      <div class=\"title\">商务标评审</div>\r\n      <el-table :data=\"tableData\" border class=\"full-width-table\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"result\">\r\n        <div class=\"result-title\">\r\n          评审结果：评分结果以少数服从多数。\r\n          <div v-if=\"hasInconsistentScores\" class=\"warning-message\">\r\n            评分内容不符合少数服从多数，暂先以专家组长的评分为结果，建议专家组长进行表决\r\n          </div>\r\n        </div>\r\n        <div class=\"result-container\">\r\n          <div class=\"result-item\" v-for=\"(item,index) in result\" :key=\"index\">\r\n            <div class=\"supplier-name\">{{ item.gys }}</div>\r\n\t          <el-input disabled v-model=\"item.result\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          v-if=\"hasInconsistentScores\"\r\n          class=\"item-button review-button\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" @click=\"completed\">节点评审完成</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"right-panel\">\r\n      <div class=\"result\">\r\n        <div class=\"voting-title\">表决结果</div>\r\n\r\n        <el-input class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { leaderSummaryQuery } from \"@/api/expert/review\";\r\nimport { updateProcess } from \"@/api/evaluation/process\";\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  props: {\r\n    finish: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      columns: {},\r\n      result: [],\r\n      isReadOnly: false,\r\n      votingResults: \"\",\r\n      hasInconsistentScores: false,\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        itemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.votingResults = response.data.bjjgsb\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n          this.columns = response.data.tableColumns;\r\n          \r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n\r\n          // if (response.data.evaluationProcess) {\r\n          //   let psjg = JSON.parse(\r\n          //     response.data.evaluationProcess.evaluationResult\r\n          //   );\r\n          //   this.result = psjg;\r\n          //   if (response.data.evaluationProcess.evaluationState == 2) {\r\n          //     this.isReadOnly = true;\r\n          //   }\r\n          // }\r\n          // if (this.result == null) {\r\n          //   this.result = this.generateResultTable(\r\n          //     response.data.tableColumns,\r\n          //     response.data.busiBidderInfos,\r\n          //     response.data.tableData\r\n          //   );\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 转换函数\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建一个映射，用于将 bidderId 映射到 bidderName\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };\r\n        return acc;\r\n      }, {});\r\n\r\n      // 创建一个映射，用于将 resultId 映射到 itemName\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换数据\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };\r\n\r\n        // 只取 tableColumns 中定义的评估项\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;\r\n          transformedRow[column.xm] = row[itemId] || \"未提交\"; // 默认为 '0' 如果没有找到对应值\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    // 组装评审结果\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, bidder.bidderName])\r\n      );\r\n\r\n      // 检测是否存在评分不一致的情况\r\n      let hasInconsistent = false;\r\n\r\n      // Generate the result table、\r\n      const resultTable = tableData.map((row) => {\r\n        // 检查当前行是否存在评分不一致\r\n        if (this.hasInconsistentScoring(row)) {\r\n          hasInconsistent = true;\r\n        }\r\n\r\n        return {\r\n          bidder: row.gys,\r\n          gys: bidderMap.get(row.gys),\r\n          result: this.getMode(row),\r\n        };\r\n      });\r\n\r\n      // 设置不一致标志\r\n      this.hasInconsistentScores = hasInconsistent;\r\n\r\n      return resultTable;\r\n    },\r\n    /**\r\n     * 获取众数（统计学中的模式值）\r\n     * 用于计算专家评审结果中出现频率最高的评分值\r\n     * 如果评分不一致（所有评分都不相同），会返回任意一个值，此时需要专家组长表决\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象，通常包含多个专家的评分\r\n     * @returns {string|number|null} 返回出现次数最多的值，如果没有数据则返回null\r\n     */\r\n    getMode(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = row;\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      // gys字段通常表示供应商ID，不是评分数据\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      // Object.values()返回对象所有属性值组成的数组\r\n      const values = Object.values(data);\r\n\r\n      // 使用reduce方法统计每个值出现的次数\r\n      // 创建频率映射表：{值: 出现次数}\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        // 如果该值已存在，次数+1；否则初始化为1\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 遍历频率映射表，找出出现次数最多的值（众数）\r\n      let maxFrequency = 0;    // 记录最大出现次数\r\n      let mode = null;         // 记录众数值\r\n\r\n      // 遍历频率映射表的每个键值对\r\n      for (const [value, frequency] of Object.entries(frequencyMap)) {\r\n        // 如果当前值的出现次数大于已记录的最大次数\r\n        if (frequency > maxFrequency) {\r\n          maxFrequency = frequency;  // 更新最大出现次数\r\n          mode = value;              // 更新众数值\r\n        }\r\n      }\r\n\r\n      // 返回众数，用作该行的最终评审结果\r\n      // 注意：如果最大出现次数为1，说明所有评分都不相同，此时mode可能为任意一个值\r\n      // 这种情况下需要专家组长进行表决\r\n      return mode;\r\n    },\r\n    /**\r\n     * 获取专家组长的评分\r\n     * 从tableColumns中找到expertLeader为1的专家，返回其对应的评分\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {string|number|null} 返回专家组长的评分，如果找不到则返回null\r\n     */\r\n    getLeaderScore(row) {\r\n      // 找到专家组长的列（expertLeader为1）\r\n      const leaderColumn = this.columns.find(column => column.expertLeader === 1);\r\n\r\n      if (!leaderColumn) {\r\n        console.warn('未找到专家组长信息');\r\n        return null;\r\n      }\r\n\r\n      // 返回专家组长对应的评分\r\n      // 使用resultId作为key从row中获取评分\r\n      return row[leaderColumn.resultId] || null;\r\n    },\r\n    /**\r\n     * 检测是否存在评分不一致的情况\r\n     * 当某家供应商的所有专家评分都不相同时，返回true\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {boolean} 如果评分不一致返回true，否则返回false\r\n     */\r\n    hasInconsistentScoring(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = { ...row };\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      const values = Object.values(data);\r\n\r\n      // 如果评分数量少于2个，不存在不一致问题\r\n      if (values.length < 2) {\r\n        return false;\r\n      }\r\n\r\n      // 统计每个值出现的次数\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 获取最大出现次数\r\n      const maxFrequency = Math.max(...Object.values(frequencyMap));\r\n\r\n      // 如果最大出现次数为1，说明所有评分都不相同\r\n      return maxFrequency === 1;\r\n    },\r\n    // 节点评审完成\r\n    completed() {\r\n      //检查所有的 result 是否为空\r\n      const allResultsNotEmpty = this.result.every(\r\n        (item) => item.result !== \"\"\r\n      );\r\n      // const allResultsNotEmpty = true;\r\n      if (!allResultsNotEmpty) {\r\n        console.log(\"请填写评审结果\");\r\n        this.$message.warning(\"请完善评审结果\");\r\n        return false;\r\n      }\r\n\r\n      // 检查是否存在评分不一致的情况\r\n      if (this.hasInconsistentScores) {\r\n        this.$message.warning(\"评分内容不符合多数服从少数，建议专家组长进行表决\");\r\n        return false;\r\n      }\r\n      // else {\r\n      const evaluationProcessId = JSON.parse(\r\n        localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n      );\r\n      const data = {\r\n        evaluationProcessId: evaluationProcessId.evaluationProcessId,\r\n        evaluationResult: JSON.stringify(this.result),\r\n        evaluationState: 2,\r\n        evaluationResultRemark: this.votingResults,\r\n      };\r\n      updateProcess(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$router.push({\r\n            path: \"/expertInfo\",\r\n            query: {\r\n              projectId: this.$route.query.projectId,\r\n              zjhm: this.$route.query.zjhm,\r\n            },\r\n          });\r\n        } else {\r\n          this.$message.warning();\r\n        }\r\n      });\r\n      // }\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n    getIconClass(value) {\r\n      return value == \"1\" ? \"el-icon-check\" : \"el-icon-circle-close\";\r\n    },\r\n    // 重新评审\r\n    reviewed() {\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,\r\n      };\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确认流标\r\n    confirmflow() {\r\n      if (this.reasonFlowBid == \"\") {\r\n        const data = {\r\n          projectId: this.$route.query.projectId,\r\n          remark: this.reasonFlowBid,\r\n        };\r\n        abortiveTenderNotice(data).then((response) => {\r\n          if (response.code == 200) {\r\n            // 跳转到哪个页面\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      } else {\r\n      }\r\n    },\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n\t\tthis.intervalId = setInterval(()=>{\r\n\t\t\tthis.init();\r\n\t\t},5000)\r\n  },\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n\r\n.left-panel {\r\n  width: 70%;\r\n}\r\n\r\n.right-panel {\r\n  width: 30%;\r\n}\r\n\r\n.title {\r\n  position: relative;\r\n  margin-bottom: 20px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.full-width-table {\r\n  width: 100%;\r\n}\r\n\r\n.result-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.result-container {\r\n  display: flex;\r\n}\r\n\r\n.result-item {\r\n  display: flex;\r\n  margin-right: 30px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.supplier-name {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  margin-right: 10px;\r\n}\r\n\r\n.review-button {\r\n  background-color: #f5f5f5 !important;\r\n  color: #176adb !important;\r\n}\r\n\r\n.voting-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.warning-message {\r\n  font-size: 14px;\r\n  color: #e6a23c;\r\n  background-color: #fdf6ec;\r\n  border: 1px solid #f5dab1;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  margin-top: 10px;\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 20px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}