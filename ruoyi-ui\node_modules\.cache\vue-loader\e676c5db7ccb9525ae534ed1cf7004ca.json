{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue", "mtime": 1753865965427}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/business", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div class=\"left-panel\">\r\n      <div class=\"title\">商务标评审</div>\r\n      <el-table :data=\"tableData\" border class=\"full-width-table\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"result\">\r\n        <div class=\"result-title\">\r\n          评审结果：评分结果以少数服从多数。\r\n          <div v-if=\"hasInconsistentScores\" class=\"warning-message\">\r\n            评分内容不符合少数服从多数，暂先以专家组长的评分为结果，建议专家组长进行表决\r\n          </div>\r\n        </div>\r\n        <div class=\"result-container\">\r\n          <div class=\"result-item\" v-for=\"(item,index) in result\" :key=\"index\">\r\n            <div class=\"supplier-name\">{{ item.gys }}</div>\r\n\t          <el-input disabled v-model=\"item.result\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          v-if=\"hasInconsistentScores\"\r\n          class=\"item-button review-button\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" @click=\"completed\">节点评审完成</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"right-panel\">\r\n      <div class=\"result\">\r\n        <div class=\"voting-title\">表决结果</div>\r\n\r\n        <el-input class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { leaderSummaryQuery } from \"@/api/expert/review\";\r\nimport { updateProcess } from \"@/api/evaluation/process\";\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  props: {\r\n    finish: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      columns: {},\r\n      result: [],\r\n      isReadOnly: false,\r\n      votingResults: \"\",\r\n      hasInconsistentScores: false,\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        itemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.votingResults = response.data.bjjgsb\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n          this.columns = response.data.tableColumns;\r\n          \r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n\r\n          // if (response.data.evaluationProcess) {\r\n          //   let psjg = JSON.parse(\r\n          //     response.data.evaluationProcess.evaluationResult\r\n          //   );\r\n          //   this.result = psjg;\r\n          //   if (response.data.evaluationProcess.evaluationState == 2) {\r\n          //     this.isReadOnly = true;\r\n          //   }\r\n          // }\r\n          // if (this.result == null) {\r\n          //   this.result = this.generateResultTable(\r\n          //     response.data.tableColumns,\r\n          //     response.data.busiBidderInfos,\r\n          //     response.data.tableData\r\n          //   );\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 转换函数\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建一个映射，用于将 bidderId 映射到 bidderName\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };\r\n        return acc;\r\n      }, {});\r\n\r\n      // 创建一个映射，用于将 resultId 映射到 itemName\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换数据\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };\r\n\r\n        // 只取 tableColumns 中定义的评估项\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;\r\n          transformedRow[column.xm] = row[itemId] || \"未提交\"; // 默认为 '0' 如果没有找到对应值\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    // 组装评审结果\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, bidder.bidderName])\r\n      );\r\n\r\n      // 检测是否存在评分不一致的情况\r\n      let hasInconsistent = false;\r\n\r\n      // Generate the result table、\r\n      const resultTable = tableData.map((row) => {\r\n        // 检查当前行是否存在评分不一致\r\n        if (this.hasInconsistentScoring(row)) {\r\n          hasInconsistent = true;\r\n        }\r\n\r\n        return {\r\n          bidder: row.gys,\r\n          gys: bidderMap.get(row.gys),\r\n          result: this.getMode(row),\r\n        };\r\n      });\r\n\r\n      // 设置不一致标志\r\n      this.hasInconsistentScores = hasInconsistent;\r\n\r\n      return resultTable;\r\n    },\r\n    /**\r\n     * 获取众数（统计学中的模式值）\r\n     * 用于计算专家评审结果中出现频率最高的评分值\r\n     * 如果评分不一致（所有评分都不相同），会返回任意一个值，此时需要专家组长表决\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象，通常包含多个专家的评分\r\n     * @returns {string|number|null} 返回出现次数最多的值，如果没有数据则返回null\r\n     */\r\n    getMode(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = row;\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      // gys字段通常表示供应商ID，不是评分数据\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      // Object.values()返回对象所有属性值组成的数组\r\n      const values = Object.values(data);\r\n\r\n      // 使用reduce方法统计每个值出现的次数\r\n      // 创建频率映射表：{值: 出现次数}\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        // 如果该值已存在，次数+1；否则初始化为1\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 遍历频率映射表，找出出现次数最多的值（众数）\r\n      let maxFrequency = 0;    // 记录最大出现次数\r\n      let mode = null;         // 记录众数值\r\n\r\n      // 遍历频率映射表的每个键值对\r\n      for (const [value, frequency] of Object.entries(frequencyMap)) {\r\n        // 如果当前值的出现次数大于已记录的最大次数\r\n        if (frequency > maxFrequency) {\r\n          maxFrequency = frequency;  // 更新最大出现次数\r\n          mode = value;              // 更新众数值\r\n        }\r\n      }\r\n\r\n      // 返回众数，用作该行的最终评审结果\r\n      // 注意：如果最大出现次数为1，说明所有评分都不相同，此时mode可能为任意一个值\r\n      // 这种情况下需要专家组长进行表决\r\n      return mode;\r\n    },\r\n    /**\r\n     * 获取专家组长的评分\r\n     * 从tableColumns中找到expertLeader为1的专家，返回其对应的评分\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {string|number|null} 返回专家组长的评分，如果找不到则返回null\r\n     */\r\n    getLeaderScore(row) {\r\n      // 找到专家组长的列（expertLeader为1）\r\n      const leaderColumn = this.columns.find(column => column.expertLeader === 1);\r\n\r\n      if (!leaderColumn) {\r\n        console.warn('未找到专家组长信息');\r\n        return null;\r\n      }\r\n\r\n      // 返回专家组长对应的评分\r\n      // 使用resultId作为key从row中获取评分\r\n      return row[leaderColumn.resultId] || null;\r\n    },\r\n    /**\r\n     * 检测是否存在评分不一致的情况\r\n     * 当某家供应商的所有专家评分都不相同时，返回true\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {boolean} 如果评分不一致返回true，否则返回false\r\n     */\r\n    hasInconsistentScoring(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = { ...row };\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      const values = Object.values(data);\r\n\r\n      // 如果评分数量少于2个，不存在不一致问题\r\n      if (values.length < 2) {\r\n        return false;\r\n      }\r\n\r\n      // 统计每个值出现的次数\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 获取最大出现次数\r\n      const maxFrequency = Math.max(...Object.values(frequencyMap));\r\n\r\n      // 如果最大出现次数为1，说明所有评分都不相同\r\n      return maxFrequency === 1;\r\n    },\r\n    // 节点评审完成\r\n    completed() {\r\n      //检查所有的 result 是否为空\r\n      const allResultsNotEmpty = this.result.every(\r\n        (item) => item.result !== \"\"\r\n      );\r\n      // const allResultsNotEmpty = true;\r\n      if (!allResultsNotEmpty) {\r\n        console.log(\"请填写评审结果\");\r\n        this.$message.warning(\"请完善评审结果\");\r\n        return false;\r\n      }\r\n\r\n      // 检查是否存在评分不一致的情况\r\n      if (this.hasInconsistentScores) {\r\n        this.$message.warning(\"评分内容不符合多数服从少数，建议专家组长进行表决\");\r\n        return false;\r\n      }\r\n      // else {\r\n      const evaluationProcessId = JSON.parse(\r\n        localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n      );\r\n      const data = {\r\n        evaluationProcessId: evaluationProcessId.evaluationProcessId,\r\n        evaluationResult: JSON.stringify(this.result),\r\n        evaluationState: 2,\r\n        evaluationResultRemark: this.votingResults,\r\n      };\r\n      updateProcess(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$router.push({\r\n            path: \"/expertInfo\",\r\n            query: {\r\n              projectId: this.$route.query.projectId,\r\n              zjhm: this.$route.query.zjhm,\r\n            },\r\n          });\r\n        } else {\r\n          this.$message.warning();\r\n        }\r\n      });\r\n      // }\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n    getIconClass(value) {\r\n      return value == \"1\" ? \"el-icon-check\" : \"el-icon-circle-close\";\r\n    },\r\n    // 重新评审\r\n    reviewed() {\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,\r\n      };\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确认流标\r\n    confirmflow() {\r\n      if (this.reasonFlowBid == \"\") {\r\n        const data = {\r\n          projectId: this.$route.query.projectId,\r\n          remark: this.reasonFlowBid,\r\n        };\r\n        abortiveTenderNotice(data).then((response) => {\r\n          if (response.code == 200) {\r\n            // 跳转到哪个页面\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      } else {\r\n      }\r\n    },\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n\t\tthis.intervalId = setInterval(()=>{\r\n\t\t\tthis.init();\r\n\t\t},5000)\r\n  },\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n\r\n.left-panel {\r\n  width: 70%;\r\n}\r\n\r\n.right-panel {\r\n  width: 30%;\r\n}\r\n\r\n.title {\r\n  position: relative;\r\n  margin-bottom: 20px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.full-width-table {\r\n  width: 100%;\r\n}\r\n\r\n.result-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.result-container {\r\n  display: flex;\r\n}\r\n\r\n.result-item {\r\n  display: flex;\r\n  margin-right: 30px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.supplier-name {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  margin-right: 10px;\r\n}\r\n\r\n.review-button {\r\n  background-color: #f5f5f5 !important;\r\n  color: #176adb !important;\r\n}\r\n\r\n.voting-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.warning-message {\r\n  font-size: 14px;\r\n  color: #e6a23c;\r\n  background-color: #fdf6ec;\r\n  border: 1px solid #f5dab1;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  margin-top: 10px;\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 20px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}