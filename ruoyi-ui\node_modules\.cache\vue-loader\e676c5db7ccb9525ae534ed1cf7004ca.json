{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\three.vue", "mtime": 1753866123198}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/business", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div class=\"left-panel\">\r\n      <div class=\"title\">商务标评审</div>\r\n      <el-table :data=\"tableData\" border class=\"full-width-table\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"getColumnLabel(item)\">\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"result\">\r\n        <div class=\"result-title\">\r\n          评审结果：评分结果以少数服从多数。\r\n          <div v-if=\"hasInconsistentScores\" class=\"warning-message\">\r\n            评分内容不符合少数服从多数，暂先以专家组长的评分为结果，建议专家组长进行表决\r\n          </div>\r\n        </div>\r\n        <div class=\"result-container\">\r\n          <div class=\"result-item\" v-for=\"(item,index) in result\" :key=\"index\">\r\n            <div class=\"supplier-name\">{{ item.gys }}</div>\r\n\t          <el-input disabled v-model=\"item.result\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          v-if=\"hasInconsistentScores\"\r\n          class=\"item-button review-button\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" @click=\"completed\">节点评审完成</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"right-panel\">\r\n      <div class=\"result\">\r\n        <div class=\"voting-title\">表决结果</div>\r\n\r\n        <el-input class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { leaderSummaryQuery } from \"@/api/expert/review\";\r\nimport { updateProcess } from \"@/api/evaluation/process\";\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  props: {\r\n    finish: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      columns: {},\r\n      result: [],\r\n      isReadOnly: false,\r\n      votingResults: \"\",\r\n      hasInconsistentScores: false,\r\n      originalTableData: [], // 保存原始表格数据\r\n      originalBusiBidderInfos: [], // 保存原始投标人信息\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 获取表格列标签，如果是专家组长则在姓名后加\"（组长）\"\r\n     * @param {Object} column - 列配置对象\r\n     * @returns {string} 列标签\r\n     */\r\n    getColumnLabel(column) {\r\n      if (column.expertLeader === 1) {\r\n        return `${column.xm}（组长）`;\r\n      }\r\n      return column.xm;\r\n    },\r\n    init() {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        itemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.votingResults = response.data.bjjgsb\r\n\r\n          // 保存原始数据用于评分检查\r\n          this.originalTableData = response.data.tableData;\r\n          this.originalBusiBidderInfos = response.data.busiBidderInfos;\r\n\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n          this.columns = response.data.tableColumns;\r\n\r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n\r\n          // if (response.data.evaluationProcess) {\r\n          //   let psjg = JSON.parse(\r\n          //     response.data.evaluationProcess.evaluationResult\r\n          //   );\r\n          //   this.result = psjg;\r\n          //   if (response.data.evaluationProcess.evaluationState == 2) {\r\n          //     this.isReadOnly = true;\r\n          //   }\r\n          // }\r\n          // if (this.result == null) {\r\n          //   this.result = this.generateResultTable(\r\n          //     response.data.tableColumns,\r\n          //     response.data.busiBidderInfos,\r\n          //     response.data.tableData\r\n          //   );\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 转换函数\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建一个映射，用于将 bidderId 映射到 bidderName\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };\r\n        return acc;\r\n      }, {});\r\n\r\n      // 创建一个映射，用于将 resultId 映射到 itemName\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换数据\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };\r\n\r\n        // 只取 tableColumns 中定义的评估项\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;\r\n          transformedRow[column.xm] = row[itemId] || \"未提交\"; // 默认为 '0' 如果没有找到对应值\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    // 组装评审结果\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, bidder.bidderName])\r\n      );\r\n\r\n      // 检测是否存在评分不一致的情况\r\n      let hasInconsistent = false;\r\n\r\n      // Generate the result table、\r\n      const resultTable = tableData.map((row) => {\r\n        // 检查当前行是否存在评分不一致\r\n        if (this.hasInconsistentScoring(row)) {\r\n          hasInconsistent = true;\r\n        }\r\n\r\n        return {\r\n          bidder: row.gys,\r\n          gys: bidderMap.get(row.gys),\r\n          result: this.getMode(row),\r\n        };\r\n      });\r\n\r\n      // 设置不一致标志\r\n      this.hasInconsistentScores = hasInconsistent;\r\n\r\n      return resultTable;\r\n    },\r\n    /**\r\n     * 获取众数（统计学中的模式值）\r\n     * 用于计算专家评审结果中出现频率最高的评分值\r\n     * 如果评分不一致（所有评分都不相同），则采用专家组长的评分\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象，通常包含多个专家的评分\r\n     * @returns {string|number|null} 返回出现次数最多的值，如果三位专家评分都不一样则返回专家组长的评分\r\n     */\r\n    getMode(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = { ...row };\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      // gys字段通常表示供应商ID，不是评分数据\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      // Object.values()返回对象所有属性值组成的数组\r\n      const values = Object.values(data);\r\n\r\n      // 使用reduce方法统计每个值出现的次数\r\n      // 创建频率映射表：{值: 出现次数}\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        // 如果该值已存在，次数+1；否则初始化为1\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 获取最大出现次数\r\n      const maxFrequency = Math.max(...Object.values(frequencyMap));\r\n\r\n      // 如果最大出现次数为1，说明所有评分都不相同，采用专家组长的评分\r\n      if (maxFrequency === 1) {\r\n        return this.getLeaderScore(row);\r\n      }\r\n\r\n      // 遍历频率映射表，找出出现次数最多的值（众数）\r\n      let mode = null;         // 记录众数值\r\n\r\n      // 遍历频率映射表的每个键值对\r\n      for (const [value, frequency] of Object.entries(frequencyMap)) {\r\n        // 如果当前值的出现次数等于最大次数\r\n        if (frequency === maxFrequency) {\r\n          mode = value;              // 更新众数值\r\n          break; // 找到第一个众数即可\r\n        }\r\n      }\r\n\r\n      // 返回众数，用作该行的最终评审结果\r\n      return mode;\r\n    },\r\n    /**\r\n     * 获取专家组长的评分\r\n     * 从tableColumns中找到expertLeader为1的专家，返回其对应的评分\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {string|number|null} 返回专家组长的评分，如果找不到则返回null\r\n     */\r\n    getLeaderScore(row) {\r\n      // 找到专家组长的列（expertLeader为1）\r\n      const leaderColumn = this.columns.find(column => column.expertLeader === 1);\r\n\r\n      if (!leaderColumn) {\r\n        console.warn('未找到专家组长信息');\r\n        return null;\r\n      }\r\n\r\n      // 返回专家组长对应的评分\r\n      // 使用resultId作为key从row中获取评分\r\n      return row[leaderColumn.resultId] || null;\r\n    },\r\n    /**\r\n     * 检测是否存在评分不一致的情况\r\n     * 当某家供应商的所有专家评分都不相同时，返回true\r\n     *\r\n     * @param {Object} row - 包含评审数据的行对象\r\n     * @returns {boolean} 如果评分不一致返回true，否则返回false\r\n     */\r\n    hasInconsistentScoring(row) {\r\n      // 创建数据副本，避免修改原始数据\r\n      const data = { ...row };\r\n\r\n      // 移除供应商标识字段，因为它不参与众数计算\r\n      delete data.gys;\r\n\r\n      // 提取对象中所有的值（即各专家的评分）\r\n      const values = Object.values(data);\r\n\r\n      // 如果评分数量少于2个，不存在不一致问题\r\n      if (values.length < 2) {\r\n        return false;\r\n      }\r\n\r\n      // 统计每个值出现的次数\r\n      const frequencyMap = values.reduce((acc, value) => {\r\n        acc[value] = (acc[value] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 获取最大出现次数\r\n      const maxFrequency = Math.max(...Object.values(frequencyMap));\r\n\r\n      // 如果最大出现次数为1，说明所有评分都不相同\r\n      return maxFrequency === 1;\r\n    },\r\n    /**\r\n     * 检查所有供应商的评分情况\r\n     * @returns {Object} 返回评分情况统计\r\n     */\r\n    checkAllSuppliersScoring() {\r\n      let allSame = 0; // 所有专家评分相同的供应商数量\r\n      let twoSameOnesDiff = 0; // 两位专家评分相同，一位不同的供应商数量\r\n      let allDifferent = 0; // 三位专家评分都不同的供应商数量\r\n\r\n      // 过滤掉废标的数据\r\n      const validTableData = this.tableData.filter(item => item.isAbandonedBid == 0);\r\n\r\n      validTableData.forEach(row => {\r\n        // 获取原始数据行\r\n        const originalRow = this.getOriginalRowData(row);\r\n        if (!originalRow) return;\r\n\r\n        // 创建数据副本，避免修改原始数据\r\n        const data = { ...originalRow };\r\n        delete data.gys;\r\n\r\n        // 提取对象中所有的值（即各专家的评分）\r\n        const values = Object.values(data);\r\n\r\n        if (values.length < 2) return;\r\n\r\n        // 统计每个值出现的次数\r\n        const frequencyMap = values.reduce((acc, value) => {\r\n          acc[value] = (acc[value] || 0) + 1;\r\n          return acc;\r\n        }, {});\r\n\r\n        const frequencies = Object.values(frequencyMap);\r\n        const maxFrequency = Math.max(...frequencies);\r\n\r\n        if (maxFrequency === values.length) {\r\n          // 所有评分相同\r\n          allSame++;\r\n        } else if (maxFrequency === 1) {\r\n          // 所有评分都不同\r\n          allDifferent++;\r\n        } else {\r\n          // 部分评分相同（两位专家评分相同，一位不同）\r\n          twoSameOnesDiff++;\r\n        }\r\n      });\r\n\r\n      return {\r\n        allSame,\r\n        twoSameOnesDiff,\r\n        allDifferent,\r\n        totalSuppliers: validTableData.length\r\n      };\r\n    },\r\n    /**\r\n     * 根据转换后的行数据获取原始行数据\r\n     * @param {Object} transformedRow - 转换后的行数据\r\n     * @returns {Object|null} 原始行数据\r\n     */\r\n    getOriginalRowData(transformedRow) {\r\n      // 从tableData中找到对应的原始数据\r\n      // 需要从API返回的原始tableData中查找\r\n      const supplierName = transformedRow['供应商名称'];\r\n\r\n      // 从API返回的原始数据中查找\r\n      if (this.originalTableData) {\r\n        return this.originalTableData.find(row => {\r\n          // 通过供应商ID匹配\r\n          const bidderInfo = this.originalBusiBidderInfos.find(info => info.bidderName === supplierName);\r\n          return bidderInfo && row.gys === bidderInfo.bidderId;\r\n        });\r\n      }\r\n      return null;\r\n    },\r\n    // 节点评审完成\r\n    completed() {\r\n      //检查所有的 result 是否为空\r\n      const allResultsNotEmpty = this.result.every(\r\n        (item) => item.result !== \"\"\r\n      );\r\n      // const allResultsNotEmpty = true;\r\n      if (!allResultsNotEmpty) {\r\n        console.log(\"请填写评审结果\");\r\n        this.$message.warning(\"请完善评审结果\");\r\n        return false;\r\n      }\r\n\r\n      // 检查是否存在评分不一致的情况\r\n      if (this.hasInconsistentScores) {\r\n        this.$message.warning(\"评分内容不符合多数服从少数，建议专家组长进行表决\");\r\n        return false;\r\n      }\r\n      // else {\r\n      const evaluationProcessId = JSON.parse(\r\n        localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n      );\r\n      const data = {\r\n        evaluationProcessId: evaluationProcessId.evaluationProcessId,\r\n        evaluationResult: JSON.stringify(this.result),\r\n        evaluationState: 2,\r\n        evaluationResultRemark: this.votingResults,\r\n      };\r\n      updateProcess(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$router.push({\r\n            path: \"/expertInfo\",\r\n            query: {\r\n              projectId: this.$route.query.projectId,\r\n              zjhm: this.$route.query.zjhm,\r\n            },\r\n          });\r\n        } else {\r\n          this.$message.warning();\r\n        }\r\n      });\r\n      // }\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n    getIconClass(value) {\r\n      return value == \"1\" ? \"el-icon-check\" : \"el-icon-circle-close\";\r\n    },\r\n    // 重新评审\r\n    reviewed() {\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,\r\n      };\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确认流标\r\n    confirmflow() {\r\n      if (this.reasonFlowBid == \"\") {\r\n        const data = {\r\n          projectId: this.$route.query.projectId,\r\n          remark: this.reasonFlowBid,\r\n        };\r\n        abortiveTenderNotice(data).then((response) => {\r\n          if (response.code == 200) {\r\n            // 跳转到哪个页面\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      } else {\r\n      }\r\n    },\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n\t\tthis.intervalId = setInterval(()=>{\r\n\t\t\tthis.init();\r\n\t\t},5000)\r\n  },\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n\r\n.left-panel {\r\n  width: 70%;\r\n}\r\n\r\n.right-panel {\r\n  width: 30%;\r\n}\r\n\r\n.title {\r\n  position: relative;\r\n  margin-bottom: 20px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.full-width-table {\r\n  width: 100%;\r\n}\r\n\r\n.result-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.result-container {\r\n  display: flex;\r\n}\r\n\r\n.result-item {\r\n  display: flex;\r\n  margin-right: 30px;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.supplier-name {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  margin-right: 10px;\r\n}\r\n\r\n.review-button {\r\n  background-color: #f5f5f5 !important;\r\n  color: #176adb !important;\r\n}\r\n\r\n.voting-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.warning-message {\r\n  font-size: 14px;\r\n  color: #e6a23c;\r\n  background-color: #fdf6ec;\r\n  border: 1px solid #f5dab1;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  margin-top: 10px;\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 20px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}