<template>
  <div class="three">
    <div class="left-panel">
      <div class="title">商务标评审</div>
      <el-table :data="tableData" border class="full-width-table" :header-cell-style="headStyle" :cell-style="cellStyle">
        <el-table-column prop="供应商名称" width="180">
        </el-table-column>
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.xm" :label="getColumnLabel(item)">
        </el-table-column>
      </el-table>

      <div class="result">
        <div class="result-title">
          评审结果：评分结果以少数服从多数。
          <div v-if="hasInconsistentScores" class="warning-message">
            评分内容不符合少数服从多数，暂先以专家组长的评分为结果，建议专家组长进行表决
          </div>
        </div>
        <div class="result-container">
          <div class="result-item" v-for="(item,index) in result" :key="index">
            <div class="supplier-name">{{ item.gys }}</div>
	          <el-input disabled v-model="item.result" />
          </div>
        </div>
      </div>

      <div class="operation" v-if="!finish">
        <el-button
          v-if="hasInconsistentScores"
          class="item-button review-button"
          @click="reviewed"
        >重新评审</el-button>
        <el-button class="item-button" @click="completed">节点评审完成</el-button>
      </div>
      <div v-else class="operation">
        <el-button class="item-button" @click="back">返回</el-button>
      </div>
    </div>
    <div class="right-panel">
      <div class="result">
        <div class="voting-title">表决结果</div>

        <el-input class="text" type="textarea" :rows="20" placeholder="请输入表决结果" v-model="votingResults">
        </el-input>
      </div>
    </div>
  </div>

</template>

<script>
import { leaderSummaryQuery } from "@/api/expert/review";
import { updateProcess } from "@/api/evaluation/process";
import { reEvaluationTwo } from "@/api/evaluation/expertStatus";

export default {
  props: {
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      result: [],
      isReadOnly: false,
      votingResults: "",
      hasInconsistentScores: false,
      originalTableData: [], // 保存原始表格数据
      originalBusiBidderInfos: [], // 保存原始投标人信息
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
      // 定时器ID，用于清除定时器
      intervalId: null,
    };
  },
  methods: {
    /**
     * 获取表格列标签，如果是专家组长则在姓名后加"（组长）"
     * @param {Object} column - 列配置对象
     * @returns {string} 列标签
     */
    getColumnLabel(column) {
      if (column.expertLeader === 1) {
        return `${column.xm}（组长）`;
      }
      return column.xm;
    },
    init() {
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
      };
      leaderSummaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.votingResults = response.data.bjjgsb

          // 保存原始数据用于评分检查
          this.originalTableData = response.data.tableData;
          this.originalBusiBidderInfos = response.data.busiBidderInfos;

          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          this.columns = response.data.tableColumns;

          this.result = this.generateResultTable(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );

          // if (response.data.evaluationProcess) {
          //   let psjg = JSON.parse(
          //     response.data.evaluationProcess.evaluationResult
          //   );
          //   this.result = psjg;
          //   if (response.data.evaluationProcess.evaluationState == 2) {
          //     this.isReadOnly = true;
          //   }
          // }
          // if (this.result == null) {
          //   this.result = this.generateResultTable(
          //     response.data.tableColumns,
          //     response.data.busiBidderInfos,
          //     response.data.tableData
          //   );
          // }
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 resultId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.resultId] = column.xm;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.resultId;
          transformedRow[column.xm] = row[itemId] || "未提交"; // 默认为 '0' 如果没有找到对应值
        });

        return transformedRow;
      });
    },
    // 组装评审结果
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, bidder.bidderName])
      );

      // 检测是否存在评分不一致的情况
      let hasInconsistent = false;

      // Generate the result table、
      const resultTable = tableData.map((row) => {
        // 检查当前行是否存在评分不一致
        if (this.hasInconsistentScoring(row)) {
          hasInconsistent = true;
        }

        return {
          bidder: row.gys,
          gys: bidderMap.get(row.gys),
          result: this.getMode(row),
        };
      });

      // 设置不一致标志
      this.hasInconsistentScores = hasInconsistent;

      return resultTable;
    },
    /**
     * 获取众数（统计学中的模式值）
     * 用于计算专家评审结果中出现频率最高的评分值
     * 如果评分不一致（所有评分都不相同），则采用专家组长的评分
     *
     * @param {Object} row - 包含评审数据的行对象，通常包含多个专家的评分
     * @returns {string|number|null} 返回出现次数最多的值，如果三位专家评分都不一样则返回专家组长的评分
     */
    getMode(row) {
      // 创建数据副本，避免修改原始数据
      const data = { ...row };

      // 移除供应商标识字段，因为它不参与众数计算
      // gys字段通常表示供应商ID，不是评分数据
      delete data.gys;

      // 提取对象中所有的值（即各专家的评分）
      // Object.values()返回对象所有属性值组成的数组
      const values = Object.values(data);

      // 使用reduce方法统计每个值出现的次数
      // 创建频率映射表：{值: 出现次数}
      const frequencyMap = values.reduce((acc, value) => {
        // 如果该值已存在，次数+1；否则初始化为1
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {});

      // 获取最大出现次数
      const maxFrequency = Math.max(...Object.values(frequencyMap));

      // 如果最大出现次数为1，说明所有评分都不相同，采用专家组长的评分
      if (maxFrequency === 1) {
        return this.getLeaderScore(row);
      }

      // 遍历频率映射表，找出出现次数最多的值（众数）
      let mode = null;         // 记录众数值

      // 遍历频率映射表的每个键值对
      for (const [value, frequency] of Object.entries(frequencyMap)) {
        // 如果当前值的出现次数等于最大次数
        if (frequency === maxFrequency) {
          mode = value;              // 更新众数值
          break; // 找到第一个众数即可
        }
      }

      // 返回众数，用作该行的最终评审结果
      return mode;
    },
    /**
     * 获取专家组长的评分
     * 从tableColumns中找到expertLeader为1的专家，返回其对应的评分
     *
     * @param {Object} row - 包含评审数据的行对象
     * @returns {string|number|null} 返回专家组长的评分，如果找不到则返回null
     */
    getLeaderScore(row) {
      // 找到专家组长的列（expertLeader为1）
      const leaderColumn = this.columns.find(column => column.expertLeader === 1);

      if (!leaderColumn) {
        console.warn('未找到专家组长信息');
        return null;
      }

      // 返回专家组长对应的评分
      // 使用resultId作为key从row中获取评分
      return row[leaderColumn.resultId] || null;
    },
    /**
     * 检测是否存在评分不一致的情况
     * 当某家供应商的所有专家评分都不相同时，返回true
     *
     * @param {Object} row - 包含评审数据的行对象
     * @returns {boolean} 如果评分不一致返回true，否则返回false
     */
    hasInconsistentScoring(row) {
      // 创建数据副本，避免修改原始数据
      const data = { ...row };

      // 移除供应商标识字段，因为它不参与众数计算
      delete data.gys;

      // 提取对象中所有的值（即各专家的评分）
      const values = Object.values(data);

      // 如果评分数量少于2个，不存在不一致问题
      if (values.length < 2) {
        return false;
      }

      // 统计每个值出现的次数
      const frequencyMap = values.reduce((acc, value) => {
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {});

      // 获取最大出现次数
      const maxFrequency = Math.max(...Object.values(frequencyMap));

      // 如果最大出现次数为1，说明所有评分都不相同
      return maxFrequency === 1;
    },
    /**
     * 检查所有供应商的评分情况
     * @returns {Object} 返回评分情况统计
     */
    checkAllSuppliersScoring() {
      let allSame = 0; // 所有专家评分相同的供应商数量
      let twoSameOnesDiff = 0; // 两位专家评分相同，一位不同的供应商数量
      let allDifferent = 0; // 三位专家评分都不同的供应商数量

      // 过滤掉废标的数据
      const validTableData = this.tableData.filter(item => item.isAbandonedBid == 0);

      validTableData.forEach(row => {
        // 获取原始数据行
        const originalRow = this.getOriginalRowData(row);
        if (!originalRow) return;

        // 创建数据副本，避免修改原始数据
        const data = { ...originalRow };
        delete data.gys;

        // 提取对象中所有的值（即各专家的评分）
        const values = Object.values(data);

        if (values.length < 2) return;

        // 统计每个值出现的次数
        const frequencyMap = values.reduce((acc, value) => {
          acc[value] = (acc[value] || 0) + 1;
          return acc;
        }, {});

        const frequencies = Object.values(frequencyMap);
        const maxFrequency = Math.max(...frequencies);

        if (maxFrequency === values.length) {
          // 所有评分相同
          allSame++;
        } else if (maxFrequency === 1) {
          // 所有评分都不同
          allDifferent++;
        } else {
          // 部分评分相同（两位专家评分相同，一位不同）
          twoSameOnesDiff++;
        }
      });

      return {
        allSame,
        twoSameOnesDiff,
        allDifferent,
        totalSuppliers: validTableData.length
      };
    },
    /**
     * 根据转换后的行数据获取原始行数据
     * @param {Object} transformedRow - 转换后的行数据
     * @returns {Object|null} 原始行数据
     */
    getOriginalRowData(transformedRow) {
      // 从tableData中找到对应的原始数据
      // 需要从API返回的原始tableData中查找
      const supplierName = transformedRow['供应商名称'];

      // 从API返回的原始数据中查找
      if (this.originalTableData && this.originalBusiBidderInfos) {
        // 通过供应商名称找到对应的投标人ID
        const bidderInfo = this.originalBusiBidderInfos.find(info => info.bidderName === supplierName);
        if (bidderInfo) {
          // 通过投标人ID在原始表格数据中查找对应行
          return this.originalTableData.find(row => row.gys === bidderInfo.bidderId);
        }
      }
      return null;
    },
    // 节点评审完成
    completed() {
      //检查所有的 result 是否为空
      const allResultsNotEmpty = this.result.every(
        (item) => item.result !== ""
      );
      // const allResultsNotEmpty = true;
      if (!allResultsNotEmpty) {
        console.log("请填写评审结果");
        this.$message.warning("请完善评审结果");
        return false;
      }

      // 检查所有供应商的评分情况
      const scoringStatus = this.checkAllSuppliersScoring();

      let confirmMessage = "";

      if (scoringStatus.allDifferent > 0) {
        // 存在三位专家评分都不同的情况
        confirmMessage = "评分内容不符合少数服从多数，将采用专家组长的分数，是否确认？";
      } else if (scoringStatus.twoSameOnesDiff > 0) {
        // 存在两位专家评分相同，一位不同的情况
        confirmMessage = "评分内容将采用少数服从多数，是否确认？";
      } else if (scoringStatus.allSame === scoringStatus.totalSuppliers) {
        // 所有供应商的专家评分都相同
        confirmMessage = "是否确认评审完成？";
      } else {
        // 混合情况，使用通用提示
        confirmMessage = "是否确认评审完成？";
      }

      // 弹出确认框
      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认后执行提交逻辑
        this.submitEvaluation();
      }).catch(() => {
        // 用户取消，不执行任何操作
        this.$message.info('已取消操作');
      });
    },
    /**
     * 提交评审结果
     */
    submitEvaluation() {
      const evaluationProcessId = JSON.parse(
        localStorage.getItem("evalProjectEvaluationProcess")
      );
      const data = {
        evaluationProcessId: evaluationProcessId.evaluationProcessId,
        evaluationResult: JSON.stringify(this.result),
        evaluationState: 2,
        evaluationResultRemark: this.votingResults,
      };
      updateProcess(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
            },
          });
        } else {
          this.$message.warning(response.msg || '提交失败');
        }
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
    getIconClass(value) {
      return value == "1" ? "el-icon-check" : "el-icon-circle-close";
    },
    // 重新评审
    reviewed() {
      const query = {
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).projectEvaluationId,
        expertResultId: JSON.parse(localStorage.getItem("evalExpertScoreInfo"))
          .expertResultId,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).scoringMethodItemId,
      };
      reEvaluationTwo(query).then((res) => {
        if (res.code == 200) {
          const evaluationProcessId = JSON.parse(
            localStorage.getItem("evalProjectEvaluationProcess")
          );

          reEvaluate(evaluationProcessId.evaluationProcessId).then(
            (response) => {
              if (response.code == 200) {
                // 触发重新评审通知，通知其他专家页面
                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {
                  this.$parent.triggerReEvaluationNotification();
                }
                this.$emit("send", "one");
              } else {
                this.$message.warning(response.msg);
              }
            }
          );
        }
      });
    },
    flowLabel() {
      this.dialogVisible = true;
    },
    // 确认流标
    confirmflow() {
      if (this.reasonFlowBid == "") {
        const data = {
          projectId: this.$route.query.projectId,
          remark: this.reasonFlowBid,
        };
        abortiveTenderNotice(data).then((response) => {
          if (response.code == 200) {
            // 跳转到哪个页面
          } else {
            this.$message.warning(response.msg);
          }
        });
      } else {
      }
    },
    /**
     * 清除定时器的通用方法
     * 在多个生命周期钩子中调用，确保定时器被正确清除
     */
    clearTimer() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        console.log("定时器已清除 - compliance/three.vue");
      }
    },
  },
  mounted() {
    this.init();
		this.intervalId = setInterval(()=>{
			this.init();
		},5000)
  },
  /**
   * 组件销毁前执行
   * 清除定时器，防止内存泄漏
   */
  beforeDestroy() {
    this.clearTimer();
  },

  /**
   * 组件完全销毁后执行
   * 作为额外的安全措施清除定时器
   */
  destroyed() {
    this.clearTimer();
  },

  /**
   * 如果父组件使用了keep-alive，在组件失活时清除定时器
   */
  deactivated() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 20px 40px;
  display: flex;
}

.left-panel {
  width: 70%;
}

.right-panel {
  width: 30%;
}

.title {
  position: relative;
  margin-bottom: 20px;
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 24px;
  color: #333333;
  letter-spacing: 0;
}

.full-width-table {
  width: 100%;
}

.result-title {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 18px;
  color: #333333;
  letter-spacing: 0;
  margin: 20px;
	display: flex;
	align-items: center;
}

.result-container {
  display: flex;
}

.result-item {
  display: flex;
  margin-right: 30px;
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 18px;
  color: #333333;
  letter-spacing: 0;
}

.supplier-name {
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin-right: 10px;
}

.review-button {
  background-color: #f5f5f5 !important;
  color: #176adb !important;
}

.voting-title {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 24px;
  color: #333333;
  letter-spacing: 0;
  margin-bottom: 15px;
}

.warning-message {
  font-size: 14px;
  color: #e6a23c;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 10px;
  font-weight: 400;
  line-height: 1.4;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #176adb;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.result {
  text-align: left;
  margin-left: 20px;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
